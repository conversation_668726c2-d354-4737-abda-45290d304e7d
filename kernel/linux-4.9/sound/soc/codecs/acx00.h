/*
 * sound\soc\codecs\acx00.h
 * (C) Copyright 2012-2016
 * Allwinner Technology Co., Ltd. <www.allwinnertech.com>
 * <PERSON> <huang<PERSON><PERSON>@allwinnertech.com>
 *
 * some simple description for this code
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License as
 * published by the Free Software Foundation; either version 2 of
 * the License, or (at your option) any later version.
 *
 */

#ifndef __ACX00_H_
#define __ACX00_H_

/* ACX00 register offset list */
#define AC_SYS_CLK_CTL		0x2000
#define AC_SYS_MOD_RST		0x2002
#define AC_SYS_SR_CTL		0x2004
/* Left blank */
#define AC_I2S_CTL		0x2100
#define AC_I2S_CLK		0x2102
#define AC_I2S_FMT0		0x2104
/* Left blank */
#define AC_I2S_FMT1		0x2108
/* Left blank */
#define AC_I2S_MIXER_SRC	0x2114
#define AC_I2S_MIXER_GAIN	0x2116
#define AC_I2S_DAC_VOL		0x2118
#define AC_I2S_ADC_VOL		0x211A
/* Left blank */
#define AC_DAC_CTL		0x2200
#define AC_DAC_MIXER_SRC	0x2202
#define AC_DAC_MIXER_GAIN	0x2204
/* Left blank */
#define AC_OUT_MIXER_CTL	0x2220
#define AC_OUT_MIXER_SRC	0x2222
#define AC_LINEOUT_CTL		0x2224
/* Left blank */
#define AC_ADC_CTL		0x2300
/* Left blank */
#define AC_MICBIAS_CTL		0x2310
/* Left blank */
#define AC_ADC_MIC_CTL		0x2320
#define AC_ADC_MIXER_SRC	0x2322
/* Left blank */
#define AC_BIAS_CTL		0x232A
#define AC_ANALOG_PROF_CTL	0x232C
/* Left blank */
#define AC_ADC_DAPL_CTRL	0x2500
#define AC_ADC_DAPR_CTRL	0x2502
#define AC_ADC_DAPLSTA		0x2504
#define AC_ADC_DAPRSTA		0x2506
#define AC_ADC_DAP_LTL		0x2508
#define AC_ADC_DAP_RTL		0x250A
#define AC_ADC_DAP_LHAC		0x250C
#define AC_ADC_DAP_LLAC		0x250E
#define AC_ADC_DAP_RHAC		0x2510
#define AC_ADC_DAP_RLAC		0x2512
#define AC_ADC_DAP_LDT		0x2514
#define AC_ADC_DAP_LAT		0x2516
#define AC_ADC_DAP_RDT		0x2518
#define AC_ADC_DAP_RAT		0x251A
#define AC_ADC_DAP_NTH		0x251C
#define AC_ADC_DAP_LHNAC	0x251E
#define AC_ADC_DAP_LLNAC	0x2520
#define AC_ADC_DAP_RHNAC	0x2522
#define AC_ADC_DAP_RLNAC	0x2524
#define AC_ADC_DAP_HHPFC	0x2526
#define AC_ADC_DAP_LHPFC	0x2528
#define AC_ADC_DAP_OPT		0x252A
/* Left blank */
#define AC_AGC_SEL		0x2480
/* Left blank */
#define AC_ADC_DAPL_CTRL	0x2500
#define AC_ADC_DAPR_CTRL	0x2502
#define AC_ADC_DAPLSTA		0x2504
#define AC_ADC_DAPRSTA		0x2506
#define AC_ADC_DAP_LTL		0x2508
#define AC_ADC_DAP_RTL		0x250A
#define AC_ADC_DAP_LHAC		0x250C
#define AC_ADC_DAP_LLAC		0x250E
#define AC_ADC_DAP_RHAC		0x2510
#define AC_ADC_DAP_RLAC		0x2512
#define AC_ADC_DAP_LDT		0x2514
#define AC_ADC_DAP_LAT		0x2516
#define AC_ADC_DAP_RDT		0x2518
#define AC_ADC_DAP_RAT		0x251A
#define AC_ADC_DAP_NTH		0x251C
#define AC_ADC_DAP_LHNAC	0x251E
#define AC_ADC_DAP_LLNAC	0x2520
#define AC_ADC_DAP_RHNAC	0x2522
#define AC_ADC_DAP_RLNAC	0x2524
#define AC_ADC_DAP_HHPFC	0x2526
#define AC_ADC_DAP_LHPFC	0x2528
#define AC_ADC_DAP_OPT		0x252A
/* Left blank */
#define AC_DRC_SEL		0x2f80
/* Left blank */
#define AC_DRC_CHAN_CTRL	0x3000
#define AC_DRC_HHPFC		0x3002
#define AC_DRC_LHPFC		0x3004
#define AC_DRC_CTRL		0x3006
#define AC_DRC_LPFHAT		0x3008
#define AC_DRC_LPFLAT		0x300A
#define AC_DRC_RPFHAT		0x300C
#define AC_DRC_RPFLAT		0x300E
#define AC_DRC_LPFHRT		0x3010
#define AC_DRC_LPFLRT		0x3012
#define AC_DRC_RPFHRT		0x3014
#define AC_DRC_RPFLRT		0x3016
#define AC_DRC_LRMSHAT		0x3018
#define AC_DRC_LRMSLAT		0x301A
#define AC_DRC_RRMSHAT		0x301C
#define AC_DRC_RRMSLAT		0x301E
#define AC_DRC_HCT		0x3020
#define AC_DRC_LCT		0x3022
#define AC_DRC_HKC		0x3024
#define AC_DRC_LKC		0x3026
#define AC_DRC_HOPC		0x3028
#define AC_DRC_LOPC		0x302A
#define AC_DRC_HLT		0x302C
#define AC_DRC_LLT		0x302E
#define AC_DRC_HKI		0x3030
#define AC_DRC_LKI		0x3032
#define AC_DRC_HOPL		0x3034
#define AC_DRC_LOPL		0x3036
#define AC_DRC_HET		0x3038
#define AC_DRC_LET		0x303A
#define AC_DRC_HKE		0x303C
#define AC_DRC_LKE		0x303E
#define AC_DRC_HOPE		0x3040
#define AC_DRC_LOPE		0x3042
#define AC_DRC_HKN		0x3044
#define AC_DRC_LKN		0x3046
#define AC_DRC_SFHAT		0x3048
#define AC_DRC_SFLAT		0x304A
#define AC_DRC_SFHRT		0x304C
#define AC_DRC_SFLRT		0x304E
#define AC_DRC_MXGHS		0x3050
#define AC_DRC_MXGLS		0x3052
#define AC_DRC_MNGHS		0x3054
#define AC_DRC_MNGLS		0x3056
#define AC_DRC_EPSHC		0x3058
#define AC_DRC_EPSLC		0x305A
#define AC_DRC_OPT		0x305C
#define AC_DRC_HPFHGAIN		0x305E
#define AC_DRC_HPFLGAIN		0x3060
#define AC_DRC_BISTCR		0x3100
#define AC_DRC_BISTST		0x3102

/* AC_SYS_CLK_CTL : 0x2000 */
#define SYS_CLK_I2S		15
#define SYS_CLK_AGC		7
#define SYS_CLK_DRC		6
#define SYS_CLK_ADC		3
#define SYS_CLK_DAC		2

/* AC_SYS_MOD_RST : 0x2002 */
#define MOD_RST_I2S		15
#define MOD_RST_AGC		7
#define MOD_RST_DRC		6
#define MOD_RST_ADC		3
#define MOD_RST_DAC		2

/* AC_SYS_SR_CTL : 0x2004 */
#define SYS_SR_BIT		0
#define SYS_SR_MASK		0xF
#define SYS_SR_BIT_0		0	/* 8000 */
#define SYS_SR_BIT_1		1	/* 11025 */
#define SYS_SR_BIT_2		2	/* 12000 */
#define SYS_SR_BIT_3		3	/* 16000 */
#define SYS_SR_BIT_4		4	/* 22050 */
#define SYS_SR_BIT_5		5	/* 24000 */
#define SYS_SR_BIT_6		6	/* 32000 */
#define SYS_SR_BIT_7		7	/* 44100 */
#define SYS_SR_BIT_8		8	/* 48000 */
#define SYS_SR_BIT_9		9	/* 96000 */
#define SYS_SR_BIT_10		10	/* 192000 */

/* AC_I2S_CTL : 0x2100 */
#define I2S_SDO0_EN		3
#define I2S_TX_EN		2
#define I2S_RX_EN		1
#define I2S_GEN			0

/* AC_I2S_CLK : 0x2102 */
#define I2S_BCLK_OUT		15
#define I2S_LRCK_OUT		14
#define I2S_BLCK_DIV		10
#define I2S_LRCK_PERIOD		0
/* BCLK DIV Define */
#define I2S_BCLK_DIV_MASK	0xF
#define I2S_BCLK_DIV_1		1
#define I2S_BCLK_DIV_2		2
#define I2S_BCLK_DIV_3		3
#define I2S_BCLK_DIV_4		4
#define I2S_BCLK_DIV_5		5
#define I2S_BCLK_DIV_6		6
#define I2S_BCLK_DIV_7		7
#define I2S_BCLK_DIV_8		8
#define I2S_BCLK_DIV_9		9
#define I2S_BCLK_DIV_10		10
#define I2S_BCLK_DIV_11		11
#define I2S_BCLK_DIV_12		12
#define I2S_BCLK_DIV_13		13
#define I2S_BCLK_DIV_14		14
#define I2S_BCLK_DIV_15		15
#define I2S_LRCK_PERIOD_MASK	0x3FF

/* AC_I2S_FMT0 : 0x2104 */
#define I2S_FMT_MODE		14
#define I2S_FMT_TX_OFFSET	10
#define I2S_FMT_RX_OFFSET	8
#define I2S_FMT_SAMPLE		4
#define I2S_FMT_SLOT_WIDTH	1
#define I2S_FMT_LOOP		0

/* AC_I2S_FMT1 : 0x2108 */
#define I2S_FMT_BCLK_POLAR	15
#define I2S_FMT_LRCK_POLAR	14
#define I2S_FMT_EDGE_TRANSFER	13
#define I2S_FMT_RX_MLS		11
#define I2S_FMT_TX_MLS		10
#define I2S_FMT_EXTEND		9
#define I2S_FMT_LRCK_WIDTH	4	/* PCM long/short Frame */
#define I2S_MFT_RX_PDM		2
#define I2S_FMT_TX_PDM		0

/* AC_I2S_MIXER_SRC : 0x2114 */
#define I2S_MIXERL_SRC_DAC	13
#define I2S_MIXERL_SRC_ADC	12
#define I2S_MIXERR_SRC_DAC	9
#define I2S_MIXERR_SRC_ADC	8

/* AC_I2S_MIXER_GAIN : 0x2116 */
#define I2S_MIXERL_GAIN_DAC	13
#define I2S_MIXERL_GAIN_ADC	12
#define I2S_MIXERR_GAIN_DAC	9
#define I2S_MIXERR_GAIN_ADC	8


/* AC_I2S_DAC_VOL : 0x2118 */
#define I2S_DACL_VOL		8
#define I2S_DACR_VOL		0

/* AC_I2S_ADC_VOL : 0x211A */
#define I2S_ADCL_VOL		8
#define I2S_ADCR_VOL		0

/* AC_DAC_CTL : 0x2200 */
#define DAC_CTL_DAC_EN		15
#define DAC_CTL_HPF_EN		14
#define DAC_CTL_FIR			13
#define DAC_CTL_MODQU		8

/* AC_DAC_MIXER_SRC : 0x2202 */
#define DAC_MIXERL_SRC_DAC	13
#define DAC_MIXERL_SRC_ADC	12
#define DAC_MIXERR_SRC_DAC	9
#define DAC_MIXERR_SRC_ADC	8

/* AC_DAC_MIXER_GAIN : 0x2204 */
#define DAC_MIXERL_GAIN_DAC	13
#define DAC_MIXERL_GAIN_ADC	12
#define DAC_MIXERR_GAIN_DAC	9
#define DAC_MIXERR_GAIN_ADC	8

/* AC_OUT_MIXER_CTL : 0x2220 */
#define OUT_MIXER_DACR_EN	15
#define OUT_MIXER_DACL_EN	14
#define OUT_MIXER_RMIX_EN	13
#define OUT_MIXER_LMIX_EN	12
#define OUT_MIXER_LINE_VOL	8
#define OUT_MIXER_MIC1_VOL	4
#define OUT_MIXER_MIC2_VOL	0

/* AC_OUT_MIXER_SRC : 0x2222 */
#define OUT_MIXERR_SRC_MIC1	14
#define OUT_MIXERR_SRC_MIC2	13
#define OUT_MIXERR_SRC_PHPN	12
#define OUT_MIXERR_SRC_PHP	11
#define OUT_MIXERR_SRC_LINER	10
#define OUT_MIXERR_SRC_DACR	9
#define OUT_MIXERR_SRC_DACL	8
#define OUT_MIXERL_SRC_MIC1	6
#define OUT_MIXERL_SRC_MIC2	5
#define OUT_MIXERL_SRC_PHPN	4
#define OUT_MIXERL_SRC_PHN	3
#define OUT_MIXERL_SRC_LINEL	2
#define OUT_MIXERL_SRC_DACL	1
#define OUT_MIXERL_SRC_DACR	0

/* AC_LINEOUT_CTL : 0x2224 */
#define LINEOUT_EN		15
#define LINEL_SRC_EN		14
#define LINER_SRC_EN		13
#define LINEL_SRC		12
#define LINER_SRC		11
/* ramp just skip */
#define LINE_SLOPE_SEL		8
#define LINE_ANTI_TIME		5
#define LINEOUT_VOL			0

/* AC_ADC_CTL : 0x2300 */
#define ADC_EN			15
#define ADC_ENDM		14
#define ADC_FIR			13
#define ADC_DELAY_TIME		2
#define ADC_DELAY_EN		1

/* AC_MICBIAS_CTL : 0x2310 */
#define MMBIAS_EN		15
#define MMBIAS_CHOPPER		14
#define MMBIAS_CHOP_CLK		12
#define MMBIAS_SEL		8
#define ADDA_BIAS_CUR		3

/* AC_ADC_MIC_CTL : 0x2320 */
#define ADCR_EN			15
#define ADCL_EN			14
#define ADC_GAIN		8
#define MIC1_GAIN_EN		7
#define MIC1_BOOST		4
#define MIC2_GAIN_EN		3
#define MIC2_BOOST		0

/* AC_ADC_MIXER_SRC : 0x2322 */
#define ADC_MIXERR_MIC1		14
#define ADC_MIXERR_MIC2		13
#define ADC_MIXERR_PHPN		12
#define ADC_MIXERR_PHP		11
#define ADC_MIXERR_LINER	10
#define ADC_MIXERR_MIXR		9
#define ADC_MIXERR_MIXL		8
#define ADC_MIXERL_MIC1		6
#define ADC_MIXERL_MIC2		5
#define ADC_MIXERL_PHPN		4
#define ADC_MIXERL_PHN		3
#define ADC_MIXERL_LINEL	2
#define ADC_MIXERL_MIXL		1
#define ADC_MIXERL_MIXR		0

/* AC_BIAS_CTL : 0x232A */

/* AC_ANALOG_PROF_CTL : 0x232C */
/* used for current performance measure */

/* AC_DLDO_OSC_CTL : 0x2340 */
/* AC_ALDO_CTL : 0x2342 */
/* used for digital & analog LDO test... etc */

#endif
