/*
 * MAX98504 ALSA SoC Audio driver
 *
 * Copyright 2011 - 2012 Maxim Integrated Products
 * Copyright 2016 Samsung Electronics Co., Ltd.
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 */
#ifndef MAX98504_H_
#define MAX98504_H_

/*
 * MAX98504 Register Definitions
 */
#define MAX98504_INTERRUPT_STATUS		0x01
#define MAX98504_INTERRUPT_FLAGS		0x02
#define MAX98504_INTERRUPT_ENABLE		0x03
#define MAX98504_INTERRUPT_FLAG_CLEARS		0x04
#define MAX98504_GPIO_ENABLE			0x10
#define MAX98504_GPIO_CONFIG			0x11
#define MAX98504_WATCHDOG_ENABLE		0x12
#define MAX98504_WATCHDOG_CONFIG		0x13
#define MAX98504_WATCHDOG_CLEAR			0x14
#define MAX98504_CLOCK_MONITOR_ENABLE		0x15
#define MAX98504_PVDD_BROWNOUT_ENABLE		0x16
#define MAX98504_PVDD_BROWNOUT_CONFIG_1		0x17
#define MAX98504_PVDD_BROWNOUT_CONFIG_2		0x18
#define MAX98504_PVDD_BROWNOUT_CONFIG_3		0x19
#define MAX98504_PVDD_BROWNOUT_CONFIG_4		0x1a
#define MAX98504_PCM_RX_ENABLE			0x20
#define MAX98504_PCM_TX_ENABLE			0x21
#define MAX98504_PCM_TX_HIZ_CONTROL		0x22
#define MAX98504_PCM_TX_CHANNEL_SOURCES		0x23
#define MAX98504_PCM_MODE_CONFIG		0x24
#define MAX98504_PCM_DSP_CONFIG			0x25
#define MAX98504_PCM_CLOCK_SETUP		0x26
#define MAX98504_PCM_SAMPLE_RATE_SETUP		0x27
#define MAX98504_PCM_TO_SPEAKER_MONOMIX		0x28
#define MAX98504_PDM_TX_ENABLE			0x30
#define MAX98504_PDM_TX_HIZ_CONTROL		0x31
#define MAX98504_PDM_TX_CONTROL			0x32
#define MAX98504_PDM_RX_ENABLE			0x33
#define MAX98504_SPEAKER_ENABLE			0x34
#define MAX98504_SPEAKER_SOURCE_SELECT		0x35
#define MAX98504_MEASUREMENT_ENABLE		0x36
#define MAX98504_ANALOGUE_INPUT_GAIN		0x37
#define MAX98504_TEMPERATURE_LIMIT_CONFIG	0x38
#define MAX98504_GLOBAL_ENABLE			0x40
#define MAX98504_SOFTWARE_RESET			0x41
#define MAX98504_REV_ID				0x7fff

#define MAX98504_MAX_REGISTER			0x7fff

#define MAX98504_DAI_ID_PCM			1
#define MAX98504_DAI_ID_PDM			2

#endif /* MAX98504_H_ */
