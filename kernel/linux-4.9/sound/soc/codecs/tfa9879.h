/*
 * tfa9879.h  --  driver for NXP Semiconductors TFA9879
 *
 * Copyright (C) 2014 Axentia Technologies AB
 * Author: <PERSON> <<EMAIL>>
 *
 *  This program is free software; you can redistribute  it and/or modify it
 *  under  the terms of  the GNU General  Public License as published by the
 *  Free Software Foundation;  either version 2 of the  License, or (at your
 *  option) any later version.
 *
 */

#ifndef _TFA9879_H
#define _TFA9879_H

#define TFA9879_DEVICE_CONTROL		0x00
#define TFA9879_SERIAL_INTERFACE_1	0x01
#define TFA9879_PCM_IOM2_FORMAT_1	0x02
#define TFA9879_SERIAL_INTERFACE_2	0x03
#define TFA9879_PCM_IOM2_FORMAT_2	0x04
#define TFA9879_EQUALIZER_A1		0x05
#define TFA9879_EQUALIZER_A2		0x06
#define TFA9879_EQUALIZER_B1		0x07
#define TFA9879_EQUALIZER_B2		0x08
#define TFA9879_EQUALIZER_C1		0x09
#define TFA9879_EQUALIZER_C2		0x0a
#define TFA9879_EQUALIZER_D1		0x0b
#define TFA9879_EQUALIZER_D2		0x0c
#define TFA9879_EQUALIZER_E1		0x0d
#define TFA9879_EQUALIZER_E2		0x0e
#define TFA9879_BYPASS_CONTROL		0x0f
#define TFA9879_DYNAMIC_RANGE_COMPR	0x10
#define TFA9879_BASS_TREBLE		0x11
#define TFA9879_HIGH_PASS_FILTER	0x12
#define TFA9879_VOLUME_CONTROL		0x13
#define TFA9879_MISC_CONTROL		0x14
#define TFA9879_MISC_STATUS		0x15

/* TFA9879_DEVICE_CONTROL */
#define TFA9879_INPUT_SEL_MASK		0x0010
#define TFA9879_INPUT_SEL_SHIFT		4
#define TFA9879_OPMODE_MASK		0x0008
#define TFA9879_OPMODE_SHIFT		3
#define TFA9879_RESET_MASK		0x0002
#define TFA9879_RESET_SHIFT		1
#define TFA9879_POWERUP_MASK		0x0001
#define TFA9879_POWERUP_SHIFT		0

/* TFA9879_SERIAL_INTERFACE */
#define TFA9879_MONO_SEL_MASK		0x0c00
#define TFA9879_MONO_SEL_SHIFT		10
#define TFA9879_MONO_SEL_LEFT		0
#define TFA9879_MONO_SEL_RIGHT		1
#define TFA9879_MONO_SEL_BOTH		2
#define TFA9879_I2S_FS_MASK		0x03c0
#define TFA9879_I2S_FS_SHIFT		6
#define TFA9879_I2S_FS_8000		0
#define TFA9879_I2S_FS_11025		1
#define TFA9879_I2S_FS_12000		2
#define TFA9879_I2S_FS_16000		3
#define TFA9879_I2S_FS_22050		4
#define TFA9879_I2S_FS_24000		5
#define TFA9879_I2S_FS_32000		6
#define TFA9879_I2S_FS_44100		7
#define TFA9879_I2S_FS_48000		8
#define TFA9879_I2S_FS_64000		9
#define TFA9879_I2S_FS_88200		10
#define TFA9879_I2S_FS_96000		11
#define TFA9879_I2S_SET_MASK		0x0038
#define TFA9879_I2S_SET_SHIFT		3
#define TFA9879_I2S_SET_MSB_J_24	2
#define TFA9879_I2S_SET_I2S_24		3
#define TFA9879_I2S_SET_LSB_J_16	4
#define TFA9879_I2S_SET_LSB_J_18	5
#define TFA9879_I2S_SET_LSB_J_20	6
#define TFA9879_I2S_SET_LSB_J_24	7
#define TFA9879_SCK_POL_MASK		0x0004
#define TFA9879_SCK_POL_SHIFT		2
#define TFA9879_SCK_POL_NORMAL		0
#define TFA9879_SCK_POL_INVERSE		1
#define TFA9879_I_MODE_MASK		0x0003
#define TFA9879_I_MODE_SHIFT		0
#define TFA9879_I_MODE_I2S		0
#define TFA9879_I_MODE_PCM_IOM2_SHORT	1
#define TFA9879_I_MODE_PCM_IOM2_LONG	2

/* TFA9879_PCM_IOM2_FORMAT */
#define TFA9879_PCM_FS_MASK		0x0800
#define TFA9879_PCM_FS_SHIFT		11
#define TFA9879_A_LAW_MASK		0x0400
#define TFA9879_A_LAW_SHIFT		10
#define TFA9879_PCM_COMP_MASK		0x0200
#define TFA9879_PCM_COMP_SHIFT		9
#define TFA9879_PCM_DL_MASK		0x0100
#define TFA9879_PCM_DL_SHIFT		8
#define TFA9879_D1_SLOT_MASK		0x00f0
#define TFA9879_D1_SLOT_SHIFT		4
#define TFA9879_D2_SLOT_MASK		0x000f
#define TFA9879_D2_SLOT_SHIFT		0

/* TFA9879_EQUALIZER_X1 */
#define TFA9879_T1_MASK			0x8000
#define TFA9879_T1_SHIFT		15
#define TFA9879_K1M_MASK		0x7ff0
#define TFA9879_K1M_SHIFT		4
#define TFA9879_K1E_MASK		0x000f
#define TFA9879_K1E_SHIFT		0

/* TFA9879_EQUALIZER_X2 */
#define TFA9879_T2_MASK			0x8000
#define TFA9879_T2_SHIFT		15
#define TFA9879_K2M_MASK		0x7800
#define TFA9879_K2M_SHIFT		11
#define TFA9879_K2E_MASK		0x0700
#define TFA9879_K2E_SHIFT		8
#define TFA9879_K0_MASK			0x00fe
#define TFA9879_K0_SHIFT		1
#define TFA9879_S_MASK			0x0001
#define TFA9879_S_SHIFT			0

/* TFA9879_BYPASS_CONTROL */
#define TFA9879_L_OCP_MASK		0x00c0
#define TFA9879_L_OCP_SHIFT		6
#define TFA9879_L_OTP_MASK		0x0030
#define TFA9879_L_OTP_SHIFT		4
#define TFA9879_CLIPCTRL_MASK		0x0008
#define TFA9879_CLIPCTRL_SHIFT		3
#define TFA9879_HPF_BP_MASK		0x0004
#define TFA9879_HPF_BP_SHIFT		2
#define TFA9879_DRC_BP_MASK		0x0002
#define TFA9879_DRC_BP_SHIFT		1
#define TFA9879_EQ_BP_MASK		0x0001
#define TFA9879_EQ_BP_SHIFT		0

/* TFA9879_DYNAMIC_RANGE_COMPR */
#define TFA9879_AT_LVL_MASK		0xf000
#define TFA9879_AT_LVL_SHIFT		12
#define TFA9879_AT_RATE_MASK		0x0f00
#define TFA9879_AT_RATE_SHIFT		8
#define TFA9879_RL_LVL_MASK		0x00f0
#define TFA9879_RL_LVL_SHIFT		4
#define TFA9879_RL_RATE_MASK		0x000f
#define TFA9879_RL_RATE_SHIFT		0

/* TFA9879_BASS_TREBLE */
#define TFA9879_G_TRBLE_MASK		0x3e00
#define TFA9879_G_TRBLE_SHIFT		9
#define TFA9879_F_TRBLE_MASK		0x0180
#define TFA9879_F_TRBLE_SHIFT		7
#define TFA9879_G_BASS_MASK		0x007c
#define TFA9879_G_BASS_SHIFT		2
#define TFA9879_F_BASS_MASK		0x0003
#define TFA9879_F_BASS_SHIFT		0

/* TFA9879_HIGH_PASS_FILTER */
#define TFA9879_HP_CTRL_MASK		0x00ff
#define TFA9879_HP_CTRL_SHIFT		0

/* TFA9879_VOLUME_CONTROL */
#define TFA9879_ZR_CRSS_MASK		0x1000
#define TFA9879_ZR_CRSS_SHIFT		12
#define TFA9879_VOL_MASK		0x00ff
#define TFA9879_VOL_SHIFT		0

/* TFA9879_MISC_CONTROL */
#define TFA9879_DE_PHAS_MASK		0x0c00
#define TFA9879_DE_PHAS_SHIFT		10
#define TFA9879_H_MUTE_MASK		0x0200
#define TFA9879_H_MUTE_SHIFT		9
#define TFA9879_S_MUTE_MASK		0x0100
#define TFA9879_S_MUTE_SHIFT		8
#define TFA9879_P_LIM_MASK		0x00ff
#define TFA9879_P_LIM_SHIFT		0

/* TFA9879_MISC_STATUS */
#define TFA9879_PS_MASK			0x4000
#define TFA9879_PS_SHIFT		14
#define TFA9879_PORA_MASK		0x2000
#define TFA9879_PORA_SHIFT		13
#define TFA9879_AMP_MASK		0x0600
#define TFA9879_AMP_SHIFT		9
#define TFA9879_IBP_2_MASK		0x0100
#define TFA9879_IBP_2_SHIFT		8
#define TFA9879_OFP_2_MASK		0x0080
#define TFA9879_OFP_2_SHIFT		7
#define TFA9879_UFP_2_MASK		0x0040
#define TFA9879_UFP_2_SHIFT		6
#define TFA9879_IBP_1_MASK		0x0020
#define TFA9879_IBP_1_SHIFT		5
#define TFA9879_OFP_1_MASK		0x0010
#define TFA9879_OFP_1_SHIFT		4
#define TFA9879_UFP_1_MASK		0x0008
#define TFA9879_UFP_1_SHIFT		3
#define TFA9879_OCPOKA_MASK		0x0004
#define TFA9879_OCPOKA_SHIFT		2
#define TFA9879_OCPOKB_MASK		0x0002
#define TFA9879_OCPOKB_SHIFT		1
#define TFA9879_OTPOK_MASK		0x0001
#define TFA9879_OTPOK_SHIFT		0

#endif
