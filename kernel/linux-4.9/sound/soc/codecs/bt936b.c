#include <linux/module.h>
#include <linux/platform_device.h>
#include <linux/soundcard.h>
#include <linux/io.h>
#include <sound/soc.h>
#include <linux/slab.h>

#define BT936B_DEBUG			//used at debug mode
#ifdef BT936B_DEBUG
#define bt936bprt printk
#else
#define bt936bprt(format, arg...) do {} while (0)
#endif

#define BT936B_RATES \
	(SNDRV_PCM_RATE_11025 | SNDRV_PCM_RATE_16000 | SNDRV_PCM_RATE_22050 | \
	SNDRV_PCM_RATE_32000 | SNDRV_PCM_RATE_44100 | SNDRV_PCM_RATE_48000 | \
	SNDRV_PCM_RATE_64000 | SNDRV_PCM_RATE_88200 | SNDRV_PCM_RATE_96000 | \
	SNDRV_PCM_RATE_176400 | SNDRV_PCM_RATE_192000 |SNDRV_PCM_RATE_8000)

struct bt936b_i2s_dev {
    struct snd_soc_dai *dai;
};

static int bt936b_i2s_startup(struct snd_pcm_substream *substream,
    struct snd_soc_dai *dai)
{
    return 0;
}

static void bt936b_i2s_shutdown(struct snd_pcm_substream *substream,
      struct snd_soc_dai *dai)
{
// 注意这里返回类型应该是void而不是int
}

static int bt936b_i2s_hw_params(struct snd_pcm_substream *substream,
      struct snd_pcm_hw_params *params,
      struct snd_soc_dai *dai)
{
    printk("[BT936B] bt936b_i2s_hw_params called\n");
    return 0;
}

static int bt936b_set_sysclk(struct snd_soc_dai *dai, int clk_id,unsigned int freq, int dir)
{
    printk("bt936b_set_sysclk called clock id %d freq %u dir %d\n", clk_id, freq, dir);
    return 0;
}

static int bt936b_i2s_trigger(struct snd_pcm_substream *substream, int cmd,
	struct snd_soc_dai *dai)
{
    return 0;
}

static int bt936b_i2s_set_pll(struct snd_soc_dai *dai, int pll_id, int source,
    unsigned int freq_in, unsigned int freq_out)
{
    printk("bt936b_set_pll called pll_id %d source %d freq in %d freq out %d\n", pll_id, source, freq_in, freq_out);
    return 0;    
}

static int bt936b_i2s_set_clkdiv(struct snd_soc_dai *dai, int div_id, int div)
{
    printk("bt936b_set_clkdiv called div id %d div %d\n", div_id, div);
    return 0;    
}

static int bt936b_i2s_hw_free(struct snd_pcm_substream *substream,
    struct snd_soc_dai *dai)
{
    printk("bt936b_hw_free called\n");
    return 0; 
}

static int bt936b_i2s_set_fmt(struct snd_soc_dai *dai, unsigned int fmt)
{
    printk("bt936b_i2s_set_fmt called\n");
    switch (fmt & SND_SOC_DAIFMT_MASTER_MASK) {
        case SND_SOC_DAIFMT_CBM_CFM:	
            printk("bt936b set to work as Master\n");
            break;
        case SND_SOC_DAIFMT_CBS_CFS:	
            printk("bt936b set to work as Slave\n");
            break;
    }
    return 0;    
}

static struct snd_soc_dai_ops bt936b_i2s_dai_ops = {
    .startup = bt936b_i2s_startup,
    .shutdown = bt936b_i2s_shutdown,

    .hw_params = bt936b_i2s_hw_params,
    .hw_free = bt936b_i2s_hw_free,

    .set_pll = bt936b_i2s_set_pll,
	.set_clkdiv = bt936b_i2s_set_clkdiv,

    .set_sysclk = bt936b_set_sysclk,

    .trigger = bt936b_i2s_trigger,
    
    .set_fmt = bt936b_i2s_set_fmt,
};

static struct snd_soc_codec_driver bt936b_i2s_codec_driver = {

};

static struct snd_soc_dai_driver bt936b_i2s_dai[] = {
    {
        .name = "bt936b-i2s-dai",
        .ops = &bt936b_i2s_dai_ops,
        .capture = {
            .channels_min = 1,
            .channels_max = 2,
            .rates = BT936B_RATES,
            .formats = SNDRV_PCM_FMTBIT_S16_LE,
        },
        .playback = {
            .channels_min = 1,
            .channels_max = 2,
            .rates = BT936B_RATES,
            .formats = SNDRV_PCM_FMTBIT_S16_LE,
        },
    }
};


static int bt936b_i2s_probe(struct platform_device *pdev)
{
    struct bt936b_i2s_dev *i2s;
    int ret;
    bt936bprt("[BT936B] bt936b_i2s_probe\n");
    i2s = devm_kzalloc(&pdev->dev, sizeof(*i2s), GFP_KERNEL);
    if (!i2s)
        return -ENOMEM;

    dev_set_drvdata(&pdev->dev, i2s);
    ret = snd_soc_register_codec(&pdev->dev, &bt936b_i2s_codec_driver,
                                         &bt936b_i2s_dai[0], ARRAY_SIZE(bt936b_i2s_dai));
    
    if (ret < 0) {
        printk(KERN_ERR "[BT936B] Failed to register component: %d\n", ret);
    } else {
        printk(KERN_INFO "[BT936B] Component registered successfully with DAI %s\n", 
               bt936b_i2s_dai[0].name);
    }
    
    return ret;
}


static int bt936b_i2s_remove(struct platform_device *pdev)
{
    bt936bprt("[BT936B] bt936b_i2s_remove\n");
    return 0;
}

static const struct of_device_id ark_adac_match[] = {
	{ .compatible = "bt936b,i2s", },
	{},
};

static struct platform_driver bt936b_i2s_driver = {
    .driver = {
        .name = "bt936b-i2s",
        .of_match_table = of_match_ptr(ark_adac_match),
    },
    .probe = bt936b_i2s_probe,
    .remove = bt936b_i2s_remove,
};

module_platform_driver(bt936b_i2s_driver);

MODULE_DESCRIPTION("BT936B I2S ALSA Virtual Sound Card Driver");
MODULE_AUTHOR("TOM");
MODULE_LICENSE("GPL");
