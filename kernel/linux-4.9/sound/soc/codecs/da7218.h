/*
 * da7218.h - DA7218 ALSA SoC Codec Driver
 *
 * Copyright (c) 2015 Dialog Semiconductor
 *
 * Author: <PERSON> <Adam<PERSON>.<EMAIL>>
 *
 * This program is free software; you can redistribute  it and/or modify it
 * under  the terms of  the GNU General  Public License as published by the
 * Free Software Foundation;  either version 2 of the  License, or (at your
 * option) any later version.
 */

#ifndef _DA7218_H
#define _DA7218_H

#include <linux/regmap.h>
#include <linux/regulator/consumer.h>
#include <sound/da7218.h>


/*
 * Registers
 */
#define DA7218_SYSTEM_ACTIVE			0x0
#define DA7218_CIF_CTRL				0x1
#define DA7218_CHIP_ID1				0x4
#define DA7218_CHIP_ID2				0x5
#define DA7218_CHIP_REVISION			0x6
#define DA7218_SPARE1				0x7
#define DA7218_STATUS1				0x8
#define DA7218_SOFT_RESET			0x9
#define DA7218_SR				0xB
#define DA7218_PC_COUNT				0xC
#define DA7218_GAIN_RAMP_CTRL			0xD
#define DA7218_CIF_TIMEOUT_CTRL			0x10
#define DA7218_SYSTEM_MODES_INPUT		0x14
#define DA7218_SYSTEM_MODES_OUTPUT		0x15
#define DA7218_SYSTEM_STATUS			0x16
#define DA7218_IN_1L_FILTER_CTRL		0x18
#define DA7218_IN_1R_FILTER_CTRL		0x19
#define DA7218_IN_2L_FILTER_CTRL		0x1A
#define DA7218_IN_2R_FILTER_CTRL		0x1B
#define DA7218_OUT_1L_FILTER_CTRL		0x20
#define DA7218_OUT_1R_FILTER_CTRL		0x21
#define DA7218_OUT_1_HPF_FILTER_CTRL		0x24
#define DA7218_OUT_1_EQ_12_FILTER_CTRL		0x25
#define DA7218_OUT_1_EQ_34_FILTER_CTRL		0x26
#define DA7218_OUT_1_EQ_5_FILTER_CTRL		0x27
#define DA7218_OUT_1_BIQ_5STAGE_CTRL		0x28
#define DA7218_OUT_1_BIQ_5STAGE_DATA		0x29
#define DA7218_OUT_1_BIQ_5STAGE_ADDR		0x2A
#define DA7218_MIXIN_1_CTRL			0x2C
#define DA7218_MIXIN_1_GAIN			0x2D
#define DA7218_MIXIN_2_CTRL			0x2E
#define DA7218_MIXIN_2_GAIN			0x2F
#define DA7218_ALC_CTRL1			0x30
#define DA7218_ALC_CTRL2			0x31
#define DA7218_ALC_CTRL3			0x32
#define DA7218_ALC_NOISE			0x33
#define DA7218_ALC_TARGET_MIN			0x34
#define DA7218_ALC_TARGET_MAX			0x35
#define DA7218_ALC_GAIN_LIMITS			0x36
#define DA7218_ALC_ANA_GAIN_LIMITS		0x37
#define DA7218_ALC_ANTICLIP_CTRL		0x38
#define DA7218_AGS_ENABLE			0x3C
#define DA7218_AGS_TRIGGER			0x3D
#define DA7218_AGS_ATT_MAX			0x3E
#define DA7218_AGS_TIMEOUT			0x3F
#define DA7218_AGS_ANTICLIP_CTRL		0x40
#define DA7218_CALIB_CTRL			0x44
#define DA7218_CALIB_OFFSET_AUTO_M_1		0x45
#define DA7218_CALIB_OFFSET_AUTO_U_1		0x46
#define DA7218_CALIB_OFFSET_AUTO_M_2		0x47
#define DA7218_CALIB_OFFSET_AUTO_U_2		0x48
#define DA7218_ENV_TRACK_CTRL			0x4C
#define DA7218_LVL_DET_CTRL			0x50
#define DA7218_LVL_DET_LEVEL			0x51
#define DA7218_DGS_TRIGGER			0x54
#define DA7218_DGS_ENABLE			0x55
#define DA7218_DGS_RISE_FALL			0x56
#define DA7218_DGS_SYNC_DELAY			0x57
#define DA7218_DGS_SYNC_DELAY2			0x58
#define DA7218_DGS_SYNC_DELAY3			0x59
#define DA7218_DGS_LEVELS			0x5A
#define DA7218_DGS_GAIN_CTRL			0x5B
#define DA7218_DROUTING_OUTDAI_1L		0x5C
#define DA7218_DMIX_OUTDAI_1L_INFILT_1L_GAIN	0x5D
#define DA7218_DMIX_OUTDAI_1L_INFILT_1R_GAIN	0x5E
#define DA7218_DMIX_OUTDAI_1L_INFILT_2L_GAIN	0x5F
#define DA7218_DMIX_OUTDAI_1L_INFILT_2R_GAIN	0x60
#define DA7218_DMIX_OUTDAI_1L_TONEGEN_GAIN	0x61
#define DA7218_DMIX_OUTDAI_1L_INDAI_1L_GAIN	0x62
#define DA7218_DMIX_OUTDAI_1L_INDAI_1R_GAIN	0x63
#define DA7218_DROUTING_OUTDAI_1R		0x64
#define DA7218_DMIX_OUTDAI_1R_INFILT_1L_GAIN	0x65
#define DA7218_DMIX_OUTDAI_1R_INFILT_1R_GAIN	0x66
#define DA7218_DMIX_OUTDAI_1R_INFILT_2L_GAIN	0x67
#define DA7218_DMIX_OUTDAI_1R_INFILT_2R_GAIN	0x68
#define DA7218_DMIX_OUTDAI_1R_TONEGEN_GAIN	0x69
#define DA7218_DMIX_OUTDAI_1R_INDAI_1L_GAIN	0x6A
#define DA7218_DMIX_OUTDAI_1R_INDAI_1R_GAIN	0x6B
#define DA7218_DROUTING_OUTFILT_1L		0x6C
#define DA7218_DMIX_OUTFILT_1L_INFILT_1L_GAIN	0x6D
#define DA7218_DMIX_OUTFILT_1L_INFILT_1R_GAIN	0x6E
#define DA7218_DMIX_OUTFILT_1L_INFILT_2L_GAIN	0x6F
#define DA7218_DMIX_OUTFILT_1L_INFILT_2R_GAIN	0x70
#define DA7218_DMIX_OUTFILT_1L_TONEGEN_GAIN	0x71
#define DA7218_DMIX_OUTFILT_1L_INDAI_1L_GAIN	0x72
#define DA7218_DMIX_OUTFILT_1L_INDAI_1R_GAIN	0x73
#define DA7218_DROUTING_OUTFILT_1R		0x74
#define DA7218_DMIX_OUTFILT_1R_INFILT_1L_GAIN	0x75
#define DA7218_DMIX_OUTFILT_1R_INFILT_1R_GAIN	0x76
#define DA7218_DMIX_OUTFILT_1R_INFILT_2L_GAIN	0x77
#define DA7218_DMIX_OUTFILT_1R_INFILT_2R_GAIN	0x78
#define DA7218_DMIX_OUTFILT_1R_TONEGEN_GAIN	0x79
#define DA7218_DMIX_OUTFILT_1R_INDAI_1L_GAIN	0x7A
#define DA7218_DMIX_OUTFILT_1R_INDAI_1R_GAIN	0x7B
#define DA7218_DROUTING_OUTDAI_2L		0x7C
#define DA7218_DMIX_OUTDAI_2L_INFILT_1L_GAIN	0x7D
#define DA7218_DMIX_OUTDAI_2L_INFILT_1R_GAIN	0x7E
#define DA7218_DMIX_OUTDAI_2L_INFILT_2L_GAIN	0x7F
#define DA7218_DMIX_OUTDAI_2L_INFILT_2R_GAIN	0x80
#define DA7218_DMIX_OUTDAI_2L_TONEGEN_GAIN	0x81
#define DA7218_DMIX_OUTDAI_2L_INDAI_1L_GAIN	0x82
#define DA7218_DMIX_OUTDAI_2L_INDAI_1R_GAIN	0x83
#define DA7218_DROUTING_OUTDAI_2R		0x84
#define DA7218_DMIX_OUTDAI_2R_INFILT_1L_GAIN	0x85
#define DA7218_DMIX_OUTDAI_2R_INFILT_1R_GAIN	0x86
#define DA7218_DMIX_OUTDAI_2R_INFILT_2L_GAIN	0x87
#define DA7218_DMIX_OUTDAI_2R_INFILT_2R_GAIN	0x88
#define DA7218_DMIX_OUTDAI_2R_TONEGEN_GAIN	0x89
#define DA7218_DMIX_OUTDAI_2R_INDAI_1L_GAIN	0x8A
#define DA7218_DMIX_OUTDAI_2R_INDAI_1R_GAIN	0x8B
#define DA7218_DAI_CTRL				0x8C
#define DA7218_DAI_TDM_CTRL			0x8D
#define DA7218_DAI_OFFSET_LOWER			0x8E
#define DA7218_DAI_OFFSET_UPPER			0x8F
#define DA7218_DAI_CLK_MODE			0x90
#define DA7218_PLL_CTRL				0x91
#define DA7218_PLL_FRAC_TOP			0x92
#define DA7218_PLL_FRAC_BOT			0x93
#define DA7218_PLL_INTEGER			0x94
#define DA7218_PLL_STATUS			0x95
#define DA7218_PLL_REFOSC_CAL			0x98
#define DA7218_DAC_NG_CTRL			0x9C
#define DA7218_DAC_NG_SETUP_TIME		0x9D
#define DA7218_DAC_NG_OFF_THRESH		0x9E
#define DA7218_DAC_NG_ON_THRESH			0x9F
#define DA7218_TONE_GEN_CFG1			0xA0
#define DA7218_TONE_GEN_CFG2			0xA1
#define DA7218_TONE_GEN_FREQ1_L			0xA2
#define DA7218_TONE_GEN_FREQ1_U			0xA3
#define DA7218_TONE_GEN_FREQ2_L			0xA4
#define DA7218_TONE_GEN_FREQ2_U			0xA5
#define DA7218_TONE_GEN_CYCLES			0xA6
#define DA7218_TONE_GEN_ON_PER			0xA7
#define DA7218_TONE_GEN_OFF_PER			0xA8
#define DA7218_CP_CTRL				0xAC
#define DA7218_CP_DELAY				0xAD
#define DA7218_CP_VOL_THRESHOLD1		0xAE
#define DA7218_MIC_1_CTRL			0xB4
#define DA7218_MIC_1_GAIN			0xB5
#define DA7218_MIC_1_SELECT			0xB7
#define DA7218_MIC_2_CTRL			0xB8
#define DA7218_MIC_2_GAIN			0xB9
#define DA7218_MIC_2_SELECT			0xBB
#define DA7218_IN_1_HPF_FILTER_CTRL		0xBC
#define DA7218_IN_2_HPF_FILTER_CTRL		0xBD
#define DA7218_ADC_1_CTRL			0xC0
#define DA7218_ADC_2_CTRL			0xC1
#define DA7218_ADC_MODE				0xC2
#define DA7218_MIXOUT_L_CTRL			0xCC
#define DA7218_MIXOUT_L_GAIN			0xCD
#define DA7218_MIXOUT_R_CTRL			0xCE
#define DA7218_MIXOUT_R_GAIN			0xCF
#define DA7218_HP_L_CTRL			0xD0
#define DA7218_HP_L_GAIN			0xD1
#define DA7218_HP_R_CTRL			0xD2
#define DA7218_HP_R_GAIN			0xD3
#define DA7218_HP_SNGL_CTRL			0xD4
#define DA7218_HP_DIFF_CTRL			0xD5
#define DA7218_HP_DIFF_UNLOCK			0xD7
#define DA7218_HPLDET_JACK			0xD8
#define DA7218_HPLDET_CTRL			0xD9
#define DA7218_HPLDET_TEST			0xDA
#define DA7218_REFERENCES			0xDC
#define DA7218_IO_CTRL				0xE0
#define DA7218_LDO_CTRL				0xE1
#define DA7218_SIDETONE_CTRL			0xE4
#define DA7218_SIDETONE_IN_SELECT		0xE5
#define DA7218_SIDETONE_GAIN			0xE6
#define DA7218_DROUTING_ST_OUTFILT_1L		0xE8
#define DA7218_DROUTING_ST_OUTFILT_1R		0xE9
#define DA7218_SIDETONE_BIQ_3STAGE_DATA		0xEA
#define DA7218_SIDETONE_BIQ_3STAGE_ADDR		0xEB
#define DA7218_EVENT_STATUS			0xEC
#define DA7218_EVENT				0xED
#define DA7218_EVENT_MASK			0xEE
#define DA7218_DMIC_1_CTRL			0xF0
#define DA7218_DMIC_2_CTRL			0xF1
#define DA7218_IN_1L_GAIN			0xF4
#define DA7218_IN_1R_GAIN			0xF5
#define DA7218_IN_2L_GAIN			0xF6
#define DA7218_IN_2R_GAIN			0xF7
#define DA7218_OUT_1L_GAIN			0xF8
#define DA7218_OUT_1R_GAIN			0xF9
#define DA7218_MICBIAS_CTRL			0xFC
#define DA7218_MICBIAS_EN			0xFD


/*
 * Bit Fields
 */

#define DA7218_SWITCH_EN_MAX		0x1

/* DA7218_SYSTEM_ACTIVE = 0x0 */
#define DA7218_SYSTEM_ACTIVE_SHIFT	0
#define DA7218_SYSTEM_ACTIVE_MASK	(0x1 << 0)

/* DA7218_CIF_CTRL = 0x1 */
#define DA7218_CIF_I2C_WRITE_MODE_SHIFT	0
#define DA7218_CIF_I2C_WRITE_MODE_MASK	(0x1 << 0)

/* DA7218_CHIP_ID1 = 0x4 */
#define DA7218_CHIP_ID1_SHIFT	0
#define DA7218_CHIP_ID1_MASK	(0xFF << 0)

/* DA7218_CHIP_ID2 = 0x5 */
#define DA7218_CHIP_ID2_SHIFT	0
#define DA7218_CHIP_ID2_MASK	(0xFF << 0)

/* DA7218_CHIP_REVISION = 0x6 */
#define DA7218_CHIP_MINOR_SHIFT	0
#define DA7218_CHIP_MINOR_MASK	(0xF << 0)
#define DA7218_CHIP_MAJOR_SHIFT	4
#define DA7218_CHIP_MAJOR_MASK	(0xF << 4)

/* DA7218_SPARE1 = 0x7 */
#define DA7218_SPARE1_SHIFT	0
#define DA7218_SPARE1_MASK	(0xFF << 0)

/* DA7218_STATUS1 = 0x8 */
#define DA7218_STATUS_SPARE1_SHIFT	0
#define DA7218_STATUS_SPARE1_MASK	(0xFF << 0)

/* DA7218_SOFT_RESET = 0x9 */
#define DA7218_CIF_REG_SOFT_RESET_SHIFT	7
#define DA7218_CIF_REG_SOFT_RESET_MASK	(0x1 << 7)

/* DA7218_SR = 0xB */
#define DA7218_SR_ADC_SHIFT	0
#define DA7218_SR_ADC_MASK	(0xF << 0)
#define DA7218_SR_DAC_SHIFT	4
#define DA7218_SR_DAC_MASK	(0xF << 4)
#define DA7218_SR_8000		0x01
#define DA7218_SR_11025		0x02
#define DA7218_SR_12000		0x03
#define DA7218_SR_16000		0x05
#define DA7218_SR_22050		0x06
#define DA7218_SR_24000		0x07
#define DA7218_SR_32000		0x09
#define DA7218_SR_44100		0x0A
#define DA7218_SR_48000		0x0B
#define DA7218_SR_88200		0x0E
#define DA7218_SR_96000		0x0F

/* DA7218_PC_COUNT = 0xC */
#define DA7218_PC_FREERUN_SHIFT		0
#define DA7218_PC_FREERUN_MASK		(0x1 << 0)
#define DA7218_PC_RESYNC_AUTO_SHIFT	1
#define DA7218_PC_RESYNC_AUTO_MASK	(0x1 << 1)

/* DA7218_GAIN_RAMP_CTRL = 0xD */
#define DA7218_GAIN_RAMP_RATE_SHIFT	0
#define DA7218_GAIN_RAMP_RATE_MASK	(0x3 << 0)
#define DA7218_GAIN_RAMP_RATE_MAX	4

/* DA7218_CIF_TIMEOUT_CTRL = 0x10 */
#define DA7218_I2C_TIMEOUT_EN_SHIFT	0
#define DA7218_I2C_TIMEOUT_EN_MASK	(0x1 << 0)

/* DA7218_SYSTEM_MODES_INPUT = 0x14 */
#define DA7218_MODE_SUBMIT_SHIFT	0
#define DA7218_MODE_SUBMIT_MASK		(0x1 << 0)
#define DA7218_ADC_MODE_SHIFT		1
#define DA7218_ADC_MODE_MASK		(0x7F << 1)

/* DA7218_SYSTEM_MODES_OUTPUT = 0x15 */
#define DA7218_MODE_SUBMIT_SHIFT	0
#define DA7218_MODE_SUBMIT_MASK		(0x1 << 0)
#define DA7218_DAC_MODE_SHIFT		1
#define DA7218_DAC_MODE_MASK		(0x7F << 1)

/* DA7218_SYSTEM_STATUS = 0x16 */
#define DA7218_SC1_BUSY_SHIFT	0
#define DA7218_SC1_BUSY_MASK	(0x1 << 0)
#define DA7218_SC2_BUSY_SHIFT	1
#define DA7218_SC2_BUSY_MASK	(0x1 << 1)

/* DA7218_IN_1L_FILTER_CTRL = 0x18 */
#define DA7218_IN_1L_RAMP_EN_SHIFT	5
#define DA7218_IN_1L_RAMP_EN_MASK	(0x1 << 5)
#define DA7218_IN_1L_MUTE_EN_SHIFT	6
#define DA7218_IN_1L_MUTE_EN_MASK	(0x1 << 6)
#define DA7218_IN_1L_FILTER_EN_SHIFT	7
#define DA7218_IN_1L_FILTER_EN_MASK	(0x1 << 7)

/* DA7218_IN_1R_FILTER_CTRL = 0x19 */
#define DA7218_IN_1R_RAMP_EN_SHIFT	5
#define DA7218_IN_1R_RAMP_EN_MASK	(0x1 << 5)
#define DA7218_IN_1R_MUTE_EN_SHIFT	6
#define DA7218_IN_1R_MUTE_EN_MASK	(0x1 << 6)
#define DA7218_IN_1R_FILTER_EN_SHIFT	7
#define DA7218_IN_1R_FILTER_EN_MASK	(0x1 << 7)

/* DA7218_IN_2L_FILTER_CTRL = 0x1A */
#define DA7218_IN_2L_RAMP_EN_SHIFT	5
#define DA7218_IN_2L_RAMP_EN_MASK	(0x1 << 5)
#define DA7218_IN_2L_MUTE_EN_SHIFT	6
#define DA7218_IN_2L_MUTE_EN_MASK	(0x1 << 6)
#define DA7218_IN_2L_FILTER_EN_SHIFT	7
#define DA7218_IN_2L_FILTER_EN_MASK	(0x1 << 7)

/* DA7218_IN_2R_FILTER_CTRL = 0x1B */
#define DA7218_IN_2R_RAMP_EN_SHIFT	5
#define DA7218_IN_2R_RAMP_EN_MASK	(0x1 << 5)
#define DA7218_IN_2R_MUTE_EN_SHIFT	6
#define DA7218_IN_2R_MUTE_EN_MASK	(0x1 << 6)
#define DA7218_IN_2R_FILTER_EN_SHIFT	7
#define DA7218_IN_2R_FILTER_EN_MASK	(0x1 << 7)

/* DA7218_OUT_1L_FILTER_CTRL = 0x20 */
#define DA7218_OUT_1L_BIQ_5STAGE_SEL_SHIFT	3
#define DA7218_OUT_1L_BIQ_5STAGE_SEL_MASK	(0x1 << 3)
#define DA7218_OUT_BIQ_5STAGE_SEL_MAX		2
#define DA7218_OUT_1L_SUBRANGE_EN_SHIFT		4
#define DA7218_OUT_1L_SUBRANGE_EN_MASK		(0x1 << 4)
#define DA7218_OUT_1L_RAMP_EN_SHIFT		5
#define DA7218_OUT_1L_RAMP_EN_MASK		(0x1 << 5)
#define DA7218_OUT_1L_MUTE_EN_SHIFT		6
#define DA7218_OUT_1L_MUTE_EN_MASK		(0x1 << 6)
#define DA7218_OUT_1L_FILTER_EN_SHIFT		7
#define DA7218_OUT_1L_FILTER_EN_MASK		(0x1 << 7)

/* DA7218_OUT_1R_FILTER_CTRL = 0x21 */
#define DA7218_OUT_1R_BIQ_5STAGE_SEL_SHIFT	3
#define DA7218_OUT_1R_BIQ_5STAGE_SEL_MASK	(0x1 << 3)
#define DA7218_OUT_1R_SUBRANGE_EN_SHIFT		4
#define DA7218_OUT_1R_SUBRANGE_EN_MASK		(0x1 << 4)
#define DA7218_OUT_1R_RAMP_EN_SHIFT		5
#define DA7218_OUT_1R_RAMP_EN_MASK		(0x1 << 5)
#define DA7218_OUT_1R_MUTE_EN_SHIFT		6
#define DA7218_OUT_1R_MUTE_EN_MASK		(0x1 << 6)
#define DA7218_OUT_1R_FILTER_EN_SHIFT		7
#define DA7218_OUT_1R_FILTER_EN_MASK		(0x1 << 7)

/* DA7218_OUT_1_HPF_FILTER_CTRL = 0x24 */
#define DA7218_OUT_1_VOICE_HPF_CORNER_SHIFT	0
#define DA7218_OUT_1_VOICE_HPF_CORNER_MASK	(0x7 << 0)
#define DA7218_VOICE_HPF_CORNER_MAX		8
#define DA7218_OUT_1_VOICE_EN_SHIFT		3
#define DA7218_OUT_1_VOICE_EN_MASK		(0x1 << 3)
#define DA7218_OUT_1_AUDIO_HPF_CORNER_SHIFT	4
#define DA7218_OUT_1_AUDIO_HPF_CORNER_MASK	(0x3 << 4)
#define DA7218_AUDIO_HPF_CORNER_MAX		4
#define DA7218_OUT_1_HPF_EN_SHIFT		7
#define DA7218_OUT_1_HPF_EN_MASK		(0x1 << 7)
#define DA7218_HPF_MODE_SHIFT			0
#define DA7218_HPF_DISABLED			((0x0 << 3) | (0x0 << 7))
#define DA7218_HPF_AUDIO_EN			((0x0 << 3) | (0x1 << 7))
#define DA7218_HPF_VOICE_EN			((0x1 << 3) | (0x1 << 7))
#define DA7218_HPF_MODE_MASK			((0x1 << 3) | (0x1 << 7))
#define DA7218_HPF_MODE_MAX			3

/* DA7218_OUT_1_EQ_12_FILTER_CTRL = 0x25 */
#define DA7218_OUT_1_EQ_BAND1_SHIFT	0
#define DA7218_OUT_1_EQ_BAND1_MASK	(0xF << 0)
#define DA7218_OUT_EQ_BAND_MAX		0xF
#define DA7218_OUT_1_EQ_BAND2_SHIFT	4
#define DA7218_OUT_1_EQ_BAND2_MASK	(0xF << 4)

/* DA7218_OUT_1_EQ_34_FILTER_CTRL = 0x26 */
#define DA7218_OUT_1_EQ_BAND3_SHIFT	0
#define DA7218_OUT_1_EQ_BAND3_MASK	(0xF << 0)
#define DA7218_OUT_1_EQ_BAND4_SHIFT	4
#define DA7218_OUT_1_EQ_BAND4_MASK	(0xF << 4)

/* DA7218_OUT_1_EQ_5_FILTER_CTRL = 0x27 */
#define DA7218_OUT_1_EQ_BAND5_SHIFT	0
#define DA7218_OUT_1_EQ_BAND5_MASK	(0xF << 0)
#define DA7218_OUT_1_EQ_EN_SHIFT	7
#define DA7218_OUT_1_EQ_EN_MASK		(0x1 << 7)

/* DA7218_OUT_1_BIQ_5STAGE_CTRL = 0x28 */
#define DA7218_OUT_1_BIQ_5STAGE_MUTE_EN_SHIFT	6
#define DA7218_OUT_1_BIQ_5STAGE_MUTE_EN_MASK	(0x1 << 6)
#define DA7218_OUT_1_BIQ_5STAGE_FILTER_EN_SHIFT	7
#define DA7218_OUT_1_BIQ_5STAGE_FILTER_EN_MASK	(0x1 << 7)

/* DA7218_OUT_1_BIQ_5STAGE_DATA = 0x29 */
#define DA7218_OUT_1_BIQ_5STAGE_DATA_SHIFT	0
#define DA7218_OUT_1_BIQ_5STAGE_DATA_MASK	(0xFF << 0)

/* DA7218_OUT_1_BIQ_5STAGE_ADDR = 0x2A */
#define DA7218_OUT_1_BIQ_5STAGE_ADDR_SHIFT	0
#define DA7218_OUT_1_BIQ_5STAGE_ADDR_MASK	(0x3F << 0)
#define DA7218_OUT_1_BIQ_5STAGE_CFG_SIZE	50

/* DA7218_MIXIN_1_CTRL = 0x2C */
#define DA7218_MIXIN_1_MIX_SEL_SHIFT		3
#define DA7218_MIXIN_1_MIX_SEL_MASK		(0x1 << 3)
#define DA7218_MIXIN_1_AMP_ZC_EN_SHIFT		4
#define DA7218_MIXIN_1_AMP_ZC_EN_MASK		(0x1 << 4)
#define DA7218_MIXIN_1_AMP_RAMP_EN_SHIFT	5
#define DA7218_MIXIN_1_AMP_RAMP_EN_MASK		(0x1 << 5)
#define DA7218_MIXIN_1_AMP_MUTE_EN_SHIFT	6
#define DA7218_MIXIN_1_AMP_MUTE_EN_MASK		(0x1 << 6)
#define DA7218_MIXIN_1_AMP_EN_SHIFT		7
#define DA7218_MIXIN_1_AMP_EN_MASK		(0x1 << 7)

/* DA7218_MIXIN_1_GAIN = 0x2D */
#define DA7218_MIXIN_1_AMP_GAIN_SHIFT	0
#define DA7218_MIXIN_1_AMP_GAIN_MASK	(0xF << 0)
#define DA7218_MIXIN_AMP_GAIN_MAX	0xF

/* DA7218_MIXIN_2_CTRL = 0x2E */
#define DA7218_MIXIN_2_MIX_SEL_SHIFT		3
#define DA7218_MIXIN_2_MIX_SEL_MASK		(0x1 << 3)
#define DA7218_MIXIN_2_AMP_ZC_EN_SHIFT		4
#define DA7218_MIXIN_2_AMP_ZC_EN_MASK		(0x1 << 4)
#define DA7218_MIXIN_2_AMP_RAMP_EN_SHIFT	5
#define DA7218_MIXIN_2_AMP_RAMP_EN_MASK		(0x1 << 5)
#define DA7218_MIXIN_2_AMP_MUTE_EN_SHIFT	6
#define DA7218_MIXIN_2_AMP_MUTE_EN_MASK		(0x1 << 6)
#define DA7218_MIXIN_2_AMP_EN_SHIFT		7
#define DA7218_MIXIN_2_AMP_EN_MASK		(0x1 << 7)

/* DA7218_MIXIN_2_GAIN = 0x2F */
#define DA7218_MIXIN_2_AMP_GAIN_SHIFT	0
#define DA7218_MIXIN_2_AMP_GAIN_MASK	(0xF << 0)

/* DA7218_ALC_CTRL1 = 0x30 */
#define DA7218_ALC_EN_SHIFT		0
#define DA7218_ALC_EN_MASK		(0xF << 0)
#define DA7218_ALC_CHAN1_L_EN_SHIFT	0
#define DA7218_ALC_CHAN1_R_EN_SHIFT	1
#define DA7218_ALC_CHAN2_L_EN_SHIFT	2
#define DA7218_ALC_CHAN2_R_EN_SHIFT	3
#define DA7218_ALC_SYNC_MODE_SHIFT	4
#define DA7218_ALC_SYNC_MODE_MASK	(0xF << 4)
#define DA7218_ALC_SYNC_MODE_CH1	(0x1 << 4)
#define DA7218_ALC_SYNC_MODE_CH2	(0x4 << 4)

/* DA7218_ALC_CTRL2 = 0x31 */
#define DA7218_ALC_ATTACK_SHIFT		0
#define DA7218_ALC_ATTACK_MASK		(0xF << 0)
#define DA7218_ALC_ATTACK_MAX		13
#define DA7218_ALC_RELEASE_SHIFT	4
#define DA7218_ALC_RELEASE_MASK		(0xF << 4)
#define DA7218_ALC_RELEASE_MAX		11

/* DA7218_ALC_CTRL3 = 0x32 */
#define DA7218_ALC_HOLD_SHIFT	0
#define DA7218_ALC_HOLD_MASK	(0xF << 0)
#define DA7218_ALC_HOLD_MAX	16

/* DA7218_ALC_NOISE = 0x33 */
#define DA7218_ALC_NOISE_SHIFT		0
#define DA7218_ALC_NOISE_MASK		(0x3F << 0)
#define DA7218_ALC_THRESHOLD_MAX	0x3F

/* DA7218_ALC_TARGET_MIN = 0x34 */
#define DA7218_ALC_THRESHOLD_MIN_SHIFT	0
#define DA7218_ALC_THRESHOLD_MIN_MASK	(0x3F << 0)

/* DA7218_ALC_TARGET_MAX = 0x35 */
#define DA7218_ALC_THRESHOLD_MAX_SHIFT	0
#define DA7218_ALC_THRESHOLD_MAX_MASK	(0x3F << 0)

/* DA7218_ALC_GAIN_LIMITS = 0x36 */
#define DA7218_ALC_ATTEN_MAX_SHIFT	0
#define DA7218_ALC_ATTEN_MAX_MASK	(0xF << 0)
#define DA7218_ALC_ATTEN_GAIN_MAX	0xF
#define DA7218_ALC_GAIN_MAX_SHIFT	4
#define DA7218_ALC_GAIN_MAX_MASK	(0xF << 4)

/* DA7218_ALC_ANA_GAIN_LIMITS = 0x37 */
#define DA7218_ALC_ANA_GAIN_MIN_SHIFT	0
#define DA7218_ALC_ANA_GAIN_MIN_MASK	(0x7 << 0)
#define DA7218_ALC_ANA_GAIN_MIN		0x1
#define DA7218_ALC_ANA_GAIN_MAX		0x7
#define DA7218_ALC_ANA_GAIN_MAX_SHIFT	4
#define DA7218_ALC_ANA_GAIN_MAX_MASK	(0x7 << 4)

/* DA7218_ALC_ANTICLIP_CTRL = 0x38 */
#define DA7218_ALC_ANTICLIP_STEP_SHIFT	0
#define DA7218_ALC_ANTICLIP_STEP_MASK	(0x3 << 0)
#define DA7218_ALC_ANTICLIP_STEP_MAX	4
#define DA7218_ALC_ANTICLIP_EN_SHIFT	7
#define DA7218_ALC_ANTICLIP_EN_MASK	(0x1 << 7)

/* DA7218_AGS_ENABLE = 0x3C */
#define DA7218_AGS_ENABLE_SHIFT		0
#define DA7218_AGS_ENABLE_MASK		(0x3 << 0)
#define DA7218_AGS_ENABLE_CHAN1_SHIFT	0
#define DA7218_AGS_ENABLE_CHAN2_SHIFT	1

/* DA7218_AGS_TRIGGER = 0x3D */
#define DA7218_AGS_TRIGGER_SHIFT	0
#define DA7218_AGS_TRIGGER_MASK		(0xF << 0)
#define DA7218_AGS_TRIGGER_MAX		0xF

/* DA7218_AGS_ATT_MAX = 0x3E */
#define DA7218_AGS_ATT_MAX_SHIFT	0
#define DA7218_AGS_ATT_MAX_MASK		(0x7 << 0)
#define DA7218_AGS_ATT_MAX_MAX		0x7

/* DA7218_AGS_TIMEOUT = 0x3F */
#define DA7218_AGS_TIMEOUT_EN_SHIFT	0
#define DA7218_AGS_TIMEOUT_EN_MASK	(0x1 << 0)

/* DA7218_AGS_ANTICLIP_CTRL = 0x40 */
#define DA7218_AGS_ANTICLIP_EN_SHIFT	7
#define DA7218_AGS_ANTICLIP_EN_MASK	(0x1 << 7)

/* DA7218_CALIB_CTRL = 0x44 */
#define DA7218_CALIB_OFFSET_EN_SHIFT	0
#define DA7218_CALIB_OFFSET_EN_MASK	(0x1 << 0)
#define DA7218_CALIB_AUTO_EN_SHIFT	2
#define DA7218_CALIB_AUTO_EN_MASK	(0x1 << 2)
#define DA7218_CALIB_OVERFLOW_SHIFT	3
#define DA7218_CALIB_OVERFLOW_MASK	(0x1 << 3)

/* DA7218_CALIB_OFFSET_AUTO_M_1 = 0x45 */
#define DA7218_CALIB_OFFSET_AUTO_M_1_SHIFT	0
#define DA7218_CALIB_OFFSET_AUTO_M_1_MASK	(0xFF << 0)

/* DA7218_CALIB_OFFSET_AUTO_U_1 = 0x46 */
#define DA7218_CALIB_OFFSET_AUTO_U_1_SHIFT	0
#define DA7218_CALIB_OFFSET_AUTO_U_1_MASK	(0xF << 0)

/* DA7218_CALIB_OFFSET_AUTO_M_2 = 0x47 */
#define DA7218_CALIB_OFFSET_AUTO_M_2_SHIFT	0
#define DA7218_CALIB_OFFSET_AUTO_M_2_MASK	(0xFF << 0)

/* DA7218_CALIB_OFFSET_AUTO_U_2 = 0x48 */
#define DA7218_CALIB_OFFSET_AUTO_U_2_SHIFT	0
#define DA7218_CALIB_OFFSET_AUTO_U_2_MASK	(0xF << 0)

/* DA7218_ENV_TRACK_CTRL = 0x4C */
#define DA7218_INTEG_ATTACK_SHIFT	0
#define DA7218_INTEG_ATTACK_MASK	(0x3 << 0)
#define DA7218_INTEG_RELEASE_SHIFT	4
#define DA7218_INTEG_RELEASE_MASK	(0x3 << 4)
#define DA7218_INTEG_MAX		4

/* DA7218_LVL_DET_CTRL = 0x50 */
#define DA7218_LVL_DET_EN_SHIFT		0
#define DA7218_LVL_DET_EN_MASK		(0xF << 0)
#define DA7218_LVL_DET_EN_CHAN1L_SHIFT	0
#define DA7218_LVL_DET_EN_CHAN1R_SHIFT	1
#define DA7218_LVL_DET_EN_CHAN2L_SHIFT	2
#define DA7218_LVL_DET_EN_CHAN2R_SHIFT	3

/* DA7218_LVL_DET_LEVEL = 0x51 */
#define DA7218_LVL_DET_LEVEL_SHIFT	0
#define DA7218_LVL_DET_LEVEL_MASK	(0x7F << 0)
#define DA7218_LVL_DET_LEVEL_MAX	0x7F

/* DA7218_DGS_TRIGGER = 0x54 */
#define DA7218_DGS_TRIGGER_LVL_SHIFT	0
#define DA7218_DGS_TRIGGER_LVL_MASK	(0x3F << 0)
#define DA7218_DGS_TRIGGER_MAX		0x3F

/* DA7218_DGS_ENABLE = 0x55 */
#define DA7218_DGS_ENABLE_SHIFT		0
#define DA7218_DGS_ENABLE_MASK		(0x3 << 0)
#define DA7218_DGS_ENABLE_L_SHIFT	0
#define DA7218_DGS_ENABLE_R_SHIFT	1

/* DA7218_DGS_RISE_FALL = 0x56 */
#define DA7218_DGS_RISE_COEFF_SHIFT	0
#define DA7218_DGS_RISE_COEFF_MASK	(0x7 << 0)
#define DA7218_DGS_RISE_COEFF_MAX	7
#define DA7218_DGS_FALL_COEFF_SHIFT	4
#define DA7218_DGS_FALL_COEFF_MASK	(0x7 << 4)
#define DA7218_DGS_FALL_COEFF_MAX	8

/* DA7218_DGS_SYNC_DELAY = 0x57 */
#define DA7218_DGS_SYNC_DELAY_SHIFT	0
#define DA7218_DGS_SYNC_DELAY_MASK	(0xFF << 0)
#define DA7218_DGS_SYNC_DELAY_MAX	0xFF

/* DA7218_DGS_SYNC_DELAY2 = 0x58 */
#define DA7218_DGS_SYNC_DELAY2_SHIFT	0
#define DA7218_DGS_SYNC_DELAY2_MASK	(0xFF << 0)

/* DA7218_DGS_SYNC_DELAY3 = 0x59 */
#define DA7218_DGS_SYNC_DELAY3_SHIFT	0
#define DA7218_DGS_SYNC_DELAY3_MASK	(0x7F << 0)
#define DA7218_DGS_SYNC_DELAY3_MAX	0x7F

/* DA7218_DGS_LEVELS = 0x5A */
#define DA7218_DGS_ANTICLIP_LVL_SHIFT	0
#define DA7218_DGS_ANTICLIP_LVL_MASK	(0x7 << 0)
#define DA7218_DGS_ANTICLIP_LVL_MAX	0x7
#define DA7218_DGS_SIGNAL_LVL_SHIFT	4
#define DA7218_DGS_SIGNAL_LVL_MASK	(0xF << 4)
#define DA7218_DGS_SIGNAL_LVL_MAX	0xF

/* DA7218_DGS_GAIN_CTRL = 0x5B */
#define DA7218_DGS_STEPS_SHIFT		0
#define DA7218_DGS_STEPS_MASK		(0x1F << 0)
#define DA7218_DGS_STEPS_MAX		0x1F
#define DA7218_DGS_RAMP_EN_SHIFT	5
#define DA7218_DGS_RAMP_EN_MASK		(0x1 << 5)
#define DA7218_DGS_SUBR_EN_SHIFT	6
#define DA7218_DGS_SUBR_EN_MASK		(0x1 << 6)

/* DA7218_DROUTING_OUTDAI_1L = 0x5C */
#define DA7218_OUTDAI_1L_SRC_SHIFT	0
#define DA7218_OUTDAI_1L_SRC_MASK	(0x7F << 0)
#define DA7218_DMIX_SRC_INFILT1L	0
#define DA7218_DMIX_SRC_INFILT1R	1
#define DA7218_DMIX_SRC_INFILT2L	2
#define DA7218_DMIX_SRC_INFILT2R	3
#define DA7218_DMIX_SRC_TONEGEN		4
#define DA7218_DMIX_SRC_DAIL		5
#define DA7218_DMIX_SRC_DAIR		6

/* DA7218_DMIX_OUTDAI_1L_INFILT_1L_GAIN = 0x5D */
#define DA7218_OUTDAI_1L_INFILT_1L_GAIN_SHIFT	0
#define DA7218_OUTDAI_1L_INFILT_1L_GAIN_MASK	(0x1F << 0)
#define DA7218_DMIX_GAIN_MAX			0x1F

/* DA7218_DMIX_OUTDAI_1L_INFILT_1R_GAIN = 0x5E */
#define DA7218_OUTDAI_1L_INFILT_1R_GAIN_SHIFT	0
#define DA7218_OUTDAI_1L_INFILT_1R_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_1L_INFILT_2L_GAIN = 0x5F */
#define DA7218_OUTDAI_1L_INFILT_2L_GAIN_SHIFT	0
#define DA7218_OUTDAI_1L_INFILT_2L_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_1L_INFILT_2R_GAIN = 0x60 */
#define DA7218_OUTDAI_1L_INFILT_2R_GAIN_SHIFT	0
#define DA7218_OUTDAI_1L_INFILT_2R_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_1L_TONEGEN_GAIN = 0x61 */
#define DA7218_OUTDAI_1L_TONEGEN_GAIN_SHIFT	0
#define DA7218_OUTDAI_1L_TONEGEN_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_1L_INDAI_1L_GAIN = 0x62 */
#define DA7218_OUTDAI_1L_INDAI_1L_GAIN_SHIFT	0
#define DA7218_OUTDAI_1L_INDAI_1L_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_1L_INDAI_1R_GAIN = 0x63 */
#define DA7218_OUTDAI_1L_INDAI_1R_GAIN_SHIFT	0
#define DA7218_OUTDAI_1L_INDAI_1R_GAIN_MASK	(0x1F << 0)

/* DA7218_DROUTING_OUTDAI_1R = 0x64 */
#define DA7218_OUTDAI_1R_SRC_SHIFT	0
#define DA7218_OUTDAI_1R_SRC_MASK	(0x7F << 0)

/* DA7218_DMIX_OUTDAI_1R_INFILT_1L_GAIN = 0x65 */
#define DA7218_OUTDAI_1R_INFILT_1L_GAIN_SHIFT	0
#define DA7218_OUTDAI_1R_INFILT_1L_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_1R_INFILT_1R_GAIN = 0x66 */
#define DA7218_OUTDAI_1R_INFILT_1R_GAIN_SHIFT	0
#define DA7218_OUTDAI_1R_INFILT_1R_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_1R_INFILT_2L_GAIN = 0x67 */
#define DA7218_OUTDAI_1R_INFILT_2L_GAIN_SHIFT	0
#define DA7218_OUTDAI_1R_INFILT_2L_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_1R_INFILT_2R_GAIN = 0x68 */
#define DA7218_OUTDAI_1R_INFILT_2R_GAIN_SHIFT	0
#define DA7218_OUTDAI_1R_INFILT_2R_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_1R_TONEGEN_GAIN = 0x69 */
#define DA7218_OUTDAI_1R_TONEGEN_GAIN_SHIFT	0
#define DA7218_OUTDAI_1R_TONEGEN_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_1R_INDAI_1L_GAIN = 0x6A */
#define DA7218_OUTDAI_1R_INDAI_1L_GAIN_SHIFT	0
#define DA7218_OUTDAI_1R_INDAI_1L_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_1R_INDAI_1R_GAIN = 0x6B */
#define DA7218_OUTDAI_1R_INDAI_1R_GAIN_SHIFT	0
#define DA7218_OUTDAI_1R_INDAI_1R_GAIN_MASK	(0x1F << 0)

/* DA7218_DROUTING_OUTFILT_1L = 0x6C */
#define DA7218_OUTFILT_1L_SRC_SHIFT	0
#define DA7218_OUTFILT_1L_SRC_MASK	(0x7F << 0)

/* DA7218_DMIX_OUTFILT_1L_INFILT_1L_GAIN = 0x6D */
#define DA7218_OUTFILT_1L_INFILT_1L_GAIN_SHIFT	0
#define DA7218_OUTFILT_1L_INFILT_1L_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTFILT_1L_INFILT_1R_GAIN = 0x6E */
#define DA7218_OUTFILT_1L_INFILT_1R_GAIN_SHIFT	0
#define DA7218_OUTFILT_1L_INFILT_1R_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTFILT_1L_INFILT_2L_GAIN = 0x6F */
#define DA7218_OUTFILT_1L_INFILT_2L_GAIN_SHIFT	0
#define DA7218_OUTFILT_1L_INFILT_2L_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTFILT_1L_INFILT_2R_GAIN = 0x70 */
#define DA7218_OUTFILT_1L_INFILT_2R_GAIN_SHIFT	0
#define DA7218_OUTFILT_1L_INFILT_2R_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTFILT_1L_TONEGEN_GAIN = 0x71 */
#define DA7218_OUTFILT_1L_TONEGEN_GAIN_SHIFT	0
#define DA7218_OUTFILT_1L_TONEGEN_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTFILT_1L_INDAI_1L_GAIN = 0x72 */
#define DA7218_OUTFILT_1L_INDAI_1L_GAIN_SHIFT	0
#define DA7218_OUTFILT_1L_INDAI_1L_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTFILT_1L_INDAI_1R_GAIN = 0x73 */
#define DA7218_OUTFILT_1L_INDAI_1R_GAIN_SHIFT	0
#define DA7218_OUTFILT_1L_INDAI_1R_GAIN_MASK	(0x1F << 0)

/* DA7218_DROUTING_OUTFILT_1R = 0x74 */
#define DA7218_OUTFILT_1R_SRC_SHIFT	0
#define DA7218_OUTFILT_1R_SRC_MASK	(0x7F << 0)

/* DA7218_DMIX_OUTFILT_1R_INFILT_1L_GAIN = 0x75 */
#define DA7218_OUTFILT_1R_INFILT_1L_GAIN_SHIFT	0
#define DA7218_OUTFILT_1R_INFILT_1L_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTFILT_1R_INFILT_1R_GAIN = 0x76 */
#define DA7218_OUTFILT_1R_INFILT_1R_GAIN_SHIFT	0
#define DA7218_OUTFILT_1R_INFILT_1R_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTFILT_1R_INFILT_2L_GAIN = 0x77 */
#define DA7218_OUTFILT_1R_INFILT_2L_GAIN_SHIFT	0
#define DA7218_OUTFILT_1R_INFILT_2L_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTFILT_1R_INFILT_2R_GAIN = 0x78 */
#define DA7218_OUTFILT_1R_INFILT_2R_GAIN_SHIFT	0
#define DA7218_OUTFILT_1R_INFILT_2R_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTFILT_1R_TONEGEN_GAIN = 0x79 */
#define DA7218_OUTFILT_1R_TONEGEN_GAIN_SHIFT	0
#define DA7218_OUTFILT_1R_TONEGEN_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTFILT_1R_INDAI_1L_GAIN = 0x7A */
#define DA7218_OUTFILT_1R_INDAI_1L_GAIN_SHIFT	0
#define DA7218_OUTFILT_1R_INDAI_1L_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTFILT_1R_INDAI_1R_GAIN = 0x7B */
#define DA7218_OUTFILT_1R_INDAI_1R_GAIN_SHIFT	0
#define DA7218_OUTFILT_1R_INDAI_1R_GAIN_MASK	(0x1F << 0)

/* DA7218_DROUTING_OUTDAI_2L = 0x7C */
#define DA7218_OUTDAI_2L_SRC_SHIFT	0
#define DA7218_OUTDAI_2L_SRC_MASK	(0x7F << 0)

/* DA7218_DMIX_OUTDAI_2L_INFILT_1L_GAIN = 0x7D */
#define DA7218_OUTDAI_2L_INFILT_1L_GAIN_SHIFT	0
#define DA7218_OUTDAI_2L_INFILT_1L_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_2L_INFILT_1R_GAIN = 0x7E */
#define DA7218_OUTDAI_2L_INFILT_1R_GAIN_SHIFT	0
#define DA7218_OUTDAI_2L_INFILT_1R_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_2L_INFILT_2L_GAIN = 0x7F */
#define DA7218_OUTDAI_2L_INFILT_2L_GAIN_SHIFT	0
#define DA7218_OUTDAI_2L_INFILT_2L_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_2L_INFILT_2R_GAIN = 0x80 */
#define DA7218_OUTDAI_2L_INFILT_2R_GAIN_SHIFT	0
#define DA7218_OUTDAI_2L_INFILT_2R_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_2L_TONEGEN_GAIN = 0x81 */
#define DA7218_OUTDAI_2L_TONEGEN_GAIN_SHIFT	0
#define DA7218_OUTDAI_2L_TONEGEN_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_2L_INDAI_1L_GAIN = 0x82 */
#define DA7218_OUTDAI_2L_INDAI_1L_GAIN_SHIFT	0
#define DA7218_OUTDAI_2L_INDAI_1L_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_2L_INDAI_1R_GAIN = 0x83 */
#define DA7218_OUTDAI_2L_INDAI_1R_GAIN_SHIFT	0
#define DA7218_OUTDAI_2L_INDAI_1R_GAIN_MASK	(0x1F << 0)

/* DA7218_DROUTING_OUTDAI_2R = 0x84 */
#define DA7218_OUTDAI_2R_SRC_SHIFT	0
#define DA7218_OUTDAI_2R_SRC_MASK	(0x7F << 0)

/* DA7218_DMIX_OUTDAI_2R_INFILT_1L_GAIN = 0x85 */
#define DA7218_OUTDAI_2R_INFILT_1L_GAIN_SHIFT	0
#define DA7218_OUTDAI_2R_INFILT_1L_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_2R_INFILT_1R_GAIN = 0x86 */
#define DA7218_OUTDAI_2R_INFILT_1R_GAIN_SHIFT	0
#define DA7218_OUTDAI_2R_INFILT_1R_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_2R_INFILT_2L_GAIN = 0x87 */
#define DA7218_OUTDAI_2R_INFILT_2L_GAIN_SHIFT	0
#define DA7218_OUTDAI_2R_INFILT_2L_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_2R_INFILT_2R_GAIN = 0x88 */
#define DA7218_OUTDAI_2R_INFILT_2R_GAIN_SHIFT	0
#define DA7218_OUTDAI_2R_INFILT_2R_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_2R_TONEGEN_GAIN = 0x89 */
#define DA7218_OUTDAI_2R_TONEGEN_GAIN_SHIFT	0
#define DA7218_OUTDAI_2R_TONEGEN_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_2R_INDAI_1L_GAIN = 0x8A */
#define DA7218_OUTDAI_2R_INDAI_1L_GAIN_SHIFT	0
#define DA7218_OUTDAI_2R_INDAI_1L_GAIN_MASK	(0x1F << 0)

/* DA7218_DMIX_OUTDAI_2R_INDAI_1R_GAIN = 0x8B */
#define DA7218_OUTDAI_2R_INDAI_1R_GAIN_SHIFT	0
#define DA7218_OUTDAI_2R_INDAI_1R_GAIN_MASK	(0x1F << 0)

/* DA7218_DAI_CTRL = 0x8C */
#define DA7218_DAI_FORMAT_SHIFT		0
#define DA7218_DAI_FORMAT_MASK		(0x3 << 0)
#define DA7218_DAI_FORMAT_I2S		(0x0 << 0)
#define DA7218_DAI_FORMAT_LEFT_J	(0x1 << 0)
#define DA7218_DAI_FORMAT_RIGHT_J	(0x2 << 0)
#define DA7218_DAI_FORMAT_DSP		(0x3 << 0)
#define DA7218_DAI_WORD_LENGTH_SHIFT	2
#define DA7218_DAI_WORD_LENGTH_MASK	(0x3 << 2)
#define DA7218_DAI_WORD_LENGTH_S16_LE	(0x0 << 2)
#define DA7218_DAI_WORD_LENGTH_S20_LE	(0x1 << 2)
#define DA7218_DAI_WORD_LENGTH_S24_LE	(0x2 << 2)
#define DA7218_DAI_WORD_LENGTH_S32_LE	(0x3 << 2)
#define DA7218_DAI_CH_NUM_SHIFT		4
#define DA7218_DAI_CH_NUM_MASK		(0x7 << 4)
#define DA7218_DAI_CH_NUM_MAX		4
#define DA7218_DAI_EN_SHIFT		7
#define DA7218_DAI_EN_MASK		(0x1 << 7)

/* DA7218_DAI_TDM_CTRL = 0x8D */
#define DA7218_DAI_TDM_CH_EN_SHIFT	0
#define DA7218_DAI_TDM_CH_EN_MASK	(0xF << 0)
#define DA7218_DAI_TDM_MAX_SLOTS	4
#define DA7218_DAI_OE_SHIFT		6
#define DA7218_DAI_OE_MASK		(0x1 << 6)
#define DA7218_DAI_TDM_MODE_EN_SHIFT	7
#define DA7218_DAI_TDM_MODE_EN_MASK	(0x1 << 7)

/* DA7218_DAI_OFFSET_LOWER = 0x8E */
#define DA7218_DAI_OFFSET_LOWER_SHIFT	0
#define DA7218_DAI_OFFSET_LOWER_MASK	(0xFF << 0)

/* DA7218_DAI_OFFSET_UPPER = 0x8F */
#define DA7218_DAI_OFFSET_UPPER_SHIFT	0
#define DA7218_DAI_OFFSET_UPPER_MASK	(0x7 << 0)

/* DA7218_DAI_CLK_MODE = 0x90 */
#define DA7218_DAI_BCLKS_PER_WCLK_SHIFT	0
#define DA7218_DAI_BCLKS_PER_WCLK_MASK	(0x3 << 0)
#define DA7218_DAI_BCLKS_PER_WCLK_32	(0x0 << 0)
#define DA7218_DAI_BCLKS_PER_WCLK_64	(0x1 << 0)
#define DA7218_DAI_BCLKS_PER_WCLK_128	(0x2 << 0)
#define DA7218_DAI_BCLKS_PER_WCLK_256	(0x3 << 0)
#define DA7218_DAI_CLK_POL_SHIFT	2
#define DA7218_DAI_CLK_POL_MASK		(0x1 << 2)
#define DA7218_DAI_CLK_POL_INV		(0x1 << 2)
#define DA7218_DAI_WCLK_POL_SHIFT	3
#define DA7218_DAI_WCLK_POL_MASK	(0x1 << 3)
#define DA7218_DAI_WCLK_POL_INV		(0x1 << 3)
#define DA7218_DAI_WCLK_TRI_STATE_SHIFT	4
#define DA7218_DAI_WCLK_TRI_STATE_MASK	(0x1 << 4)
#define DA7218_DAI_CLK_EN_SHIFT		7
#define DA7218_DAI_CLK_EN_MASK		(0x1 << 7)

/* DA7218_PLL_CTRL = 0x91 */
#define DA7218_PLL_INDIV_SHIFT		0
#define DA7218_PLL_INDIV_MASK		(0x7 << 0)
#define DA7218_PLL_INDIV_2_TO_4_5_MHZ	(0x0 << 0)
#define DA7218_PLL_INDIV_4_5_TO_9_MHZ	(0x1 << 0)
#define DA7218_PLL_INDIV_9_TO_18_MHZ	(0x2 << 0)
#define DA7218_PLL_INDIV_18_TO_36_MHZ	(0x3 << 0)
#define DA7218_PLL_INDIV_36_TO_54_MHZ	(0x4 << 0)
#define DA7218_PLL_MCLK_SQR_EN_SHIFT	4
#define DA7218_PLL_MCLK_SQR_EN_MASK	(0x1 << 4)
#define DA7218_PLL_MODE_SHIFT		6
#define DA7218_PLL_MODE_MASK		(0x3 << 6)
#define DA7218_PLL_MODE_BYPASS		(0x0 << 6)
#define DA7218_PLL_MODE_NORMAL		(0x1 << 6)
#define DA7218_PLL_MODE_SRM		(0x2 << 6)

/* DA7218_PLL_FRAC_TOP = 0x92 */
#define DA7218_PLL_FBDIV_FRAC_TOP_SHIFT	0
#define DA7218_PLL_FBDIV_FRAC_TOP_MASK	(0x1F << 0)

/* DA7218_PLL_FRAC_BOT = 0x93 */
#define DA7218_PLL_FBDIV_FRAC_BOT_SHIFT	0
#define DA7218_PLL_FBDIV_FRAC_BOT_MASK	(0xFF << 0)

/* DA7218_PLL_INTEGER = 0x94 */
#define DA7218_PLL_FBDIV_INTEGER_SHIFT	0
#define DA7218_PLL_FBDIV_INTEGER_MASK	(0x7F << 0)

/* DA7218_PLL_STATUS = 0x95 */
#define DA7218_PLL_SRM_STATUS_SHIFT	0
#define DA7218_PLL_SRM_STATUS_MASK	(0xFF << 0)
#define DA7218_PLL_SRM_STATUS_SRM_LOCK	(0x1 << 7)

/* DA7218_PLL_REFOSC_CAL = 0x98 */
#define DA7218_PLL_REFOSC_CAL_CTRL_SHIFT	0
#define DA7218_PLL_REFOSC_CAL_CTRL_MASK		(0x1F << 0)
#define DA7218_PLL_REFOSC_CAL_START_SHIFT	6
#define DA7218_PLL_REFOSC_CAL_START_MASK	(0x1 << 6)
#define DA7218_PLL_REFOSC_CAL_EN_SHIFT		7
#define DA7218_PLL_REFOSC_CAL_EN_MASK		(0x1 << 7)

/* DA7218_DAC_NG_CTRL = 0x9C */
#define DA7218_DAC_NG_EN_SHIFT	7
#define DA7218_DAC_NG_EN_MASK	(0x1 << 7)

/* DA7218_DAC_NG_SETUP_TIME = 0x9D */
#define DA7218_DAC_NG_SETUP_TIME_SHIFT	0
#define DA7218_DAC_NG_SETUP_TIME_MASK	(0x3 << 0)
#define DA7218_DAC_NG_SETUP_TIME_MAX	4
#define DA7218_DAC_NG_RAMPUP_RATE_SHIFT	2
#define DA7218_DAC_NG_RAMPUP_RATE_MASK	(0x1 << 2)
#define DA7218_DAC_NG_RAMPUP_RATE_MAX	2
#define DA7218_DAC_NG_RAMPDN_RATE_SHIFT	3
#define DA7218_DAC_NG_RAMPDN_RATE_MASK	(0x1 << 3)
#define DA7218_DAC_NG_RAMPDN_RATE_MAX	2

/* DA7218_DAC_NG_OFF_THRESH = 0x9E */
#define DA7218_DAC_NG_OFF_THRESHOLD_SHIFT	0
#define DA7218_DAC_NG_OFF_THRESHOLD_MASK	(0x7 << 0)
#define DA7218_DAC_NG_THRESHOLD_MAX		0x7

/* DA7218_DAC_NG_ON_THRESH = 0x9F */
#define DA7218_DAC_NG_ON_THRESHOLD_SHIFT	0
#define DA7218_DAC_NG_ON_THRESHOLD_MASK		(0x7 << 0)

/* DA7218_TONE_GEN_CFG1 = 0xA0 */
#define DA7218_DTMF_REG_SHIFT		0
#define DA7218_DTMF_REG_MASK		(0xF << 0)
#define DA7218_DTMF_REG_MAX		16
#define DA7218_DTMF_EN_SHIFT		4
#define DA7218_DTMF_EN_MASK		(0x1 << 4)
#define DA7218_START_STOPN_SHIFT	7
#define DA7218_START_STOPN_MASK		(0x1 << 7)

/* DA7218_TONE_GEN_CFG2 = 0xA1 */
#define DA7218_SWG_SEL_SHIFT	0
#define DA7218_SWG_SEL_MASK	(0x3 << 0)
#define DA7218_SWG_SEL_MAX	4

/* DA7218_TONE_GEN_FREQ1_L = 0xA2 */
#define DA7218_FREQ1_L_SHIFT	0
#define DA7218_FREQ1_L_MASK	(0xFF << 0)
#define DA7218_FREQ_MAX		0xFFFF

/* DA7218_TONE_GEN_FREQ1_U = 0xA3 */
#define DA7218_FREQ1_U_SHIFT	0
#define DA7218_FREQ1_U_MASK	(0xFF << 0)

/* DA7218_TONE_GEN_FREQ2_L = 0xA4 */
#define DA7218_FREQ2_L_SHIFT	0
#define DA7218_FREQ2_L_MASK	(0xFF << 0)

/* DA7218_TONE_GEN_FREQ2_U = 0xA5 */
#define DA7218_FREQ2_U_SHIFT	0
#define DA7218_FREQ2_U_MASK	(0xFF << 0)

/* DA7218_TONE_GEN_CYCLES = 0xA6 */
#define DA7218_BEEP_CYCLES_SHIFT	0
#define DA7218_BEEP_CYCLES_MASK		(0x7 << 0)

/* DA7218_TONE_GEN_ON_PER = 0xA7 */
#define DA7218_BEEP_ON_PER_SHIFT	0
#define DA7218_BEEP_ON_PER_MASK		(0x3F << 0)

/* DA7218_TONE_GEN_OFF_PER = 0xA8 */
#define DA7218_BEEP_OFF_PER_SHIFT	0
#define DA7218_BEEP_OFF_PER_MASK	(0x3F << 0)
#define DA7218_BEEP_ON_OFF_MAX		0x3F

/* DA7218_CP_CTRL = 0xAC */
#define DA7218_CP_MOD_SHIFT			2
#define DA7218_CP_MOD_MASK			(0x3 << 2)
#define DA7218_CP_MCHANGE_SHIFT			4
#define DA7218_CP_MCHANGE_MASK			(0x3 << 4)
#define DA7218_CP_MCHANGE_REL_MASK		0x3
#define DA7218_CP_MCHANGE_MAX			3
#define DA7218_CP_MCHANGE_LARGEST_VOL		0x1
#define DA7218_CP_MCHANGE_DAC_VOL		0x2
#define DA7218_CP_MCHANGE_SIG_MAG		0x3
#define DA7218_CP_SMALL_SWITCH_FREQ_EN_SHIFT	6
#define DA7218_CP_SMALL_SWITCH_FREQ_EN_MASK	(0x1 << 6)
#define DA7218_CP_EN_SHIFT			7
#define DA7218_CP_EN_MASK			(0x1 << 7)

/* DA7218_CP_DELAY = 0xAD */
#define DA7218_CP_FCONTROL_SHIFT	0
#define DA7218_CP_FCONTROL_MASK		(0x7 << 0)
#define DA7218_CP_FCONTROL_MAX		6
#define DA7218_CP_TAU_DELAY_SHIFT	3
#define DA7218_CP_TAU_DELAY_MASK	(0x7 << 3)
#define DA7218_CP_TAU_DELAY_MAX		8

/* DA7218_CP_VOL_THRESHOLD1 = 0xAE */
#define DA7218_CP_THRESH_VDD2_SHIFT	0
#define DA7218_CP_THRESH_VDD2_MASK	(0x3F << 0)
#define DA7218_CP_THRESH_VDD2_MAX	0x3F

/* DA7218_MIC_1_CTRL = 0xB4 */
#define DA7218_MIC_1_AMP_MUTE_EN_SHIFT	6
#define DA7218_MIC_1_AMP_MUTE_EN_MASK	(0x1 << 6)
#define DA7218_MIC_1_AMP_EN_SHIFT	7
#define DA7218_MIC_1_AMP_EN_MASK	(0x1 << 7)

/* DA7218_MIC_1_GAIN = 0xB5 */
#define DA7218_MIC_1_AMP_GAIN_SHIFT	0
#define DA7218_MIC_1_AMP_GAIN_MASK	(0x7 << 0)
#define DA7218_MIC_AMP_GAIN_MAX		0x7

/* DA7218_MIC_1_SELECT = 0xB7 */
#define DA7218_MIC_1_AMP_IN_SEL_SHIFT	0
#define DA7218_MIC_1_AMP_IN_SEL_MASK	(0x3 << 0)

/* DA7218_MIC_2_CTRL = 0xB8 */
#define DA7218_MIC_2_AMP_MUTE_EN_SHIFT	6
#define DA7218_MIC_2_AMP_MUTE_EN_MASK	(0x1 << 6)
#define DA7218_MIC_2_AMP_EN_SHIFT	7
#define DA7218_MIC_2_AMP_EN_MASK	(0x1 << 7)

/* DA7218_MIC_2_GAIN = 0xB9 */
#define DA7218_MIC_2_AMP_GAIN_SHIFT	0
#define DA7218_MIC_2_AMP_GAIN_MASK	(0x7 << 0)

/* DA7218_MIC_2_SELECT = 0xBB */
#define DA7218_MIC_2_AMP_IN_SEL_SHIFT	0
#define DA7218_MIC_2_AMP_IN_SEL_MASK	(0x3 << 0)

/* DA7218_IN_1_HPF_FILTER_CTRL = 0xBC */
#define DA7218_IN_1_VOICE_HPF_CORNER_SHIFT	0
#define DA7218_IN_1_VOICE_HPF_CORNER_MASK	(0x7 << 0)
#define DA7218_IN_VOICE_HPF_CORNER_MAX		8
#define DA7218_IN_1_VOICE_EN_SHIFT		3
#define DA7218_IN_1_VOICE_EN_MASK		(0x1 << 3)
#define DA7218_IN_1_AUDIO_HPF_CORNER_SHIFT	4
#define DA7218_IN_1_AUDIO_HPF_CORNER_MASK	(0x3 << 4)
#define DA7218_IN_1_HPF_EN_SHIFT		7
#define DA7218_IN_1_HPF_EN_MASK			(0x1 << 7)

/* DA7218_IN_2_HPF_FILTER_CTRL = 0xBD */
#define DA7218_IN_2_VOICE_HPF_CORNER_SHIFT	0
#define DA7218_IN_2_VOICE_HPF_CORNER_MASK	(0x7 << 0)
#define DA7218_IN_2_VOICE_EN_SHIFT		3
#define DA7218_IN_2_VOICE_EN_MASK		(0x1 << 3)
#define DA7218_IN_2_AUDIO_HPF_CORNER_SHIFT	4
#define DA7218_IN_2_AUDIO_HPF_CORNER_MASK	(0x3 << 4)
#define DA7218_IN_2_HPF_EN_SHIFT		7
#define DA7218_IN_2_HPF_EN_MASK			(0x1 << 7)

/* DA7218_ADC_1_CTRL = 0xC0 */
#define DA7218_ADC_1_AAF_EN_SHIFT	2
#define DA7218_ADC_1_AAF_EN_MASK	(0x1 << 2)

/* DA7218_ADC_2_CTRL = 0xC1 */
#define DA7218_ADC_2_AAF_EN_SHIFT	2
#define DA7218_ADC_2_AAF_EN_MASK	(0x1 << 2)

/* DA7218_ADC_MODE = 0xC2 */
#define DA7218_ADC_LP_MODE_SHIFT		0
#define DA7218_ADC_LP_MODE_MASK			(0x1 << 0)
#define DA7218_ADC_LVLDET_MODE_SHIFT		1
#define DA7218_ADC_LVLDET_MODE_MASK		(0x1 << 1)
#define DA7218_ADC_LVLDET_AUTO_EXIT_SHIFT	2
#define DA7218_ADC_LVLDET_AUTO_EXIT_MASK	(0x1 << 2)

/* DA7218_MIXOUT_L_CTRL = 0xCC */
#define DA7218_MIXOUT_L_AMP_EN_SHIFT	7
#define DA7218_MIXOUT_L_AMP_EN_MASK	(0x1 << 7)

/* DA7218_MIXOUT_L_GAIN = 0xCD */
#define DA7218_MIXOUT_L_AMP_GAIN_SHIFT	0
#define DA7218_MIXOUT_L_AMP_GAIN_MASK	(0x3 << 0)
#define DA7218_MIXOUT_AMP_GAIN_MIN	0x1
#define DA7218_MIXOUT_AMP_GAIN_MAX	0x3

/* DA7218_MIXOUT_R_CTRL = 0xCE */
#define DA7218_MIXOUT_R_AMP_EN_SHIFT	7
#define DA7218_MIXOUT_R_AMP_EN_MASK	(0x1 << 7)

/* DA7218_MIXOUT_R_GAIN = 0xCF */
#define DA7218_MIXOUT_R_AMP_GAIN_SHIFT	0
#define DA7218_MIXOUT_R_AMP_GAIN_MASK	(0x3 << 0)

/* DA7218_HP_L_CTRL = 0xD0 */
#define DA7218_HP_L_AMP_MIN_GAIN_EN_SHIFT	2
#define DA7218_HP_L_AMP_MIN_GAIN_EN_MASK	(0x1 << 2)
#define DA7218_HP_L_AMP_OE_SHIFT		3
#define DA7218_HP_L_AMP_OE_MASK			(0x1 << 3)
#define DA7218_HP_L_AMP_ZC_EN_SHIFT		4
#define DA7218_HP_L_AMP_ZC_EN_MASK		(0x1 << 4)
#define DA7218_HP_L_AMP_RAMP_EN_SHIFT		5
#define DA7218_HP_L_AMP_RAMP_EN_MASK		(0x1 << 5)
#define DA7218_HP_L_AMP_MUTE_EN_SHIFT		6
#define DA7218_HP_L_AMP_MUTE_EN_MASK		(0x1 << 6)
#define DA7218_HP_L_AMP_EN_SHIFT		7
#define DA7218_HP_L_AMP_EN_MASK			(0x1 << 7)
#define DA7218_HP_AMP_OE_MASK			(0x1 << 3)

/* DA7218_HP_L_GAIN = 0xD1 */
#define DA7218_HP_L_AMP_GAIN_SHIFT	0
#define DA7218_HP_L_AMP_GAIN_MASK	(0x3F << 0)
#define DA7218_HP_AMP_GAIN_MIN		0x15
#define DA7218_HP_AMP_GAIN_MAX		0x3F

/* DA7218_HP_R_CTRL = 0xD2 */
#define DA7218_HP_R_AMP_MIN_GAIN_EN_SHIFT	2
#define DA7218_HP_R_AMP_MIN_GAIN_EN_MASK	(0x1 << 2)
#define DA7218_HP_R_AMP_OE_SHIFT		3
#define DA7218_HP_R_AMP_OE_MASK			(0x1 << 3)
#define DA7218_HP_R_AMP_ZC_EN_SHIFT		4
#define DA7218_HP_R_AMP_ZC_EN_MASK		(0x1 << 4)
#define DA7218_HP_R_AMP_RAMP_EN_SHIFT		5
#define DA7218_HP_R_AMP_RAMP_EN_MASK		(0x1 << 5)
#define DA7218_HP_R_AMP_MUTE_EN_SHIFT		6
#define DA7218_HP_R_AMP_MUTE_EN_MASK		(0x1 << 6)
#define DA7218_HP_R_AMP_EN_SHIFT		7
#define DA7218_HP_R_AMP_EN_MASK			(0x1 << 7)

/* DA7218_HP_R_GAIN = 0xD3 */
#define DA7218_HP_R_AMP_GAIN_SHIFT	0
#define DA7218_HP_R_AMP_GAIN_MASK	(0x3F << 0)

/* DA7218_HP_SNGL_CTRL = 0xD4 */
#define DA7218_HP_AMP_STEREO_DETECT_STATUS_SHIFT	0
#define DA7218_HP_AMP_STEREO_DETECT_STATUS_MASK		(0x1 << 0)
#define DA7218_HPL_AMP_LOAD_DETECT_STATUS_SHIFT		1
#define DA7218_HPL_AMP_LOAD_DETECT_STATUS_MASK		(0x1 << 1)
#define DA7218_HPR_AMP_LOAD_DETECT_STATUS_SHIFT		2
#define DA7218_HPR_AMP_LOAD_DETECT_STATUS_MASK		(0x1 << 2)
#define DA7218_HP_AMP_LOAD_DETECT_EN_SHIFT		6
#define DA7218_HP_AMP_LOAD_DETECT_EN_MASK		(0x1 << 6)
#define DA7218_HP_AMP_STEREO_DETECT_EN_SHIFT		7
#define DA7218_HP_AMP_STEREO_DETECT_EN_MASK		(0x1 << 7)

/* DA7218_HP_DIFF_CTRL = 0xD5 */
#define DA7218_HP_AMP_DIFF_MODE_EN_SHIFT	0
#define DA7218_HP_AMP_DIFF_MODE_EN_MASK		(0x1 << 0)
#define DA7218_HP_AMP_SINGLE_SUPPLY_EN_SHIFT	4
#define DA7218_HP_AMP_SINGLE_SUPPLY_EN_MASK	(0x1 << 4)

/* DA7218_HP_DIFF_UNLOCK = 0xD7 */
#define DA7218_HP_DIFF_UNLOCK_SHIFT	0
#define DA7218_HP_DIFF_UNLOCK_MASK	(0x1 << 0)
#define DA7218_HP_DIFF_UNLOCK_VAL	0xC3

/* DA7218_HPLDET_JACK = 0xD8 */
#define DA7218_HPLDET_JACK_RATE_SHIFT		0
#define DA7218_HPLDET_JACK_RATE_MASK		(0x7 << 0)
#define DA7218_HPLDET_JACK_DEBOUNCE_SHIFT	3
#define DA7218_HPLDET_JACK_DEBOUNCE_MASK	(0x3 << 3)
#define DA7218_HPLDET_JACK_THR_SHIFT		5
#define DA7218_HPLDET_JACK_THR_MASK		(0x3 << 5)
#define DA7218_HPLDET_JACK_EN_SHIFT		7
#define DA7218_HPLDET_JACK_EN_MASK		(0x1 << 7)

/* DA7218_HPLDET_CTRL = 0xD9 */
#define DA7218_HPLDET_COMP_INV_SHIFT		0
#define DA7218_HPLDET_COMP_INV_MASK		(0x1 << 0)
#define DA7218_HPLDET_HYST_EN_SHIFT		1
#define DA7218_HPLDET_HYST_EN_MASK		(0x1 << 1)
#define DA7218_HPLDET_DISCHARGE_EN_SHIFT	7
#define DA7218_HPLDET_DISCHARGE_EN_MASK		(0x1 << 7)

/* DA7218_HPLDET_TEST = 0xDA */
#define DA7218_HPLDET_COMP_STS_SHIFT	4
#define DA7218_HPLDET_COMP_STS_MASK	(0x1 << 4)

/* DA7218_REFERENCES = 0xDC */
#define DA7218_BIAS_EN_SHIFT	3
#define DA7218_BIAS_EN_MASK	(0x1 << 3)

/* DA7218_IO_CTRL = 0xE0 */
#define DA7218_IO_VOLTAGE_LEVEL_SHIFT		0
#define DA7218_IO_VOLTAGE_LEVEL_MASK		(0x1 << 0)
#define DA7218_IO_VOLTAGE_LEVEL_2_5V_3_6V	0
#define DA7218_IO_VOLTAGE_LEVEL_1_5V_2_5V	1

/* DA7218_LDO_CTRL = 0xE1 */
#define DA7218_LDO_LEVEL_SELECT_SHIFT	4
#define DA7218_LDO_LEVEL_SELECT_MASK	(0x3 << 4)
#define DA7218_LDO_EN_SHIFT		7
#define DA7218_LDO_EN_MASK		(0x1 << 7)

/* DA7218_SIDETONE_CTRL = 0xE4 */
#define DA7218_SIDETONE_MUTE_EN_SHIFT	6
#define DA7218_SIDETONE_MUTE_EN_MASK	(0x1 << 6)
#define DA7218_SIDETONE_FILTER_EN_SHIFT	7
#define DA7218_SIDETONE_FILTER_EN_MASK	(0x1 << 7)

/* DA7218_SIDETONE_IN_SELECT = 0xE5 */
#define DA7218_SIDETONE_IN_SELECT_SHIFT	0
#define DA7218_SIDETONE_IN_SELECT_MASK	(0x3 << 0)
#define DA7218_SIDETONE_IN_SELECT_MAX	4

/* DA7218_SIDETONE_GAIN = 0xE6 */
#define DA7218_SIDETONE_GAIN_SHIFT	0
#define DA7218_SIDETONE_GAIN_MASK	(0x1F << 0)

/* DA7218_DROUTING_ST_OUTFILT_1L = 0xE8 */
#define DA7218_OUTFILT_ST_1L_SRC_SHIFT	0
#define DA7218_OUTFILT_ST_1L_SRC_MASK	(0x7 << 0)
#define DA7218_DMIX_ST_SRC_OUTFILT1L	0
#define DA7218_DMIX_ST_SRC_OUTFILT1R	1
#define DA7218_DMIX_ST_SRC_SIDETONE	2

/* DA7218_DROUTING_ST_OUTFILT_1R = 0xE9 */
#define DA7218_OUTFILT_ST_1R_SRC_SHIFT	0
#define DA7218_OUTFILT_ST_1R_SRC_MASK	(0x7 << 0)

/* DA7218_SIDETONE_BIQ_3STAGE_DATA = 0xEA */
#define DA7218_SIDETONE_BIQ_3STAGE_DATA_SHIFT	0
#define DA7218_SIDETONE_BIQ_3STAGE_DATA_MASK	(0xFF << 0)

/* DA7218_SIDETONE_BIQ_3STAGE_ADDR = 0xEB */
#define DA7218_SIDETONE_BIQ_3STAGE_ADDR_SHIFT	0
#define DA7218_SIDETONE_BIQ_3STAGE_ADDR_MASK	(0x1F << 0)
#define DA7218_SIDETONE_BIQ_3STAGE_CFG_SIZE	30

/* DA7218_EVENT_STATUS = 0xEC */
#define DA7218_HPLDET_JACK_STS_SHIFT	7
#define DA7218_HPLDET_JACK_STS_MASK	(0x1 << 7)

/* DA7218_EVENT = 0xED */
#define DA7218_LVL_DET_EVENT_SHIFT	0
#define DA7218_LVL_DET_EVENT_MASK	(0x1 << 0)
#define DA7218_HPLDET_JACK_EVENT_SHIFT	7
#define DA7218_HPLDET_JACK_EVENT_MASK	(0x1 << 7)

/* DA7218_EVENT_MASK	= 0xEE */
#define DA7218_LVL_DET_EVENT_MSK_SHIFT		0
#define DA7218_LVL_DET_EVENT_MSK_MASK		(0x1 << 0)
#define DA7218_HPLDET_JACK_EVENT_IRQ_MSK_SHIFT	7
#define DA7218_HPLDET_JACK_EVENT_IRQ_MSK_MASK	(0x1 << 7)

/* DA7218_DMIC_1_CTRL = 0xF0 */
#define DA7218_DMIC_1_DATA_SEL_SHIFT	0
#define DA7218_DMIC_1_DATA_SEL_MASK	(0x1 << 0)
#define DA7218_DMIC_1_SAMPLEPHASE_SHIFT	1
#define DA7218_DMIC_1_SAMPLEPHASE_MASK	(0x1 << 1)
#define DA7218_DMIC_1_CLK_RATE_SHIFT	2
#define DA7218_DMIC_1_CLK_RATE_MASK	(0x1 << 2)
#define DA7218_DMIC_1L_EN_SHIFT		6
#define DA7218_DMIC_1L_EN_MASK		(0x1 << 6)
#define DA7218_DMIC_1R_EN_SHIFT		7
#define DA7218_DMIC_1R_EN_MASK		(0x1 << 7)

/* DA7218_DMIC_2_CTRL = 0xF1 */
#define DA7218_DMIC_2_DATA_SEL_SHIFT	0
#define DA7218_DMIC_2_DATA_SEL_MASK	(0x1 << 0)
#define DA7218_DMIC_2_SAMPLEPHASE_SHIFT	1
#define DA7218_DMIC_2_SAMPLEPHASE_MASK	(0x1 << 1)
#define DA7218_DMIC_2_CLK_RATE_SHIFT	2
#define DA7218_DMIC_2_CLK_RATE_MASK	(0x1 << 2)
#define DA7218_DMIC_2L_EN_SHIFT		6
#define DA7218_DMIC_2L_EN_MASK		(0x1 << 6)
#define DA7218_DMIC_2R_EN_SHIFT		7
#define DA7218_DMIC_2R_EN_MASK		(0x1 << 7)

/* DA7218_IN_1L_GAIN = 0xF4 */
#define DA7218_IN_1L_DIGITAL_GAIN_SHIFT	0
#define DA7218_IN_1L_DIGITAL_GAIN_MASK	(0x7F << 0)
#define DA7218_IN_DIGITAL_GAIN_MAX	0x7F

/* DA7218_IN_1R_GAIN = 0xF5 */
#define DA7218_IN_1R_DIGITAL_GAIN_SHIFT	0
#define DA7218_IN_1R_DIGITAL_GAIN_MASK	(0x7F << 0)

/* DA7218_IN_2L_GAIN = 0xF6 */
#define DA7218_IN_2L_DIGITAL_GAIN_SHIFT	0
#define DA7218_IN_2L_DIGITAL_GAIN_MASK	(0x7F << 0)

/* DA7218_IN_2R_GAIN = 0xF7 */
#define DA7218_IN_2R_DIGITAL_GAIN_SHIFT	0
#define DA7218_IN_2R_DIGITAL_GAIN_MASK	(0x7F << 0)

/* DA7218_OUT_1L_GAIN = 0xF8 */
#define DA7218_OUT_1L_DIGITAL_GAIN_SHIFT	0
#define DA7218_OUT_1L_DIGITAL_GAIN_MASK		(0xFF << 0)
#define DA7218_OUT_DIGITAL_GAIN_MIN		0x0
#define DA7218_OUT_DIGITAL_GAIN_MAX		0x97

/* DA7218_OUT_1R_GAIN = 0xF9 */
#define DA7218_OUT_1R_DIGITAL_GAIN_SHIFT	0
#define DA7218_OUT_1R_DIGITAL_GAIN_MASK		(0xFF << 0)

/* DA7218_MICBIAS_CTRL = 0xFC */
#define DA7218_MICBIAS_1_LEVEL_SHIFT	0
#define DA7218_MICBIAS_1_LEVEL_MASK	(0x7 << 0)
#define DA7218_MICBIAS_1_LP_MODE_SHIFT	3
#define DA7218_MICBIAS_1_LP_MODE_MASK	(0x1 << 3)
#define DA7218_MICBIAS_2_LEVEL_SHIFT	4
#define DA7218_MICBIAS_2_LEVEL_MASK	(0x7 << 4)
#define DA7218_MICBIAS_2_LP_MODE_SHIFT	7
#define DA7218_MICBIAS_2_LP_MODE_MASK	(0x1 << 7)

/* DA7218_MICBIAS_EN = 0xFD */
#define DA7218_MICBIAS_1_EN_SHIFT	0
#define DA7218_MICBIAS_1_EN_MASK	(0x1 << 0)
#define DA7218_MICBIAS_2_EN_SHIFT	4
#define DA7218_MICBIAS_2_EN_MASK	(0x1 << 4)


/*
 * General defines & data
 */

/* Register inversion */
#define DA7218_NO_INVERT	0
#define DA7218_INVERT		1

/* Byte related defines */
#define DA7218_BYTE_SHIFT	8
#define DA7218_BYTE_MASK	0xFF
#define DA7218_2BYTE_SHIFT	16
#define DA7218_2BYTE_MASK	0xFFFF

/* PLL Output Frequencies */
#define DA7218_PLL_FREQ_OUT_90316	90316800
#define DA7218_PLL_FREQ_OUT_98304	98304000

/* PLL Frequency Dividers */
#define DA7218_PLL_INDIV_2_TO_4_5_MHZ_VAL	1
#define DA7218_PLL_INDIV_4_5_TO_9_MHZ_VAL	2
#define DA7218_PLL_INDIV_9_TO_18_MHZ_VAL	4
#define DA7218_PLL_INDIV_18_TO_36_MHZ_VAL	8
#define DA7218_PLL_INDIV_36_TO_54_MHZ_VAL	16

/* ALC Calibration */
#define DA7218_ALC_CALIB_DELAY_MIN	2500
#define DA7218_ALC_CALIB_DELAY_MAX	5000
#define DA7218_ALC_CALIB_MAX_TRIES	5

/* Ref Oscillator */
#define DA7218_REF_OSC_CHECK_DELAY_MIN	5000
#define DA7218_REF_OSC_CHECK_DELAY_MAX	10000
#define DA7218_REF_OSC_CHECK_TRIES	4

/* SRM */
#define DA7218_SRM_CHECK_DELAY		50
#define DA7218_SRM_CHECK_TRIES		8

/* Mic Level Detect */
#define DA7218_MIC_LVL_DET_DELAY	50

enum da7218_biq_cfg {
	DA7218_BIQ_CFG_DATA = 0,
	DA7218_BIQ_CFG_ADDR,
	DA7218_BIQ_CFG_SIZE,
};

enum da7218_clk_src {
	DA7218_CLKSRC_MCLK = 0,
	DA7218_CLKSRC_MCLK_SQR,
};

enum da7218_sys_clk {
	DA7218_SYSCLK_MCLK = 0,
	DA7218_SYSCLK_PLL,
	DA7218_SYSCLK_PLL_SRM,
};

enum da7218_dev_id {
	DA7217_DEV_ID = 0,
	DA7218_DEV_ID,
};

/* Regulators */
enum da7218_supplies {
	DA7218_SUPPLY_VDD = 0,
	DA7218_SUPPLY_VDDMIC,
	DA7218_SUPPLY_VDDIO,
	DA7218_NUM_SUPPLIES,
};

/* Private data */
struct da7218_priv {
	struct da7218_pdata *pdata;

	struct regulator_bulk_data supplies[DA7218_NUM_SUPPLIES];
	struct regmap *regmap;
	int dev_id;

	struct snd_soc_jack *jack;
	int irq;

	struct clk *mclk;
	unsigned int mclk_rate;

	bool hp_single_supply;
	bool master;
	u8 alc_en;
	u8 in_filt_en;
	u8 mic_lvl_det_en;

	u8 biq_5stage_coeff[DA7218_OUT_1_BIQ_5STAGE_CFG_SIZE];
	u8 stbiq_3stage_coeff[DA7218_SIDETONE_BIQ_3STAGE_CFG_SIZE];
};

/* HP detect control */
int da7218_hpldet(struct snd_soc_codec *codec, struct snd_soc_jack *jack);

#endif /* _DA7218_H */
