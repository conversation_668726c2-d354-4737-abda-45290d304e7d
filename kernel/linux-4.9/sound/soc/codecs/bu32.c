/*
 * bu32.c  --  audio driver for bu32
 *
 * Copyright (C) 2018 Asahi Kasei Microdevices Corporation
 *  Author                Date        Revision
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *                      18/06/04        1.0 Kernel 4_9_XX
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 *
 */

 #include <linux/module.h>
 #include <linux/init.h>
 #include <linux/i2c.h>
 #include <linux/delay.h>
 #include <linux/slab.h>
 #include <linux/gpio.h>
 #include <sound/pcm.h>
 #include <sound/pcm_params.h>
 #include <sound/soc.h>
 #include <sound/soc-dapm.h>
 #include <sound/initval.h>
 #include <sound/tlv.h>
 #include <linux/ioctl.h>
 #include <linux/fs.h>
 #include <linux/uaccess.h>
 #include <linux/spi/spi.h>
 #include <linux/mutex.h>
 #include <linux/firmware.h>
 #include <linux/vmalloc.h>
 
 #include <linux/regmap.h>
 #include <linux/of_gpio.h>
 
 #include "bu32.h"
 
 
 #define KERNEL_4_9_XX
 
 
 #define BU32_DEBUG			//used at debug mode
 #define SPI_TRANSFER_BUF_LEN 15
 #ifdef BU32_DEBUG
 #define akdbgprt printk
 #else
 #define akdbgprt(format, arg...) do {} while (0)
 #endif
 
 #define OUTPUTMODE_REG 0x0103
 /* bu32 Codec Private Data */
 struct bu32_priv {
	 struct snd_soc_codec *codec;
	 struct spi_device *spi;
	 
	 int pdn_gpio;
	 int fs;
	 int outputmode;
	 int current_eq;
	 int debug_addr;
	 struct regmap *regmap;
	 char tx_buf[SPI_TRANSFER_BUF_LEN + 1];
	 char rx_buf[SPI_TRANSFER_BUF_LEN + 1];
 };
 
 
 static struct snd_soc_dai_ops bu32_dai_ops = {
	 .hw_params	= bu32_hw_params,
	 .set_sysclk	= bu32_set_dai_sysclk,
	 .set_fmt	= bu32_set_dai_fmt,
	 .digital_mute = bu32_set_dai_mute,
 };
 
 static struct snd_soc_card virtual_card;

 struct snd_soc_dai_driver bu32_dai[] = {   
	 {										 
		 .name = "BU32-aif1",
		 .id = AIF_PORT1,
		 .playback = {
				.stream_name = "AIF1 Playback",
				.channels_min = 1,
				.channels_max = 16,
				.rates = BU32_RATES,
				.formats = BU32_FORMATS,
		 },
		 .capture = {
				.stream_name = "AIF1 Capture",
				.channels_min = 1,
				.channels_max = 16,
				.rates = BU32_RATES,
				.formats = BU32_FORMATS,
		 },
		 .ops = &bu32_dai_ops,
	 }
 };
 
 static int bu32_set_dai_sysclk(struct snd_soc_dai *dai, int clk_id,
		 unsigned int freq, int dir)
 {
	 akdbgprt("\t[bu32] %s(%d)\n",__FUNCTION__,__LINE__);
 
	 return 0;
 }
 
 
 static int bu32_set_dai_mute(struct snd_soc_dai *dai, int mute) 
 {
	 akdbgprt("\t[bu32] %s(%d)\n",__FUNCTION__,__LINE__);
 
	 if (mute) {
		 akdbgprt("\t[bu32] Mute ON(%d)\n",__LINE__);
	 } else {
		 akdbgprt("\t[bu32] Mute OFF(%d)\n",__LINE__);
	 }
 
	 return 0;
 }
 
 
 static int bu32_set_dai_fmt(struct snd_soc_dai *dai, unsigned int fmt)
 {
	 struct snd_soc_codec *codec = dai->codec;
	 struct bu32_priv *bu32 = snd_soc_codec_get_drvdata(codec);
	 int msnbit, nSDNo;
 
 
	 akdbgprt("\t[bu32] %s(%d) port id %d\n",__FUNCTION__,__LINE__, dai->id);
 
	 switch (dai->id) {
		 case AIF_PORT1: nSDNo = 0; break;
		 case AIF_PORT2: nSDNo = 1; break;
		 case AIF_PORT3: nSDNo = 2; break;
		 case AIF_PORT4: nSDNo = 3; break;
		 case AIF_PORT5: nSDNo = 4; break;
		 default:
			 pr_err("%s: Invalid dai id %d\n", __func__, dai->id);
			 return -EINVAL;
			 break;
	 }
 
	 /* set master/slave audio interface */
	 switch (fmt & SND_SOC_DAIFMT_MASTER_MASK) {
		 case SND_SOC_DAIFMT_CBS_CFS:
			 msnbit = 0;
			 akdbgprt("\t[bu32] %s(Slave_nSDNo=%d)\n",__FUNCTION__,nSDNo);
			 break;
		 case SND_SOC_DAIFMT_CBM_CFM:
			 msnbit = 1;
			 akdbgprt("\t[bu32] %s(Master_nSDNo=%d)\n",__FUNCTION__,nSDNo);
			 break;
		 case SND_SOC_DAIFMT_CBS_CFM:
		 case SND_SOC_DAIFMT_CBM_CFS:
		 default:
			 dev_err(codec->dev, "Clock mode unsupported");
			return -EINVAL;
		}
 
	 akdbgprt("\t[bu32] %s(format=%d after mask = %d)\n",__FUNCTION__,fmt, fmt & SND_SOC_DAIFMT_FORMAT_MASK);
 
 
 
	 return(0);
 }
 
 static void write_reg(struct snd_soc_codec *codec, struct reg_default* preg, int count)
 {
	 int i;
	 for(i = 0; i < count; i++)
	 {
		 snd_soc_write(codec, preg[i].reg, preg[i].def);
	 }
 }
 
 static void read_reg(struct snd_soc_codec *codec, struct reg_default* preg, int count)
 {
	 int i, value;
	 for(i = 0; i < count; i++)
	 {
		 value = snd_soc_read(codec, preg[i].reg);
		 akdbgprt("[bu32] read reg 0x%x val 0x%x\n",preg[i].reg, value);
	 }
 }
 
 static int bu32_init_reg(struct snd_soc_codec *codec)
 {
	 struct bu32_priv *bu32 = snd_soc_codec_get_drvdata(codec);
	 int devid = 0, val = 0;
	 akdbgprt("\t[bu32] %s\n",__FUNCTION__);
	 val = snd_soc_read(codec, default_reg[0].reg);
	 akdbgprt("\t[bu32] read val 0x%x\n", val);
	 write_reg(codec, system_reset_reg_value, sizeof(system_reset_reg_value)/ sizeof(struct reg_default));
	 write_reg(codec, default_reg, sizeof(default_reg)/ sizeof(struct reg_default));
	 write_reg(codec, init_dauflt_reg_value, sizeof(init_dauflt_reg_value)/ sizeof(struct reg_default));
	 write_reg(codec, init_3_reg_value, sizeof(init_3_reg_value)/ sizeof(struct reg_default));
 
	 val = snd_soc_read(codec, default_reg[0].reg);
	 akdbgprt("\t[bu32] read val 0x%x..............\n", val);
	 if(val != default_reg[0].def)
	 {
		 akdbgprt("bu32 read reg error\n");
	 }
 
	 return 0;
 }
 
 
 static int bu32_probe(struct snd_soc_codec *codec)
 {
	 struct bu32_priv *bu32 = snd_soc_codec_get_drvdata(codec);
	 int ret = 0;
	//  int i, busstate;
 
	 akdbgprt("\t[bu32] %s(%d)\n",__FUNCTION__,__LINE__);
 
	 bu32_init_reg(codec);

	 // 设置默认输出模式为 mix (值为 3)
	 bu32->outputmode = 3; // 设置为 mix
	 write_reg(codec, mute_reg_value, sizeof(mute_reg_value) / sizeof(struct reg_default));
	 write_reg(codec, mix_reg_value, sizeof(mix_reg_value)/ sizeof(struct reg_default));
	 write_reg(codec, unmute_reg_value, sizeof(unmute_reg_value) / sizeof(struct reg_default));

	 virtual_card.dev = codec->dev;
	 ret = snd_soc_register_card(&virtual_card);
	 if(ret)
	 {
		pr_err("Failed to register virtual card: %d\n", ret);
    	return ret;
	 }
	 return ret;
 }
 
 static int bu32_remove(struct snd_soc_codec *codec)
 {
	 struct bu32_priv *bu32 = snd_soc_codec_get_drvdata(codec);
 
	 akdbgprt("\t[bu32] %s(%d)\n",__func__,__LINE__);
 
	 bu32_set_bias_level(codec, SND_SOC_BIAS_OFF);
 
	 snd_soc_unregister_card(&virtual_card);

	 return 0;
 }
 
 static int bu32_suspend(struct snd_soc_codec *codec)
 {
	 struct bu32_priv *bu32 = snd_soc_codec_get_drvdata(codec);	
	 akdbgprt("\t[bu32] %s(%d)\n",__func__,__LINE__);
	 bu32_set_bias_level(codec, SND_SOC_BIAS_OFF);
 
	 
	 return 0;
 }
 
 static int bu32_resume(struct snd_soc_codec *codec)
 {
	 struct bu32_priv *bu32 = snd_soc_codec_get_drvdata(codec);	
	 int i;
 
	 for ( i = 0 ; i < ARRAY_SIZE(default_reg) ; i++ ) {
		 regmap_write(bu32->regmap, default_reg[i].reg, default_reg[i].def);
	 }
 
	 bu32_init_reg(codec);
	 return 0;
 }
 
 static int bu32_spi_read(struct bu32_priv *priv, int len)
 {	
	 struct spi_transfer t = {
		 .tx_buf = priv->tx_buf,
		 .rx_buf = priv->rx_buf,
		 .len = len,
		 .cs_change = 1,
		 .delay_usecs = 0,
	 };
	 struct spi_message m;
	 int ret;
	 spi_message_init(&m);
	 spi_message_add_tail(&t, &m);
	 ret = spi_sync(priv->spi, &m);
	 if (ret)
	 {
		 dev_err(&priv->spi->dev, "spi transfer failed: ret = %d\n", ret);
	 }
	 return ret;
 }
 
 unsigned int bu32_read_register(struct snd_soc_codec *codec, unsigned int reg)
 {
	 struct bu32_priv *bu32 = snd_soc_codec_get_drvdata(codec);
	 int value = 0;
	 unsigned char *tvalue = (unsigned char *)&value;
	 unsigned char *tx = bu32->tx_buf, *rx = bu32->rx_buf;
	 int	wlen, rlen;
	 int i = 0;
	 int val, ret;
	 if (reg == SND_SOC_NOPM)
		 return 0;
	 
	 
	 tx [ 0 ] = 0XD0;
	 tx [ 1 ] = 0X00;
	 tx [ 2 ] = (reg >> 8) & 0xFF;
	 tx [ 3 ] = reg & 0xFF;
	 bu32_spi_read(bu32, READ_MODE_HEADER_FIRST_STEP_LEN);
 
	 // **********************************************************************************
 
	 tx [ 0 ] = 0xD1;
	 tx [ 1 ] = 0x00;
 
	 for (i = READ_MODE_HEADER_SEROND_STEP_LEN; i < TRANSFER_LEN + READ_MODE_HEADER_SEROND_STEP_LEN; i++ )
	 {
		 tx [ i ] = 0x00;
	 }
	 bu32_spi_read(bu32, TRANSFER_LEN + READ_MODE_HEADER_SEROND_STEP_LEN);
 
	 for (i = 2; i < TRANSFER_LEN + READ_MODE_HEADER_SEROND_STEP_LEN; i++ )
	 {
		 tvalue [ i - 2 ] = rx [ i ];
	 }
	 if(reg == 1)
	 {
		 akdbgprt("\t[bu32] %s(%d) read len 0x%x 0x%x 0x%x 0x%x\n",__func__,__LINE__, rx[0], rx[1], rx[2], rx[3]);
	 }
	 
	 return value;
 }
 
 static int bu32_spi_write(struct bu32_priv *priv,  int len)
 {
	 struct spi_transfer t = {
		 .tx_buf = priv->tx_buf,
		 .rx_buf = priv->rx_buf,
		 .len = len,
		 .cs_change = 1,
		 .delay_usecs = 0,
	 };
	 struct spi_message m;
	 int ret;
	 spi_message_init(&m);
	 spi_message_add_tail(&t, &m);
	 ret = spi_sync(priv->spi, &m);
	 if (ret)
	 {
		 dev_err(&priv->spi->dev, "spi transfer write failed: ret = %d\n", ret);
	 }
	 return ret;
 }
 
 
 static int bu32_write_register(struct snd_soc_codec *codec,  unsigned int reg,  unsigned int value)
 {
	 struct bu32_priv *bu32 = snd_soc_codec_get_drvdata(codec);
	 unsigned char *tvalue = (unsigned char *)&value;
	 unsigned char* tx = bu32->tx_buf, *rx = bu32->rx_buf;
	 int wlen = TRANSFER_LEN + ADDRESS_LEN;
	 int ret;
	 uint8_t i;
 
	 if (reg == SND_SOC_NOPM)
		 return 0;
 
	 tx [ 0 ] = (reg >> 8) & 0xFF;
	 tx [ 1 ] = reg & 0xFF;
 
	 for ( i = ADDRESS_LEN; i < TRANSFER_LEN + ADDRESS_LEN; i++ )
	 {
		 tx [ i ] = tvalue [ i - ADDRESS_LEN ];
	 }
	 
	 ret = bu32_spi_write(bu32, wlen);
	 if(reg == 1)
	 {
		 akdbgprt("\t[bu32] %s(%d) write reg %d int  value 0x%x tvalue 0x%x\n",__FUNCTION__,__LINE__, reg, value, tx [ ADDRESS_LEN ]);
		 akdbgprt("\t[bu32] %s(%d) tx buf tx[0] 0x%x tx[1] 0x%x tx[2] 0x%x", __FUNCTION__,__LINE__, tx [ 0 ], tx [ 1 ], tx [ 2 ]);
	 }
	 
	 return ret;
 }
 
 static int bu32_set_bias_level(struct snd_soc_codec *codec,
		 enum snd_soc_bias_level level)
 {
 #ifndef KERNEL_3_18_XX
	 struct snd_soc_dapm_context *dapm = snd_soc_codec_get_dapm(codec);
 #endif
	 akdbgprt("\t[bu32] %s(%d)\n",__FUNCTION__,__LINE__);
	 int val = 0;
	 switch (level) {
	 case SND_SOC_BIAS_ON:
		 akdbgprt("[bu32] SND_SOC_BIAS_ON\n");
 #if 0
		 snd_soc_write(codec, bu32_C3_001_MICBIAS_POWER_MANAGEMENT, 0x20);
		 snd_soc_update_bits(codec, bu32_C3_002_RESET_CONTROL, 0x1, 0x1); 
		 val = snd_soc_read(codec, bu32_C3_002_RESET_CONTROL);
		 akdbgprt("[bu32] %s reg = 0x%x val = 0x%x\n",__FUNCTION__, bu32_C3_002_RESET_CONTROL, val);
 #endif
		 break;
	 case SND_SOC_BIAS_PREPARE:
		 akdbgprt("[bu32] SND_SOC_BIAS_PREPARE\n");
		 break;
	 case SND_SOC_BIAS_STANDBY:
		 akdbgprt("[bu32] SND_SOC_BIAS_STANDBY\n");
		 break;
	 case SND_SOC_BIAS_OFF:
		 akdbgprt("\t[bu32] SND_SOC_BIAS_OFF\n");
		 break;
	 }
 
 
	 return 0;
 }
 
 static const char *test_reg_select[]   = 
 {
	 "read bu32 Reg AudioHUB 0x0**",
	 "read bu32 Reg AudioHUB 0x1**",
	 "read bu32 Reg AudioHUB 0x2**",
	 "read bu32 Reg AudioHUB 0xA**",
	 "read bu32 Reg AudioHUB ALL",
 };
 
 static const char *outputmode[]   = 
 {
	 "DAB OUT",
	 "FM OUT",
	 "LINEOUT",
	 "mix",
 };
 
 static const char *eq[]   = 
 {
	 "default",
	 "jazz",
	 "rock",
	 "flat",
 };
 
 
 static int nTestRegNo = 0;
 static const struct soc_enum bu32_test_enum[] = 
 {
	 SOC_ENUM_SINGLE_EXT(ARRAY_SIZE(test_reg_select), test_reg_select),
 };
 
 static const struct soc_enum outputmode_enum[] = 
 {
	 SOC_ENUM_SINGLE_EXT(ARRAY_SIZE(outputmode), outputmode),
 };
 
 static const struct soc_enum eq_enum[] = 
 {
	 SOC_ENUM_SINGLE_EXT(ARRAY_SIZE(eq), eq),
 };
 
 static int get_test_reg(struct snd_kcontrol *kcontrol, struct snd_ctl_elem_value  *ucontrol)
 {
	 /* Get the current output routing */
	 ucontrol->value.enumerated.item[0] = nTestRegNo;
 
	 return 0;
 }
 
 static int set_test_reg(struct snd_kcontrol *kcontrol, struct snd_ctl_elem_value  *ucontrol)
 {
	 struct snd_soc_codec *codec = snd_soc_kcontrol_codec(kcontrol);
	 u32    currMode = ucontrol->value.enumerated.item[0];
	 int    i, value;
	 int	   regs, rege;
 
	 nTestRegNo = currMode;
 
	 if(currMode != 4)
	 {
		 switch(currMode) {
			 case 0: regs = 0x000; rege = 0x22; break;
			 case 1: regs = 0x100; rege = 0x10A; break;
			 case 2: regs = 0x201; rege = 0x208; break;
			 case 3: regs = 0xA01; rege = 0xA05; break;
			 default: break;
		 }
		 for ( i = regs ; i <= rege ; i++ ){
			 value = snd_soc_read(codec, i);
			 printk("bu32:, I:0x%02X,0x%04X\n", i, value);
		 }
	 }
	 else
	 {
		 read_reg(codec, system_reset_reg_value, sizeof(system_reset_reg_value)/ sizeof(struct reg_default));
		 read_reg(codec, default_reg, sizeof(default_reg)/ sizeof(struct reg_default));
		 read_reg(codec, init_dauflt_reg_value, sizeof(init_dauflt_reg_value)/ sizeof(struct reg_default));
		 read_reg(codec, init_3_reg_value, sizeof(init_3_reg_value)/ sizeof(struct reg_default));
	 }
 
 
	 return 0;
 
 }
 
 static int get_output_mode(struct snd_kcontrol *kcontrol, struct snd_ctl_elem_value  *ucontrol)
 {
	 struct snd_soc_codec *codec = snd_soc_kcontrol_codec(kcontrol);
	 struct bu32_priv *bu32 = snd_soc_codec_get_drvdata(codec);
	 ucontrol->value.enumerated.item[0] = bu32->outputmode;
	 return 0;
 }
 
 static int set_output_mode(struct snd_kcontrol *kcontrol, struct snd_ctl_elem_value  *ucontrol)
 {
	 struct snd_soc_codec *codec = snd_soc_kcontrol_codec(kcontrol);
	 struct bu32_priv *bu32 = snd_soc_codec_get_drvdata(codec);
	 u32    currMode = ucontrol->value.enumerated.item[0];
	 bu32->outputmode = currMode;
	 write_reg(codec, mute_reg_value, sizeof(mute_reg_value)/ sizeof(struct reg_default));
	 switch(currMode)
	 {
		 case 0:
			 snd_soc_write(codec, OUTPUTMODE_REG, 0x0);
		 break;
		 case 1:
			 snd_soc_write(codec, OUTPUTMODE_REG, 0x2);
		 break;
		 case 2:
			 write_reg(codec, lineout_reg_value, sizeof(lineout_reg_value)/ sizeof(struct reg_default));
		 break;
		 case 3:
			 write_reg(codec, mix_reg_value, sizeof(mix_reg_value)/ sizeof(struct reg_default));
		 break;
		 default:
		 break;
	 }
	 write_reg(codec, unmute_reg_value, sizeof(unmute_reg_value)/ sizeof(struct reg_default));
	 return 0;
 }
 
 static int get_current_eq(struct snd_kcontrol *kcontrol, struct snd_ctl_elem_value  *ucontrol)
 {
	 struct snd_soc_codec *codec = snd_soc_kcontrol_codec(kcontrol);
	 struct bu32_priv *bu32 = snd_soc_codec_get_drvdata(codec);
	 ucontrol->value.enumerated.item[0] = bu32->current_eq;
	 return 0;
 }
 
 static int set_current_eq(struct snd_kcontrol *kcontrol, struct snd_ctl_elem_value  *ucontrol)
 {
	 struct snd_soc_codec *codec = snd_soc_kcontrol_codec(kcontrol);
	 struct bu32_priv *bu32 = snd_soc_codec_get_drvdata(codec);
	 u32    currMode = ucontrol->value.enumerated.item[0];
	 bu32->current_eq = currMode;
	 write_reg(codec, mute_reg_value, sizeof(mute_reg_value)/ sizeof(struct reg_default));
	 switch(currMode)
	 {
		 case 0:
			 write_reg(codec, init_dauflt_reg_value, sizeof(mute_reg_value)/ sizeof(struct reg_default));
		 break;
		 case 1:
			 write_reg(codec, init_jazz_reg_value, sizeof(init_jazz_reg_value)/ sizeof(struct reg_default));
		 break;
		 case 2:
			 write_reg(codec, init_rock_reg_value, sizeof(init_rock_reg_value)/ sizeof(struct reg_default));
		 break;
		 case 3:
			 write_reg(codec, init_flat_reg_value, sizeof(init_flat_reg_value)/ sizeof(struct reg_default));
		 break;
		 default:
		 break;
	 }
	 write_reg(codec, unmute_reg_value, sizeof(unmute_reg_value)/ sizeof(struct reg_default));
	 return 0;
 }
 
 static int get_debug_reg(struct snd_kcontrol *kcontrol, struct snd_ctl_elem_value  *ucontrol)
 {
	 struct snd_soc_codec *codec = snd_soc_kcontrol_codec(kcontrol);
	 struct bu32_priv *bu32 = snd_soc_codec_get_drvdata(codec);
	 ucontrol->value.enumerated.item[0] = bu32->debug_addr;
 
	 return 0;
 }
 
 static int set_debug_reg(struct snd_kcontrol *kcontrol, struct snd_ctl_elem_value  *ucontrol)
 {
	 struct snd_soc_codec *codec = snd_soc_kcontrol_codec(kcontrol);
	 struct bu32_priv *bu32 = snd_soc_codec_get_drvdata(codec);
	 u32    currMode = ucontrol->value.enumerated.item[0];
	 bu32->debug_addr = currMode;
	 return 0;
 }
 
 static int get_debug_value(struct snd_kcontrol *kcontrol, struct snd_ctl_elem_value  *ucontrol)
 {
	 struct snd_soc_codec *codec = snd_soc_kcontrol_codec(kcontrol);
	 struct bu32_priv *bu32 = snd_soc_codec_get_drvdata(codec);
	 int val = snd_soc_read(codec, bu32->debug_addr);
	 akdbgprt("[bu32] debug read reg 0x%x val 0x%x\n", bu32->debug_addr, val);
	 ucontrol->value.enumerated.item[0] = val;
 
	 return 0;
 }
 
 static int set_debug_value(struct snd_kcontrol *kcontrol, struct snd_ctl_elem_value  *ucontrol)
 {
	 struct snd_soc_codec *codec = snd_soc_kcontrol_codec(kcontrol);
	 struct bu32_priv *bu32 = snd_soc_codec_get_drvdata(codec);
	 u32    currMode = ucontrol->value.enumerated.item[0];
	 snd_soc_write(codec, bu32->debug_addr, currMode);
	 return 0;
 }
 
 static const struct snd_kcontrol_new bu32_snd_controls[] = {
 #ifdef BU32_DEBUG
	 SOC_ENUM_EXT("Reg Read", bu32_test_enum[0], get_test_reg, set_test_reg),
	 SOC_SINGLE_EXT("Set Addr", SND_SOC_NOPM, 0, 0xFFFF, 0, get_debug_reg, set_debug_reg),
	 SOC_SINGLE_EXT("Set debug value", SND_SOC_NOPM, 0, 0xFFFF, 0, get_debug_value, set_debug_value),
 #endif
	 SOC_ENUM_EXT("Output mode", outputmode_enum[0], get_output_mode, set_output_mode),
	 SOC_ENUM_EXT("SET EQ", eq_enum[0], get_current_eq, set_current_eq),
 };

/* 虚拟平台设备 */
static struct platform_device *virtual_platform_device;

/* 简单的虚拟DAI链接 */
static struct snd_soc_dai_link virtual_dai_link = {
	.name = "Virtual BU32",
	.stream_name = "Virtual BU32 Stream",
	.cpu_dai_name = "snd-soc-dummy-dai",
	.codec_dai_name = "snd-soc-dummy-dai",
	.platform_name = "snd-soc-dummy",
	.codec_name = "snd-soc-dummy",
};

static struct snd_soc_card virtual_card = {
	.name = "Virtual-BU32-Card",
	.owner = THIS_MODULE,
	.dai_link = &virtual_dai_link,
	.num_links = 1,
	.controls = bu32_snd_controls,
	.num_controls = ARRAY_SIZE(bu32_snd_controls),
};
 
 static const struct snd_soc_dapm_widget bu32_dapm_widgets[] = {
 
 };
 
 static const struct snd_soc_dapm_route bu32_intercon[] = {
 
 };
 
 struct snd_soc_codec_driver soc_codec_dev_bu32 = {
	 .probe = bu32_probe,
	 .remove = bu32_remove,
	 .suspend =	bu32_suspend,
	 .resume =	bu32_resume,
 
	 .read = bu32_read_register,
	 .write = bu32_write_register,
 
	 .idle_bias_off = true,
	 .set_bias_level = bu32_set_bias_level,
 
 #ifdef KERNEL_4_9_XX
	 .component_driver = {
 #endif
	 .controls = bu32_snd_controls,
	 .num_controls = ARRAY_SIZE(bu32_snd_controls),
	 .dapm_widgets = bu32_dapm_widgets,
	 .num_dapm_widgets = ARRAY_SIZE(bu32_dapm_widgets),
	 .dapm_routes = bu32_intercon,
	 .num_dapm_routes = ARRAY_SIZE(bu32_intercon),
 #ifdef KERNEL_4_9_XX
	 },
 #endif
 };
 EXPORT_SYMBOL_GPL(soc_codec_dev_bu32);
 
 static const struct regmap_config bu32_regmap = {
	 .reg_bits = 16,
	 .val_bits = 8,
	 
	 .max_register = BU32_MAX_REGISTER,
	 .reg_defaults = default_reg,
	 .num_reg_defaults = ARRAY_SIZE(default_reg),
	 .cache_type = REGCACHE_RBTREE,
 };
 
 static int bu32_spi_probe(struct spi_device *spi)
 {
	 struct bu32_priv *bu32;
	 int ret;
 
	 printk("\t[bu32] %s(%d)\n",__func__,__LINE__);
 
	 bu32 = devm_kzalloc(&spi->dev, sizeof(struct bu32_priv),
				   GFP_KERNEL);
	 if (bu32 == NULL)
		 return -ENOMEM;
 
	 memset(bu32, 0, sizeof(struct bu32_priv));
	 bu32->regmap = devm_regmap_init_spi(spi, &bu32_regmap);
	 if (IS_ERR(bu32->regmap)) {
		 ret = PTR_ERR(bu32->regmap);
		 dev_err(&spi->dev, "Failed to allocate register map: %d\n",
			 ret);
		 return ret;
	 }
 
	 spi_set_drvdata(spi, bu32);
 
	 bu32->spi = spi;
 
	 ret = snd_soc_register_codec(&spi->dev,
			 &soc_codec_dev_bu32, &bu32_dai[0], ARRAY_SIZE(bu32_dai));
 
	 if (ret != 0) {
		 dev_err(&spi->dev, "Failed to register CODEC: %d\n", ret);
		 return ret;
	 }
 
	 return 0;
 
 }
 
 static int  bu32_spi_remove(struct spi_device *spi)
 {
	 snd_soc_unregister_codec(&spi->dev);
	 return 0;
 }
 
 static int sdfstab[] = {
	 8000, 12000, 16000, 24000,
	 32000, 48000,
 };
 
 static int sdbicktab[] = {
	 64, 48, 32, 128, 256
 };
 
 static int setSDClock(struct snd_soc_codec *codec,int nSDNo)
 {
	 return 0;
 }
 
 static int bu32_hw_params(struct snd_pcm_substream *substream,
		 struct snd_pcm_hw_params *params,
		 struct snd_soc_dai *dai)
 {
	 struct snd_soc_codec *codec = dai->codec;
	 struct bu32_priv *bu32 = snd_soc_codec_get_drvdata(codec);
	 int nSDNo;
	 int fsno, nmax;
	 int DIODLbit, addr, value;
	 akdbgprt("\t[bu32] %s(%d) \n",__FUNCTION__,__LINE__);
 
	 bu32->fs = params_rate(params);
 
	 akdbgprt("\t[bu32] %s fs=%d\n",__FUNCTION__, bu32->fs );
	 mdelay(10);
 
	 DIODLbit = 2;
 
	 switch (params_format(params)) {
		 case SNDRV_PCM_FORMAT_S16_LE:
			 DIODLbit = 2;
			 break;
		 case SNDRV_PCM_FORMAT_S24_LE:
			 DIODLbit = 0;
			 break;
		 case SNDRV_PCM_FORMAT_S32_LE:
			 DIODLbit = 3;
			 break;
		 default:
			 pr_err("%s: invalid Audio format %u\n", __func__, params_format(params));
			 return -EINVAL;
	 }
 
	 switch (dai->id) {
		 case AIF_PORT1: nSDNo = 0; break;
		 case AIF_PORT2: nSDNo = 1; break;
		 case AIF_PORT3: nSDNo = 2; break;
		 case AIF_PORT4: nSDNo = 3; break;
		 case AIF_PORT5: nSDNo = 4; break;
		 default:
			 pr_err("%s: Invalid dai id %d\n", __func__, dai->id);
			 return -EINVAL;
			 break;
	 }	
 
	 fsno = 0;
	 nmax = sizeof(sdfstab) / sizeof(sdfstab[0]);
	 akdbgprt("\t[bu32] %s nmax = %d\n",__FUNCTION__, nmax);
 
 
 
	 akdbgprt("\t[bu32] %s setSDClock\n",__FUNCTION__);
 
	 setSDClock(codec, nSDNo);
 
 
	 return 0;
 }
 
 static struct of_device_id bu32_dt_ids[] = {
	 { .compatible = "rohm,bu32"},
	 {   }
 };
 MODULE_DEVICE_TABLE(of, bu32_dt_ids);
 
 
 
 static struct spi_driver bu32_spi_driver = {
	 .driver = {
		 .name = "bu32",
		 .owner = THIS_MODULE,
		 .of_match_table = of_match_ptr(bu32_dt_ids),
	 },
	 .probe = bu32_spi_probe,
	 .remove = bu32_spi_remove,
 };
 
 
 
 static int __init bu32_modinit(void)
 {
	 int ret = 0;
 
	 printk("\t[bu32] %s(%d)\n", __FUNCTION__,__LINE__);
 
	 ret = spi_register_driver(&bu32_spi_driver);
	 if ( ret != 0 ) {
		 printk(KERN_ERR "Failed to register bu32 SPI driver: %d\n",  ret);
 
	 }
	 printk("spi init ret = %d \n", ret);
	 return ret;
 }
 module_init(bu32_modinit);
 
 static void __exit bu32_exit(void)
 {
	 spi_unregister_driver(&bu32_spi_driver);
 }
 module_exit(bu32_exit);
 
 MODULE_DESCRIPTION("bu32 codec driver");
 MODULE_VERSION("1.0");
 MODULE_LICENSE("GPL v2");