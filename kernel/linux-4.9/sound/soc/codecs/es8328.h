/*
 * es8328.h  --  ES8328 ALSA SoC Audio driver
 */

#ifndef _ES8328_H
#define _ES8328_H

#include <linux/regmap.h>

struct device;

extern const struct regmap_config es8328_regmap_config;
int es8328_probe(struct device *dev, struct regmap *regmap);

#define ES8328_DACLVOL 46
#define ES8328_DACRVOL 47
#define ES8328_DACCTL 28
#define ES8328_RATEMASK (0x1f << 0)

#define ES8328_CONTROL1		0x00
#define ES8328_CONTROL1_VMIDSEL_OFF (0 << 0)
#define ES8328_CONTROL1_VMIDSEL_50k (1 << 0)
#define ES8328_CONTROL1_VMIDSEL_500k (2 << 0)
#define ES8328_CONTROL1_VMIDSEL_5k (3 << 0)
#define ES8328_CONTROL1_VMIDSEL_MASK (3 << 0)
#define ES8328_CONTROL1_ENREF (1 << 2)
#define ES8328_CONTROL1_SEQEN (1 << 3)
#define ES8328_CONTROL1_SAMEFS (1 << 4)
#define ES8328_CONTROL1_DACMCLK_ADC (0 << 5)
#define ES8328_CONTROL1_DACMCLK_DAC (1 << 5)
#define ES8328_CONTROL1_LRCM (1 << 6)
#define ES8328_CONTROL1_SCP_RESET (1 << 7)

#define ES8328_CONTROL2		0x01
#define ES8328_CONTROL2_VREF_BUF_OFF (1 << 0)
#define ES8328_CONTROL2_VREF_LOWPOWER (1 << 1)
#define ES8328_CONTROL2_IBIASGEN_OFF (1 << 2)
#define ES8328_CONTROL2_ANALOG_OFF (1 << 3)
#define ES8328_CONTROL2_VREF_BUF_LOWPOWER (1 << 4)
#define ES8328_CONTROL2_VCM_MOD_LOWPOWER (1 << 5)
#define ES8328_CONTROL2_OVERCURRENT_ON (1 << 6)
#define ES8328_CONTROL2_THERMAL_SHUTDOWN_ON (1 << 7)

#define ES8328_CHIPPOWER	0x02
#define ES8328_CHIPPOWER_DACVREF_OFF 0
#define ES8328_CHIPPOWER_ADCVREF_OFF 1
#define ES8328_CHIPPOWER_DACDLL_OFF 2
#define ES8328_CHIPPOWER_ADCDLL_OFF 3
#define ES8328_CHIPPOWER_DACSTM_RESET 4
#define ES8328_CHIPPOWER_ADCSTM_RESET 5
#define ES8328_CHIPPOWER_DACDIG_OFF 6
#define ES8328_CHIPPOWER_ADCDIG_OFF 7

#define ES8328_ADCPOWER		0x03
#define ES8328_ADCPOWER_INT1_LOWPOWER 0
#define ES8328_ADCPOWER_FLASH_ADC_LOWPOWER 1
#define ES8328_ADCPOWER_ADC_BIAS_GEN_OFF 2
#define ES8328_ADCPOWER_MIC_BIAS_OFF 3
#define ES8328_ADCPOWER_ADCR_OFF 4
#define ES8328_ADCPOWER_ADCL_OFF 5
#define ES8328_ADCPOWER_AINR_OFF 6
#define ES8328_ADCPOWER_AINL_OFF 7

#define ES8328_DACPOWER		0x04
#define ES8328_DACPOWER_OUT3_ON 0
#define ES8328_DACPOWER_MONO_ON 1
#define ES8328_DACPOWER_ROUT2_ON 2
#define ES8328_DACPOWER_LOUT2_ON 3
#define ES8328_DACPOWER_ROUT1_ON 4
#define ES8328_DACPOWER_LOUT1_ON 5
#define ES8328_DACPOWER_RDAC_OFF 6
#define ES8328_DACPOWER_LDAC_OFF 7

#define ES8328_CHIPLOPOW1	0x05
#define ES8328_CHIPLOPOW2	0x06
#define ES8328_ANAVOLMANAG	0x07

#define ES8328_MASTERMODE	0x08
#define ES8328_MASTERMODE_BCLKDIV (0 << 0)
#define ES8328_MASTERMODE_BCLK_INV (1 << 5)
#define ES8328_MASTERMODE_MCLKDIV2 (1 << 6)
#define ES8328_MASTERMODE_MSC (1 << 7)

#define ES8328_ADCCONTROL1	0x09
#define ES8328_ADCCONTROL2	0x0a
#define ES8328_ADCCONTROL3	0x0b

#define ES8328_ADCCONTROL4	0x0c
#define ES8328_ADCCONTROL4_ADCFORMAT_MASK (3 << 0)
#define ES8328_ADCCONTROL4_ADCFORMAT_I2S (0 << 0)
#define ES8328_ADCCONTROL4_ADCFORMAT_LJUST (1 << 0)
#define ES8328_ADCCONTROL4_ADCFORMAT_RJUST (2 << 0)
#define ES8328_ADCCONTROL4_ADCFORMAT_PCM (3 << 0)
#define ES8328_ADCCONTROL4_ADCWL_SHIFT 2
#define ES8328_ADCCONTROL4_ADCWL_MASK (7 << 2)
#define ES8328_ADCCONTROL4_ADCLRP_I2S_POL_NORMAL (0 << 5)
#define ES8328_ADCCONTROL4_ADCLRP_I2S_POL_INV (1 << 5)
#define ES8328_ADCCONTROL4_ADCLRP_PCM_MSB_CLK2 (0 << 5)
#define ES8328_ADCCONTROL4_ADCLRP_PCM_MSB_CLK1 (1 << 5)

#define ES8328_ADCCONTROL5	0x0d
#define ES8328_ADCCONTROL5_RATEMASK (0x1f << 0)

#define ES8328_ADCCONTROL6	0x0e

#define ES8328_ADCCONTROL7	0x0f
#define ES8328_ADCCONTROL7_ADC_MUTE (1 << 2)
#define ES8328_ADCCONTROL7_ADC_LER (1 << 3)
#define ES8328_ADCCONTROL7_ADC_ZERO_CROSS (1 << 4)
#define ES8328_ADCCONTROL7_ADC_SOFT_RAMP (1 << 5)
#define ES8328_ADCCONTROL7_ADC_RAMP_RATE_4 (0 << 6)
#define ES8328_ADCCONTROL7_ADC_RAMP_RATE_8 (1 << 6)
#define ES8328_ADCCONTROL7_ADC_RAMP_RATE_16 (2 << 6)
#define ES8328_ADCCONTROL7_ADC_RAMP_RATE_32 (3 << 6)

#define ES8328_ADCCONTROL8	0x10
#define ES8328_ADCCONTROL9	0x11
#define ES8328_ADCCONTROL10	0x12
#define ES8328_ADCCONTROL11	0x13
#define ES8328_ADCCONTROL12	0x14
#define ES8328_ADCCONTROL13	0x15
#define ES8328_ADCCONTROL14	0x16

#define ES8328_DACCONTROL1	0x17
#define ES8328_DACCONTROL1_DACFORMAT_MASK (3 << 1)
#define ES8328_DACCONTROL1_DACFORMAT_I2S (0 << 1)
#define ES8328_DACCONTROL1_DACFORMAT_LJUST (1 << 1)
#define ES8328_DACCONTROL1_DACFORMAT_RJUST (2 << 1)
#define ES8328_DACCONTROL1_DACFORMAT_PCM (3 << 1)
#define ES8328_DACCONTROL1_DACWL_SHIFT 3
#define ES8328_DACCONTROL1_DACWL_MASK (7 << 3)
#define ES8328_DACCONTROL1_DACLRP_I2S_POL_NORMAL (0 << 6)
#define ES8328_DACCONTROL1_DACLRP_I2S_POL_INV (1 << 6)
#define ES8328_DACCONTROL1_DACLRP_PCM_MSB_CLK2 (0 << 6)
#define ES8328_DACCONTROL1_DACLRP_PCM_MSB_CLK1 (1 << 6)
#define ES8328_DACCONTROL1_LRSWAP (1 << 7)

#define ES8328_DACCONTROL2	0x18
#define ES8328_DACCONTROL2_RATEMASK (0x1f << 0)
#define ES8328_DACCONTROL2_DOUBLESPEED (1 << 5)

#define ES8328_DACCONTROL3	0x19
#define ES8328_DACCONTROL3_AUTOMUTE (1 << 2)
#define ES8328_DACCONTROL3_DACMUTE (1 << 2)
#define ES8328_DACCONTROL3_LEFTGAINVOL (1 << 3)
#define ES8328_DACCONTROL3_DACZEROCROSS (1 << 4)
#define ES8328_DACCONTROL3_DACSOFTRAMP (1 << 5)
#define ES8328_DACCONTROL3_DACRAMPRATE (3 << 6)

#define ES8328_LDACVOL 0x1a
#define ES8328_LDACVOL_MASK (0 << 0)
#define ES8328_LDACVOL_MAX (0xc0)

#define ES8328_RDACVOL 0x1b
#define ES8328_RDACVOL_MASK (0 << 0)
#define ES8328_RDACVOL_MAX (0xc0)

#define ES8328_DACVOL_MAX (0xc0)

#define ES8328_DACCONTROL4	0x1a
#define ES8328_DACCONTROL5	0x1b

#define ES8328_DACCONTROL6	0x1c
#define ES8328_DACCONTROL6_CLICKFREE (1 << 3)
#define ES8328_DACCONTROL6_DAC_INVR (1 << 4)
#define ES8328_DACCONTROL6_DAC_INVL (1 << 5)
#define ES8328_DACCONTROL6_DEEMPH_MASK (3 << 6)
#define ES8328_DACCONTROL6_DEEMPH_OFF (0 << 6)
#define ES8328_DACCONTROL6_DEEMPH_32k (1 << 6)
#define ES8328_DACCONTROL6_DEEMPH_44_1k (2 << 6)
#define ES8328_DACCONTROL6_DEEMPH_48k (3 << 6)

#define ES8328_DACCONTROL7	0x1d
#define ES8328_DACCONTROL7_VPP_SCALE_3p5	(0 << 0)
#define ES8328_DACCONTROL7_VPP_SCALE_4p0	(1 << 0)
#define ES8328_DACCONTROL7_VPP_SCALE_3p0	(2 << 0)
#define ES8328_DACCONTROL7_VPP_SCALE_2p5	(3 << 0)
#define ES8328_DACCONTROL7_SHELVING_STRENGTH (1 << 2) /* In eights */
#define ES8328_DACCONTROL7_MONO		(1 << 5)
#define ES8328_DACCONTROL7_ZEROR	(1 << 6)
#define ES8328_DACCONTROL7_ZEROL	(1 << 7)

/* Shelving filter */
#define ES8328_DACCONTROL8	0x1e
#define ES8328_DACCONTROL9	0x1f
#define ES8328_DACCONTROL10	0x20
#define ES8328_DACCONTROL11	0x21
#define ES8328_DACCONTROL12	0x22
#define ES8328_DACCONTROL13	0x23
#define ES8328_DACCONTROL14	0x24
#define ES8328_DACCONTROL15	0x25

#define ES8328_DACCONTROL16	0x26
#define ES8328_DACCONTROL16_RMIXSEL_RIN1 (0 << 0)
#define ES8328_DACCONTROL16_RMIXSEL_RIN2 (1 << 0)
#define ES8328_DACCONTROL16_RMIXSEL_RIN3 (2 << 0)
#define ES8328_DACCONTROL16_RMIXSEL_RADC (3 << 0)
#define ES8328_DACCONTROL16_LMIXSEL_LIN1 (0 << 3)
#define ES8328_DACCONTROL16_LMIXSEL_LIN2 (1 << 3)
#define ES8328_DACCONTROL16_LMIXSEL_LIN3 (2 << 3)
#define ES8328_DACCONTROL16_LMIXSEL_LADC (3 << 3)

#define ES8328_DACCONTROL17	0x27
#define ES8328_DACCONTROL17_LI2LOVOL (7 << 3)
#define ES8328_DACCONTROL17_LI2LO (1 << 6)
#define ES8328_DACCONTROL17_LD2LO (1 << 7)

#define ES8328_DACCONTROL18	0x28
#define ES8328_DACCONTROL18_RI2LOVOL (7 << 3)
#define ES8328_DACCONTROL18_RI2LO (1 << 6)
#define ES8328_DACCONTROL18_RD2LO (1 << 7)

#define ES8328_DACCONTROL19	0x29
#define ES8328_DACCONTROL19_LI2ROVOL (7 << 3)
#define ES8328_DACCONTROL19_LI2RO (1 << 6)
#define ES8328_DACCONTROL19_LD2RO (1 << 7)

#define ES8328_DACCONTROL20	0x2a
#define ES8328_DACCONTROL20_RI2ROVOL (7 << 3)
#define ES8328_DACCONTROL20_RI2RO (1 << 6)
#define ES8328_DACCONTROL20_RD2RO (1 << 7)

#define ES8328_DACCONTROL21	0x2b
#define ES8328_DACCONTROL21_LI2MOVOL (7 << 3)
#define ES8328_DACCONTROL21_LI2MO (1 << 6)
#define ES8328_DACCONTROL21_LD2MO (1 << 7)

#define ES8328_DACCONTROL22	0x2c
#define ES8328_DACCONTROL22_RI2MOVOL (7 << 3)
#define ES8328_DACCONTROL22_RI2MO (1 << 6)
#define ES8328_DACCONTROL22_RD2MO (1 << 7)

#define ES8328_DACCONTROL23	0x2d
#define ES8328_DACCONTROL23_MOUTINV		(1 << 1)
#define ES8328_DACCONTROL23_HPSWPOL		(1 << 2)
#define ES8328_DACCONTROL23_HPSWEN		(1 << 3)
#define ES8328_DACCONTROL23_VROI_1p5k		(0 << 4)
#define ES8328_DACCONTROL23_VROI_40k		(1 << 4)
#define ES8328_DACCONTROL23_OUT3_VREF		(0 << 5)
#define ES8328_DACCONTROL23_OUT3_ROUT1		(1 << 5)
#define ES8328_DACCONTROL23_OUT3_MONOOUT	(2 << 5)
#define ES8328_DACCONTROL23_OUT3_RIGHT_MIXER	(3 << 5)
#define ES8328_DACCONTROL23_ROUT2INV		(1 << 7)

/* LOUT1 Amplifier */
#define ES8328_LOUT1VOL 0x2e
#define ES8328_LOUT1VOL_MASK (0 << 5)
#define ES8328_LOUT1VOL_MAX (0x24)

/* ROUT1 Amplifier */
#define ES8328_ROUT1VOL 0x2f
#define ES8328_ROUT1VOL_MASK (0 << 5)
#define ES8328_ROUT1VOL_MAX (0x24)

#define ES8328_OUT1VOL_MAX (0x24)

/* LOUT2 Amplifier */
#define ES8328_LOUT2VOL 0x30
#define ES8328_LOUT2VOL_MASK (0 << 5)
#define ES8328_LOUT2VOL_MAX (0x24)

/* ROUT2 Amplifier */
#define ES8328_ROUT2VOL 0x31
#define ES8328_ROUT2VOL_MASK (0 << 5)
#define ES8328_ROUT2VOL_MAX (0x24)

#define ES8328_OUT2VOL_MAX (0x24)

/* Mono Out Amplifier */
#define ES8328_MONOOUTVOL 0x32
#define ES8328_MONOOUTVOL_MASK (0 << 5)
#define ES8328_MONOOUTVOL_MAX (0x24)

#define ES8328_DACCONTROL29	0x33
#define ES8328_DACCONTROL30	0x34

#define ES8328_SYSCLK		0

#define ES8328_REG_MAX		0x35

#define ES8328_PLL1		0
#define ES8328_PLL2		1

/* clock inputs */
#define ES8328_MCLK		0
#define ES8328_PCMCLK		1

/* clock divider id's */
#define ES8328_PCMDIV		0
#define ES8328_BCLKDIV		1
#define ES8328_VXCLKDIV		2

/* PCM clock dividers */
#define ES8328_PCM_DIV_1	(0 << 6)
#define ES8328_PCM_DIV_3	(2 << 6)
#define ES8328_PCM_DIV_5_5	(3 << 6)
#define ES8328_PCM_DIV_2	(4 << 6)
#define ES8328_PCM_DIV_4	(5 << 6)
#define ES8328_PCM_DIV_6	(6 << 6)
#define ES8328_PCM_DIV_8	(7 << 6)

/* BCLK clock dividers */
#define ES8328_BCLK_DIV_1	(0 << 7)
#define ES8328_BCLK_DIV_2	(1 << 7)
#define ES8328_BCLK_DIV_4	(2 << 7)
#define ES8328_BCLK_DIV_8	(3 << 7)

/* VXCLK clock dividers */
#define ES8328_VXCLK_DIV_1	(0 << 6)
#define ES8328_VXCLK_DIV_2	(1 << 6)
#define ES8328_VXCLK_DIV_4	(2 << 6)
#define ES8328_VXCLK_DIV_8	(3 << 6)
#define ES8328_VXCLK_DIV_16	(4 << 6)

#define ES8328_DAI_HIFI		0
#define ES8328_DAI_VOICE	1

#define ES8328_1536FS		1536
#define ES8328_1024FS		1024
#define ES8328_768FS		768
#define ES8328_512FS		512
#define ES8328_384FS		384
#define ES8328_256FS		256
#define ES8328_128FS		128

#endif
