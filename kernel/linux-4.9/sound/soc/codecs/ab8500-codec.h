/*
 * Copyright (C) ST-<PERSON><PERSON> SA 2012
 *
 * Author: <PERSON><PERSON> <<EMAIL>>,
 *         <PERSON><PERSON><PERSON> <kristoffer.ka<PERSON><PERSON>@stericsson.com>,
 *         <PERSON> <<EMAIL>>,
 *         for <PERSON><PERSON><PERSON><PERSON>.
 *
 *         Based on the early work done by:
 *         <PERSON><PERSON><PERSON> <<EMAIL>>,
 *         <PERSON><PERSON><PERSON> <<EMAIL>>,
 *         for <PERSON><PERSON><PERSON><PERSON>.
 *
 * License terms:
 *
 * This program is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License version 2 as published
 * by the Free Software Foundation.
 */

#ifndef AB8500_CODEC_REGISTERS_H
#define AB8500_CODEC_REGISTERS_H

#define AB8500_SUPPORTED_RATE			(SNDRV_PCM_RATE_48000)
#define AB8500_SUPPORTED_FMT			(SNDRV_PCM_FMTBIT_S16_LE)

/* AB8500 interface slot offset definitions */

#define AB8500_AD_DATA0_OFFSET	0
#define AB8500_DA_DATA0_OFFSET	8
#define AB8500_AD_DATA1_OFFSET	16
#define AB8500_DA_DATA1_OFFSET	24

/* AB8500 audio bank (0x0d) register definitions */

#define AB8500_POWERUP				0x00
#define AB8500_AUDSWRESET			0x01
#define AB8500_ADPATHENA			0x02
#define AB8500_DAPATHENA			0x03
#define AB8500_ANACONF1				0x04
#define AB8500_ANACONF2				0x05
#define AB8500_DIGMICCONF			0x06
#define AB8500_ANACONF3				0x07
#define AB8500_ANACONF4				0x08
#define AB8500_DAPATHCONF			0x09
#define AB8500_MUTECONF				0x0A
#define AB8500_SHORTCIRCONF			0x0B
#define AB8500_ANACONF5				0x0C
#define AB8500_ENVCPCONF			0x0D
#define AB8500_SIGENVCONF			0x0E
#define AB8500_PWMGENCONF1			0x0F
#define AB8500_PWMGENCONF2			0x10
#define AB8500_PWMGENCONF3			0x11
#define AB8500_PWMGENCONF4			0x12
#define AB8500_PWMGENCONF5			0x13
#define AB8500_ANAGAIN1				0x14
#define AB8500_ANAGAIN2				0x15
#define AB8500_ANAGAIN3				0x16
#define AB8500_ANAGAIN4				0x17
#define AB8500_DIGLINHSLGAIN			0x18
#define AB8500_DIGLINHSRGAIN			0x19
#define AB8500_ADFILTCONF			0x1A
#define AB8500_DIGIFCONF1			0x1B
#define AB8500_DIGIFCONF2			0x1C
#define AB8500_DIGIFCONF3			0x1D
#define AB8500_DIGIFCONF4			0x1E
#define AB8500_ADSLOTSEL1			0x1F
#define AB8500_ADSLOTSEL2			0x20
#define AB8500_ADSLOTSEL3			0x21
#define AB8500_ADSLOTSEL4			0x22
#define AB8500_ADSLOTSEL5			0x23
#define AB8500_ADSLOTSEL6			0x24
#define AB8500_ADSLOTSEL7			0x25
#define AB8500_ADSLOTSEL8			0x26
#define AB8500_ADSLOTSEL9			0x27
#define AB8500_ADSLOTSEL10			0x28
#define AB8500_ADSLOTSEL11			0x29
#define AB8500_ADSLOTSEL12			0x2A
#define AB8500_ADSLOTSEL13			0x2B
#define AB8500_ADSLOTSEL14			0x2C
#define AB8500_ADSLOTSEL15			0x2D
#define AB8500_ADSLOTSEL16			0x2E
#define AB8500_ADSLOTSEL(slot)			(AB8500_ADSLOTSEL1 + (slot >> 1))
#define AB8500_ADSLOTHIZCTRL1			0x2F
#define AB8500_ADSLOTHIZCTRL2			0x30
#define AB8500_ADSLOTHIZCTRL3			0x31
#define AB8500_ADSLOTHIZCTRL4			0x32
#define AB8500_DASLOTCONF1			0x33
#define AB8500_DASLOTCONF2			0x34
#define AB8500_DASLOTCONF3			0x35
#define AB8500_DASLOTCONF4			0x36
#define AB8500_DASLOTCONF5			0x37
#define AB8500_DASLOTCONF6			0x38
#define AB8500_DASLOTCONF7			0x39
#define AB8500_DASLOTCONF8			0x3A
#define AB8500_CLASSDCONF1			0x3B
#define AB8500_CLASSDCONF2			0x3C
#define AB8500_CLASSDCONF3			0x3D
#define AB8500_DMICFILTCONF			0x3E
#define AB8500_DIGMULTCONF1			0x3F
#define AB8500_DIGMULTCONF2			0x40
#define AB8500_ADDIGGAIN1			0x41
#define AB8500_ADDIGGAIN2			0x42
#define AB8500_ADDIGGAIN3			0x43
#define AB8500_ADDIGGAIN4			0x44
#define AB8500_ADDIGGAIN5			0x45
#define AB8500_ADDIGGAIN6			0x46
#define AB8500_DADIGGAIN1			0x47
#define AB8500_DADIGGAIN2			0x48
#define AB8500_DADIGGAIN3			0x49
#define AB8500_DADIGGAIN4			0x4A
#define AB8500_DADIGGAIN5			0x4B
#define AB8500_DADIGGAIN6			0x4C
#define AB8500_ADDIGLOOPGAIN1			0x4D
#define AB8500_ADDIGLOOPGAIN2			0x4E
#define AB8500_HSLEARDIGGAIN			0x4F
#define AB8500_HSRDIGGAIN			0x50
#define AB8500_SIDFIRGAIN1			0x51
#define AB8500_SIDFIRGAIN2			0x52
#define AB8500_ANCCONF1				0x53
#define AB8500_ANCCONF2				0x54
#define AB8500_ANCCONF3				0x55
#define AB8500_ANCCONF4				0x56
#define AB8500_ANCCONF5				0x57
#define AB8500_ANCCONF6				0x58
#define AB8500_ANCCONF7				0x59
#define AB8500_ANCCONF8				0x5A
#define AB8500_ANCCONF9				0x5B
#define AB8500_ANCCONF10			0x5C
#define AB8500_ANCCONF11			0x5D
#define AB8500_ANCCONF12			0x5E
#define AB8500_ANCCONF13			0x5F
#define AB8500_ANCCONF14			0x60
#define AB8500_SIDFIRADR			0x61
#define AB8500_SIDFIRCOEF1			0x62
#define AB8500_SIDFIRCOEF2			0x63
#define AB8500_SIDFIRCONF			0x64
#define AB8500_AUDINTMASK1			0x65
#define AB8500_AUDINTSOURCE1			0x66
#define AB8500_AUDINTMASK2			0x67
#define AB8500_AUDINTSOURCE2			0x68
#define AB8500_FIFOCONF1			0x69
#define AB8500_FIFOCONF2			0x6A
#define AB8500_FIFOCONF3			0x6B
#define AB8500_FIFOCONF4			0x6C
#define AB8500_FIFOCONF5			0x6D
#define AB8500_FIFOCONF6			0x6E
#define AB8500_AUDREV				0x6F

#define AB8500_FIRST_REG			AB8500_POWERUP
#define AB8500_LAST_REG				AB8500_AUDREV
#define AB8500_CACHEREGNUM			(AB8500_LAST_REG + 1)

#define AB8500_MASK_ALL				0xFF
#define AB8500_MASK_SLOT(slot)			((slot & 1) ? 0xF0 : 0x0F)
#define AB8500_MASK_NONE			0x00

/* AB8500_POWERUP */
#define AB8500_POWERUP_POWERUP			7
#define AB8500_POWERUP_ENANA			3

/* AB8500_AUDSWRESET */
#define AB8500_AUDSWRESET_SWRESET		7

/* AB8500_ADPATHENA */
#define AB8500_ADPATHENA_ENAD12			7
#define AB8500_ADPATHENA_ENAD34			5
#define AB8500_ADPATHENA_ENAD5768		3

/* AB8500_DAPATHENA */
#define AB8500_DAPATHENA_ENDA1			7
#define AB8500_DAPATHENA_ENDA2			6
#define AB8500_DAPATHENA_ENDA3			5
#define AB8500_DAPATHENA_ENDA4			4
#define AB8500_DAPATHENA_ENDA5			3
#define AB8500_DAPATHENA_ENDA6			2

/* AB8500_ANACONF1 */
#define AB8500_ANACONF1_HSLOWPOW		7
#define AB8500_ANACONF1_DACLOWPOW1		6
#define AB8500_ANACONF1_DACLOWPOW0		5
#define AB8500_ANACONF1_EARDACLOWPOW		4
#define AB8500_ANACONF1_EARSELCM		2
#define AB8500_ANACONF1_HSHPEN			1
#define AB8500_ANACONF1_EARDRVLOWPOW		0

/* AB8500_ANACONF2 */
#define AB8500_ANACONF2_ENMIC1			7
#define AB8500_ANACONF2_ENMIC2			6
#define AB8500_ANACONF2_ENLINL			5
#define AB8500_ANACONF2_ENLINR			4
#define AB8500_ANACONF2_MUTMIC1			3
#define AB8500_ANACONF2_MUTMIC2			2
#define AB8500_ANACONF2_MUTLINL			1
#define AB8500_ANACONF2_MUTLINR			0

/* AB8500_DIGMICCONF */
#define AB8500_DIGMICCONF_ENDMIC1		7
#define AB8500_DIGMICCONF_ENDMIC2		6
#define AB8500_DIGMICCONF_ENDMIC3		5
#define AB8500_DIGMICCONF_ENDMIC4		4
#define AB8500_DIGMICCONF_ENDMIC5		3
#define AB8500_DIGMICCONF_ENDMIC6		2
#define AB8500_DIGMICCONF_HSFADSPEED		0

/* AB8500_ANACONF3 */
#define AB8500_ANACONF3_MIC1SEL			7
#define AB8500_ANACONF3_LINRSEL			6
#define AB8500_ANACONF3_ENDRVHSL		5
#define AB8500_ANACONF3_ENDRVHSR		4
#define AB8500_ANACONF3_ENADCMIC		2
#define AB8500_ANACONF3_ENADCLINL		1
#define AB8500_ANACONF3_ENADCLINR		0

/* AB8500_ANACONF4 */
#define AB8500_ANACONF4_DISPDVSS		7
#define AB8500_ANACONF4_ENEAR			6
#define AB8500_ANACONF4_ENHSL			5
#define AB8500_ANACONF4_ENHSR			4
#define AB8500_ANACONF4_ENHFL			3
#define AB8500_ANACONF4_ENHFR			2
#define AB8500_ANACONF4_ENVIB1			1
#define AB8500_ANACONF4_ENVIB2			0

/* AB8500_DAPATHCONF */
#define AB8500_DAPATHCONF_ENDACEAR		6
#define AB8500_DAPATHCONF_ENDACHSL		5
#define AB8500_DAPATHCONF_ENDACHSR		4
#define AB8500_DAPATHCONF_ENDACHFL		3
#define AB8500_DAPATHCONF_ENDACHFR		2
#define AB8500_DAPATHCONF_ENDACVIB1		1
#define AB8500_DAPATHCONF_ENDACVIB2		0

/* AB8500_MUTECONF */
#define AB8500_MUTECONF_MUTEAR			6
#define AB8500_MUTECONF_MUTHSL			5
#define AB8500_MUTECONF_MUTHSR			4
#define AB8500_MUTECONF_MUTDACEAR		2
#define AB8500_MUTECONF_MUTDACHSL		1
#define AB8500_MUTECONF_MUTDACHSR		0

/* AB8500_SHORTCIRCONF */
#define AB8500_SHORTCIRCONF_ENSHORTPWD		7
#define AB8500_SHORTCIRCONF_EARSHORTDIS		6
#define AB8500_SHORTCIRCONF_HSSHORTDIS		5
#define AB8500_SHORTCIRCONF_HSPULLDEN		4
#define AB8500_SHORTCIRCONF_HSOSCEN		2
#define AB8500_SHORTCIRCONF_HSFADDIS		1
#define AB8500_SHORTCIRCONF_HSZCDDIS		0
/* Zero cross should be disabled */

/* AB8500_ANACONF5 */
#define AB8500_ANACONF5_ENCPHS			7
#define AB8500_ANACONF5_HSLDACTOLOL		5
#define AB8500_ANACONF5_HSRDACTOLOR		4
#define AB8500_ANACONF5_ENLOL			3
#define AB8500_ANACONF5_ENLOR			2
#define AB8500_ANACONF5_HSAUTOEN		0

/* AB8500_ENVCPCONF */
#define AB8500_ENVCPCONF_ENVDETHTHRE		4
#define AB8500_ENVCPCONF_ENVDETLTHRE		0
#define AB8500_ENVCPCONF_ENVDETHTHRE_MAX	0x0F
#define AB8500_ENVCPCONF_ENVDETLTHRE_MAX	0x0F

/* AB8500_SIGENVCONF */
#define AB8500_SIGENVCONF_CPLVEN		5
#define AB8500_SIGENVCONF_ENVDETCPEN		4
#define AB8500_SIGENVCONF_ENVDETTIME		0
#define AB8500_SIGENVCONF_ENVDETTIME_MAX	0x0F

/* AB8500_PWMGENCONF1 */
#define AB8500_PWMGENCONF1_PWMTOVIB1		7
#define AB8500_PWMGENCONF1_PWMTOVIB2		6
#define AB8500_PWMGENCONF1_PWM1CTRL		5
#define AB8500_PWMGENCONF1_PWM2CTRL		4
#define AB8500_PWMGENCONF1_PWM1NCTRL		3
#define AB8500_PWMGENCONF1_PWM1PCTRL		2
#define AB8500_PWMGENCONF1_PWM2NCTRL		1
#define AB8500_PWMGENCONF1_PWM2PCTRL		0

/* AB8500_PWMGENCONF2 */
/* AB8500_PWMGENCONF3 */
/* AB8500_PWMGENCONF4 */
/* AB8500_PWMGENCONF5 */
#define AB8500_PWMGENCONFX_PWMVIBXPOL		7
#define AB8500_PWMGENCONFX_PWMVIBXDUTCYC	0
#define AB8500_PWMGENCONFX_PWMVIBXDUTCYC_MAX	0x64

/* AB8500_ANAGAIN1 */
/* AB8500_ANAGAIN2 */
#define AB8500_ANAGAINX_ENSEMICX		7
#define AB8500_ANAGAINX_LOWPOWMICX		6
#define AB8500_ANAGAINX_MICXGAIN		0
#define AB8500_ANAGAINX_MICXGAIN_MAX		0x1F

/* AB8500_ANAGAIN3 */
#define AB8500_ANAGAIN3_HSLGAIN			4
#define AB8500_ANAGAIN3_HSRGAIN			0
#define AB8500_ANAGAIN3_HSXGAIN_MAX		0x0F

/* AB8500_ANAGAIN4 */
#define AB8500_ANAGAIN4_LINLGAIN		4
#define AB8500_ANAGAIN4_LINRGAIN		0
#define AB8500_ANAGAIN4_LINXGAIN_MAX		0x0F

/* AB8500_DIGLINHSLGAIN */
/* AB8500_DIGLINHSRGAIN */
#define AB8500_DIGLINHSXGAIN_LINTOHSXGAIN	0
#define AB8500_DIGLINHSXGAIN_LINTOHSXGAIN_MAX	0x13

/* AB8500_ADFILTCONF */
#define AB8500_ADFILTCONF_AD1NH			7
#define AB8500_ADFILTCONF_AD2NH			6
#define AB8500_ADFILTCONF_AD3NH			5
#define AB8500_ADFILTCONF_AD4NH			4
#define AB8500_ADFILTCONF_AD1VOICE		3
#define AB8500_ADFILTCONF_AD2VOICE		2
#define AB8500_ADFILTCONF_AD3VOICE		1
#define AB8500_ADFILTCONF_AD4VOICE		0

/* AB8500_DIGIFCONF1 */
#define AB8500_DIGIFCONF1_ENMASTGEN		7
#define AB8500_DIGIFCONF1_IF1BITCLKOS1		6
#define AB8500_DIGIFCONF1_IF1BITCLKOS0		5
#define AB8500_DIGIFCONF1_ENFSBITCLK1		4
#define AB8500_DIGIFCONF1_IF0BITCLKOS1		2
#define AB8500_DIGIFCONF1_IF0BITCLKOS0		1
#define AB8500_DIGIFCONF1_ENFSBITCLK0		0

/* AB8500_DIGIFCONF2 */
#define AB8500_DIGIFCONF2_FSYNC0P		6
#define AB8500_DIGIFCONF2_BITCLK0P		5
#define AB8500_DIGIFCONF2_IF0DEL		4
#define AB8500_DIGIFCONF2_IF0FORMAT1		3
#define AB8500_DIGIFCONF2_IF0FORMAT0		2
#define AB8500_DIGIFCONF2_IF0WL1		1
#define AB8500_DIGIFCONF2_IF0WL0		0

/* AB8500_DIGIFCONF3 */
#define AB8500_DIGIFCONF3_IF0DATOIF1AD		7
#define AB8500_DIGIFCONF3_IF0CLKTOIF1CLK	6
#define AB8500_DIGIFCONF3_IF1MASTER		5
#define AB8500_DIGIFCONF3_IF1DATOIF0AD		3
#define AB8500_DIGIFCONF3_IF1CLKTOIF0CLK	2
#define AB8500_DIGIFCONF3_IF0MASTER		1
#define AB8500_DIGIFCONF3_IF0BFIFOEN		0

/* AB8500_DIGIFCONF4 */
#define AB8500_DIGIFCONF4_FSYNC1P		6
#define AB8500_DIGIFCONF4_BITCLK1P		5
#define AB8500_DIGIFCONF4_IF1DEL		4
#define AB8500_DIGIFCONF4_IF1FORMAT1		3
#define AB8500_DIGIFCONF4_IF1FORMAT0		2
#define AB8500_DIGIFCONF4_IF1WL1		1
#define AB8500_DIGIFCONF4_IF1WL0		0

/* AB8500_ADSLOTSELX */
#define AB8500_AD_OUT1	0x0
#define AB8500_AD_OUT2	0x1
#define AB8500_AD_OUT3	0x2
#define AB8500_AD_OUT4	0x3
#define AB8500_AD_OUT5	0x4
#define AB8500_AD_OUT6	0x5
#define AB8500_AD_OUT7	0x6
#define AB8500_AD_OUT8	0x7
#define AB8500_ZEROES	0x8
#define AB8500_TRISTATE	0xF
#define AB8500_ADSLOTSELX_EVEN_SHIFT		0
#define AB8500_ADSLOTSELX_ODD_SHIFT		4
#define AB8500_ADSLOTSELX_AD_OUT_TO_SLOT(out, slot)	\
	((out) << (((slot) & 1) ? \
	 AB8500_ADSLOTSELX_ODD_SHIFT : AB8500_ADSLOTSELX_EVEN_SHIFT))

/* AB8500_ADSLOTHIZCTRL1 */
/* AB8500_ADSLOTHIZCTRL2 */
/* AB8500_ADSLOTHIZCTRL3 */
/* AB8500_ADSLOTHIZCTRL4 */
/* AB8500_DASLOTCONF1 */
#define AB8500_DASLOTCONF1_DA12VOICE		7
#define AB8500_DASLOTCONF1_SWAPDA12_34		6
#define AB8500_DASLOTCONF1_DAI7TOADO1		5

/* AB8500_DASLOTCONF2 */
#define AB8500_DASLOTCONF2_DAI8TOADO2		5

/* AB8500_DASLOTCONF3 */
#define AB8500_DASLOTCONF3_DA34VOICE		7
#define AB8500_DASLOTCONF3_DAI7TOADO3		5

/* AB8500_DASLOTCONF4 */
#define AB8500_DASLOTCONF4_DAI8TOADO4		5

/* AB8500_DASLOTCONF5 */
#define AB8500_DASLOTCONF5_DA56VOICE		7
#define AB8500_DASLOTCONF5_DAI7TOADO5		5

/* AB8500_DASLOTCONF6 */
#define AB8500_DASLOTCONF6_DAI8TOADO6		5

/* AB8500_DASLOTCONF7 */
#define AB8500_DASLOTCONF7_DAI8TOADO7		5

/* AB8500_DASLOTCONF8 */
#define AB8500_DASLOTCONF8_DAI7TOADO8		5

#define AB8500_DASLOTCONFX_SLTODAX_SHIFT	0
#define AB8500_DASLOTCONFX_SLTODAX_MASK		0x1F

/* AB8500_CLASSDCONF1 */
#define AB8500_CLASSDCONF1_PARLHF		7
#define AB8500_CLASSDCONF1_PARLVIB		6
#define AB8500_CLASSDCONF1_VIB1SWAPEN		3
#define AB8500_CLASSDCONF1_VIB2SWAPEN		2
#define AB8500_CLASSDCONF1_HFLSWAPEN		1
#define AB8500_CLASSDCONF1_HFRSWAPEN		0

/* AB8500_CLASSDCONF2 */
#define AB8500_CLASSDCONF2_FIRBYP3		7
#define AB8500_CLASSDCONF2_FIRBYP2		6
#define AB8500_CLASSDCONF2_FIRBYP1		5
#define AB8500_CLASSDCONF2_FIRBYP0		4
#define AB8500_CLASSDCONF2_HIGHVOLEN3		3
#define AB8500_CLASSDCONF2_HIGHVOLEN2		2
#define AB8500_CLASSDCONF2_HIGHVOLEN1		1
#define AB8500_CLASSDCONF2_HIGHVOLEN0		0

/* AB8500_CLASSDCONF3 */
#define AB8500_CLASSDCONF3_DITHHPGAIN		4
#define AB8500_CLASSDCONF3_DITHHPGAIN_MAX	0x0A
#define AB8500_CLASSDCONF3_DITHWGAIN		0
#define AB8500_CLASSDCONF3_DITHWGAIN_MAX	0x0A

/* AB8500_DMICFILTCONF */
#define AB8500_DMICFILTCONF_ANCINSEL		7
#define AB8500_DMICFILTCONF_DA3TOEAR		6
#define AB8500_DMICFILTCONF_DMIC1SINC3		5
#define AB8500_DMICFILTCONF_DMIC2SINC3		4
#define AB8500_DMICFILTCONF_DMIC3SINC3		3
#define AB8500_DMICFILTCONF_DMIC4SINC3		2
#define AB8500_DMICFILTCONF_DMIC5SINC3		1
#define AB8500_DMICFILTCONF_DMIC6SINC3		0

/* AB8500_DIGMULTCONF1 */
#define AB8500_DIGMULTCONF1_DATOHSLEN		7
#define AB8500_DIGMULTCONF1_DATOHSREN		6
#define AB8500_DIGMULTCONF1_AD1SEL		5
#define AB8500_DIGMULTCONF1_AD2SEL		4
#define AB8500_DIGMULTCONF1_AD3SEL		3
#define AB8500_DIGMULTCONF1_AD5SEL		2
#define AB8500_DIGMULTCONF1_AD6SEL		1
#define AB8500_DIGMULTCONF1_ANCSEL		0

/* AB8500_DIGMULTCONF2 */
#define AB8500_DIGMULTCONF2_DATOHFREN		7
#define AB8500_DIGMULTCONF2_DATOHFLEN		6
#define AB8500_DIGMULTCONF2_HFRSEL		5
#define AB8500_DIGMULTCONF2_HFLSEL		4
#define AB8500_DIGMULTCONF2_FIRSID1SEL		2
#define AB8500_DIGMULTCONF2_FIRSID2SEL		0

/* AB8500_ADDIGGAIN1 */
/* AB8500_ADDIGGAIN2 */
/* AB8500_ADDIGGAIN3 */
/* AB8500_ADDIGGAIN4 */
/* AB8500_ADDIGGAIN5 */
/* AB8500_ADDIGGAIN6 */
#define AB8500_ADDIGGAINX_FADEDISADX		6
#define AB8500_ADDIGGAINX_ADXGAIN_MAX		0x3F

/* AB8500_DADIGGAIN1 */
/* AB8500_DADIGGAIN2 */
/* AB8500_DADIGGAIN3 */
/* AB8500_DADIGGAIN4 */
/* AB8500_DADIGGAIN5 */
/* AB8500_DADIGGAIN6 */
#define AB8500_DADIGGAINX_FADEDISDAX		6
#define AB8500_DADIGGAINX_DAXGAIN_MAX		0x3F

/* AB8500_ADDIGLOOPGAIN1 */
/* AB8500_ADDIGLOOPGAIN2 */
#define AB8500_ADDIGLOOPGAINX_FADEDISADXL	6
#define AB8500_ADDIGLOOPGAINX_ADXLBGAIN_MAX	0x3F

/* AB8500_HSLEARDIGGAIN */
#define AB8500_HSLEARDIGGAIN_HSSINC1		7
#define AB8500_HSLEARDIGGAIN_FADEDISHSL		4
#define AB8500_HSLEARDIGGAIN_HSLDGAIN_MAX	0x09

/* AB8500_HSRDIGGAIN */
#define AB8500_HSRDIGGAIN_FADESPEED		6
#define AB8500_HSRDIGGAIN_FADEDISHSR		4
#define AB8500_HSRDIGGAIN_HSRDGAIN_MAX		0x09

/* AB8500_SIDFIRGAIN1 */
/* AB8500_SIDFIRGAIN2 */
#define AB8500_SIDFIRGAINX_FIRSIDXGAIN_MAX	0x1F

/* AB8500_ANCCONF1 */
#define AB8500_ANCCONF1_ANCIIRUPDATE		3
#define AB8500_ANCCONF1_ENANC			2
#define AB8500_ANCCONF1_ANCIIRINIT		1
#define AB8500_ANCCONF1_ANCFIRUPDATE		0

/* AB8500_ANCCONF2 */
#define AB8500_ANCCONF2_SHIFT			5
#define AB8500_ANCCONF2_MIN			-0x10
#define AB8500_ANCCONF2_MAX			0xF

/* AB8500_ANCCONF3 */
#define AB8500_ANCCONF3_SHIFT			5
#define AB8500_ANCCONF3_MIN			-0x10
#define AB8500_ANCCONF3_MAX			0xF

/* AB8500_ANCCONF4 */
#define AB8500_ANCCONF4_SHIFT			5
#define AB8500_ANCCONF4_MIN			-0x10
#define AB8500_ANCCONF4_MAX			0xF

/* AB8500_ANC_FIR_COEFFS */
#define AB8500_ANC_FIR_COEFF_MIN		-0x8000
#define AB8500_ANC_FIR_COEFF_MAX		0x7FFF
#define AB8500_ANC_FIR_COEFFS			15

/* AB8500_ANC_IIR_COEFFS */
#define AB8500_ANC_IIR_COEFF_MIN		-0x800000
#define AB8500_ANC_IIR_COEFF_MAX		0x7FFFFF
#define AB8500_ANC_IIR_COEFFS			24
/* AB8500_ANC_WARP_DELAY */
#define AB8500_ANC_WARP_DELAY_SHIFT		16
#define AB8500_ANC_WARP_DELAY_MIN		0x0000
#define AB8500_ANC_WARP_DELAY_MAX		0xFFFF

/* AB8500_ANCCONF11 */
/* AB8500_ANCCONF12 */
/* AB8500_ANCCONF13 */
/* AB8500_ANCCONF14 */

/* AB8500_SIDFIRADR */
#define AB8500_SIDFIRADR_FIRSIDSET		7
#define AB8500_SIDFIRADR_ADDRESS_SHIFT		0
#define AB8500_SIDFIRADR_ADDRESS_MAX		0x7F

/* AB8500_SIDFIRCOEF1 */
/* AB8500_SIDFIRCOEF2 */
#define AB8500_SID_FIR_COEFF_MIN		0
#define AB8500_SID_FIR_COEFF_MAX		0xFFFF
#define AB8500_SID_FIR_COEFFS			128

/* AB8500_SIDFIRCONF */
#define AB8500_SIDFIRCONF_ENFIRSIDS		2
#define AB8500_SIDFIRCONF_FIRSIDSTOIF1		1
#define AB8500_SIDFIRCONF_FIRSIDBUSY		0

/* AB8500_AUDINTMASK1 */
/* AB8500_AUDINTSOURCE1 */
/* AB8500_AUDINTMASK2 */
/* AB8500_AUDINTSOURCE2 */

/* AB8500_FIFOCONF1 */
#define AB8500_FIFOCONF1_BFIFOMASK		0x80
#define AB8500_FIFOCONF1_BFIFO19M2		0x40
#define AB8500_FIFOCONF1_BFIFOINT_SHIFT		0
#define AB8500_FIFOCONF1_BFIFOINT_MAX		0x3F

/* AB8500_FIFOCONF2 */
#define AB8500_FIFOCONF2_BFIFOTX_SHIFT		0
#define AB8500_FIFOCONF2_BFIFOTX_MAX		0xFF

/* AB8500_FIFOCONF3 */
#define AB8500_FIFOCONF3_BFIFOEXSL_SHIFT	5
#define AB8500_FIFOCONF3_BFIFOEXSL_MAX		0x5
#define AB8500_FIFOCONF3_PREBITCLK0_SHIFT	2
#define AB8500_FIFOCONF3_PREBITCLK0_MAX		0x7
#define AB8500_FIFOCONF3_BFIFOMAST_SHIFT	1
#define AB8500_FIFOCONF3_BFIFORUN_SHIFT		0

/* AB8500_FIFOCONF4 */
#define AB8500_FIFOCONF4_BFIFOFRAMSW_SHIFT	0
#define AB8500_FIFOCONF4_BFIFOFRAMSW_MAX	0xFF

/* AB8500_FIFOCONF5 */
#define AB8500_FIFOCONF5_BFIFOWAKEUP_SHIFT	0
#define AB8500_FIFOCONF5_BFIFOWAKEUP_MAX	0xFF

/* AB8500_FIFOCONF6 */
#define AB8500_FIFOCONF6_BFIFOSAMPLE_SHIFT	0
#define AB8500_FIFOCONF6_BFIFOSAMPLE_MAX	0xFF

/* AB8500_AUDREV */

#endif
