#ifndef __BU32__H__
#define __BU32__H__
#define BU32_RATES		(SNDRV_PCM_RATE_8000 | SNDRV_PCM_RATE_11025 |\
				SNDRV_PCM_RATE_16000 | SNDRV_PCM_RATE_22050 |\
				SNDRV_PCM_RATE_32000 | SNDRV_PCM_RATE_44100 |\
				SNDRV_PCM_RATE_48000 | SNDRV_PCM_RATE_88200 |\
				SNDRV_PCM_RATE_96000 | SNDRV_PCM_RATE_176400 |\
				SNDRV_PCM_RATE_192000)
//				SNDRV_PCM_RATE_192000| SNDRV_PCM_RATE_24000)

#define BU32_FORMATS	 SNDRV_PCM_FMTBIT_S16_LE | SNDRV_PCM_FMTBIT_S24_LE | SNDRV_PCM_FMTBIT_S32_LE

static int bu32_set_dai_sysclk(struct snd_soc_dai *dai, int clk_id, unsigned int freq, int dir);
static int bu32_set_dai_mute(struct snd_soc_dai *dai, int mute);
static int bu32_set_dai_fmt(struct snd_soc_dai *dai, unsigned int fmt);
static int bu32_hw_params(struct snd_pcm_substream *substream,struct snd_pcm_hw_params *params, struct snd_soc_dai *dai);
static int bu32_set_bias_level(struct snd_soc_codec *codec, enum snd_soc_bias_level level);
enum {
	AIF_PORT1 = 0,
	AIF_PORT2,
	AIF_PORT3,
	AIF_PORT4,
	AIF_PORT5,
	NUM_AIF_DAIS,
};

#define BU32_MAX_REGISTER         0x0B00
#define READ_MODE_HEADER_FIRST_STEP_LEN  4
#define READ_MODE_HEADER_SEROND_STEP_LEN 2
#define SPI_SPEED         500000
#define SPI_MODE          SPI_MODE_0
#define SPI_BITS_PER_WORD 8
#define ADDRESS_LEN 2
#define TRANSFER_LEN 1


static const struct reg_default default_reg[] = {
    {0x0001,0x0C},
    {0x0002,0x00},
    {0x0003,0x00},
    {0x0004,0x00},
    {0x0005,0x00},
    {0x0006,0x33},
    {0x0007,0xFF},
    {0x0008,0xF3},
    {0x0010,0x0F},
    {0x0011,0x0C},
    {0x0012,0x0C},
    {0x0013,0x0C},
    {0x0014,0x00},
    {0x0015,0x00},
    {0x0016,0x0C},
    {0x0017,0x00},
    {0x0018,0x00},
    {0x0019,0x00},
    {0x001A,0x00},
    {0x001B,0x00},
    {0x001C,0x00},
    {0x001D,0x00},
    {0x001E,0xC0},
    {0x001F,0x00},
    {0x0020,0xC0},
    {0x0021,0x34},
    {0x0022,0x05},
    {0x0101,0x00},
    {0x0102,0x00},
    {0x0103,0x04},
    {0x0104,0x11},
    {0x0105,0x01},
    {0x0106,0xBC},
    {0x0107,0xA0},
    {0x0108,0xA0},
    {0x0109,0x80},
    {0x010A,0x80},
    {0x0200,0x00},
    {0x0201,0x00},
    {0x0202,0x88},
    {0x0203,0x00},
    {0x0204,0x00},
    {0x0205,0x05},
    {0x0206,0x00},
    {0x0207,0x00},
    {0x0208,0x00},
    {0x0400,0x00},
    {0x0401,0x00},
    {0x0402,0x00},
    {0x0403,0x00},
    {0x0404,0x00},
    {0x0405,0x00},
    {0x0406,0x00},
    {0x0407,0x00},
    {0x0408,0x00},
    {0x0409,0x00},
    {0x040A,0x00},
    {0x040B,0x00},
    {0x040C,0x00},
    {0x040D,0x00},
    {0x0500,0x04},
    {0x0501,0x00},
    {0x0600,0x00},
};

/*改变 EQ 风格*/
static struct reg_default init_dauflt_reg_value[]={
/*==============默认==============*/
    {0x0610,0x00},
    {0x0611,0x00},
    {0x0612,0x00},
    {0x0613,0x00},
    {0x0614,0x00},
    {0x0615,0x00},
    {0x0616,0x00},
    {0x0617,0x00},
    {0x0618,0x00},
    {0x0619,0x00},
    {0x061A,0x00},
    {0x061B,0x00},
    {0x061C,0x00},
};
static struct reg_default init_jazz_reg_value[]={
/*==============jazz==============*/
    {0x0610,0x62},
    {0x0611,0x61},
    {0x0612,0x62},
    {0x0613,0x60},
    {0x0614,0x60},
    {0x0615,0x71},
    {0x0616,0x60},
    {0x0617,0x60},
    {0x0618,0x60},
    {0x0619,0x61},
    {0x061A,0x60},
    {0x061B,0x61},
    {0x061C,0x62},
};
static struct reg_default init_rock_reg_value[]={
/*============Rock================*/
    {0x0610,0x62},
    {0x0611,0x62},
    {0x0612,0x63},
    {0x0613,0x61},
    {0x0614,0x72},
    {0x0615,0x71},
    {0x0616,0x60},
    {0x0617,0x61},
    {0x0618,0x60},
    {0x0619,0x61},
    {0x061A,0x61},
    {0x061B,0x62},
    {0x061C,0x64},
};
static struct reg_default init_flat_reg_value[]={
/*===============Flat===============*/
    {0x0610,0x60},
    {0x0611,0x60},
    {0x0612,0x60},
    {0x0613,0x60},
    {0x0614,0x60},
    {0x0615,0x60},
    {0x0616,0x60},
    {0x0617,0x60},
    {0x0618,0x60},
    {0x0619,0x60},
    {0x061A,0x60},
    {0x061B,0x60},
    {0x061C,0x60},
};
static struct reg_default init_vocal_reg_value[]={
/*==============Vocal================*/
    {0x0610,0x72},
    {0x0611,0x71},
    {0x0612,0x60},
    {0x0613,0x60},
    {0x0614,0x61},
    {0x0615,0x62},
    {0x0616,0x62},
    {0x0617,0x61},
    {0x0618,0x62},
    {0x0619,0x62},
    {0x061A,0x60},
    {0x061B,0x60},
    {0x061C,0x72},
};
static struct reg_default init_natural_reg_value[]={
/*===============Natural===============*/
    {0x0610,0x72},
    {0x0611,0x61},
    {0x0612,0x60},
    {0x0613,0x60},
    {0x0614,0x71},
    {0x0615,0x60},
    {0x0616,0x60},
    {0x0617,0x60},
    {0x0618,0x60},
    {0x0619,0x71},
    {0x061A,0x60},
    {0x061B,0x61},
    {0x061C,0x61},
};
static struct reg_default init_classic_reg_value[]={
/*===============Classic===============*/
    {0x0610,0x61},
    {0x0611,0x60},
    {0x0612,0x61},
    {0x0613,0x60},
    {0x0614,0x60},
    {0x0615,0x60},
    {0x0616,0x60},
    {0x0617,0x60},
    {0x0618,0x60},
    {0x0619,0x60},
    {0x061A,0x60},
    {0x061B,0x61},
    {0x061C,0x62},
};
static struct reg_default init_pops_reg_value[]={
/*===============Pops===============*/
    {0x0610,0x61},
    {0x0611,0x61},
    {0x0612,0x61},
    {0x0613,0x62},
    {0x0614,0x61},
    {0x0615,0x60},
    {0x0616,0x60},
    {0x0617,0x71},
    {0x0618,0x71},
    {0x0619,0x60},
    {0x061A,0x61},
    {0x061B,0x61},
    {0x061C,0x62},

};
static struct reg_default init_3_reg_value[]={
    {0x061D,0x00},
    {0x061E,0x00},
    {0x061F,0x00},
    {0x0620,0x00},
    {0x0621,0x00},
    {0x0622,0x00},
    {0x0623,0x00},
    {0x0624,0x00},
    {0x0625,0x00},
    {0x0626,0x00},
    {0x0627,0x00},
    {0x0628,0x00},
    {0x0629,0x00},
    {0x062A,0x00},
    {0x062B,0x00},
    {0x062C,0x00},
    {0x062D,0x00},
    {0x062E,0x00},
    {0x062F,0x00},
    {0x0700,0x00},
    {0x0701,0x00},
    {0x0702,0x80},
    {0x0703,0x00},
    {0x0704,0x00},
    {0x0705,0x80},
    {0x0706,0x80},
    {0x0707,0x00},
    {0x0708,0x00},
    {0x0709,0x00},
    {0x0800,0x00},
    {0x0801,0x00},
    {0x0802,0x00},
    {0x0803,0x00},
    {0x0804,0x00},
    {0x0805,0x40},
    {0x0900,0x40},
    {0x0901,0x40},
    {0x0902,0x40},
    {0x0903,0x40},
    {0x0904,0x40},
    {0x0905,0x40},
    {0x0906,0x80},
    {0x0907,0x80},
    {0x0908,0x80},
    {0x0909,0x80},
    {0x090A,0x80},
    {0x090B,0x80},


    {0x0A00,0x80},//0x80(-∞): 静音 
    {0x0A01,0x80},
    {0x0A02,0x80},
    {0x0A03,0x80},
    {0x0A04,0x80},
    {0x0A05,0x80},

};

static struct reg_default mute_reg_value[]=
{
    {0x0A02,0x80},
    {0x0A03,0x80},
};

static struct reg_default unmute_reg_value[]=
{
    {0x0A02,0xA0},//0xA0
    {0x0A03,0xA0},//0xA0
};

static struct reg_default mute_subwoofer_reg_value[]=
{
    {0x0A04,0x80},
    {0x0A05,0x80},
};

static struct reg_default unmute_subwoofer_reg_value[]=
{
    {0x0A04,0xA0},
    {0x0A05,0xA0},
};

static struct reg_default system_reset_reg_value[]=
{
    {0xFEFE,0x81},
};

static struct reg_default lineout_reg_value[]=
{
    {0x0103,0x04},
    {0x0203,0x00},
};

static struct reg_default mix_reg_value[]=
{
    {0x0106,0xBC},
    {0x0103,0x22},
    {0x0107,0xA0},
    {0x0108,0xA0},
};

#endif