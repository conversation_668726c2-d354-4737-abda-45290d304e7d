#ifndef AK7739_DSP_CODE
#define AK7739_DSP_CODE
/*
 * ak7739_dsp_code.h  --  audio driver for ak7739
 *
 * Copyright (C) 2018 Asahi Kasei Microdevices Corporation
 *  Author                Date        Revision
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *                      18/06/04	    1.0
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 *
 */


static u8 ak7739_pram1_basic[]= {
	0xB8, 0x00, 0x00,		// command code and address
	0x0C, 0x94, 0xED, 0x1E, 0x74,
	0x04, 0xA9, 0x1C, 0x6D, 0x55,
	0x00, 0x44, 0x02, 0x11, 0x99,
	0x09, 0x58, 0x6F, 0x4D, 0xD0,
	0x01, 0x5A, 0x7E, 0xC9, 0x38,
	0x01, 0x35, 0xB3, 0x09, 0x8C,
	0x03, 0x4B, 0xFA, 0x24, 0x75,
	0x06, 0x78, 0xFB, 0xA0, 0xD6,
	0x0F, 0x82, 0xD3, 0xD3, 0x40,
	0x05, 0x79, 0x77, 0x03, 0x9D,
	0x07, 0xFE, 0x22, 0x43, 0x38,
	0x0E, 0xD9, 0xA1, 0xDE, 0x1F,
	0x00, 0x7B, 0xE2, 0xE2, 0x61,
	0x01, 0xDA, 0xBC, 0xF1, 0xF1,
	0x02, 0x38, 0xDA, 0xB0, 0xD8,
	0x08, 0x04, 0x23, 0x09, 0xCA,
	0x02, 0xDE, 0x9F, 0x97, 0x42,
	0x04, 0xFD, 0x92, 0x5B, 0xF2,
	0x0A, 0x36, 0x83, 0x19, 0x46,
	0x07, 0xF4, 0x58, 0xEB, 0x2C,
	0x01, 0xF7, 0x41, 0xA9, 0x9A,
	0x0D, 0xAF, 0x2A, 0x89, 0xCA,
	0x02, 0xEE, 0x07, 0x32, 0xCF,
	0x0D, 0x44, 0x86, 0x70, 0xBD,
	0x01, 0x43, 0xB8, 0x39, 0xE0,
	0x07, 0xC5, 0xCC, 0x82, 0x53,
	0x04, 0x29, 0x73, 0x62, 0xA4,
	0x01, 0xB5, 0x71, 0x31, 0x10,
	0x08, 0x46, 0x13, 0x91, 0x43,
	0x05, 0x37, 0xA2, 0x8D, 0xA9,
	0x0B, 0x24, 0xB7, 0xEC, 0x14,
	0x0C, 0x06, 0x32, 0x8D, 0x2F,
	0x0A, 0xB1, 0xD2, 0x5F, 0xE3,
	0x0E, 0x83, 0x5B, 0x76, 0x0B,
	0x0F, 0x05, 0x82, 0x95, 0xE5,
	0x0C, 0x0E, 0x74, 0x9E, 0xBA,
	0x09, 0x0C, 0xE1, 0x7F, 0x45,
	0x0F, 0x78, 0xFF, 0xC8, 0xEF,
	0x0B, 0x99, 0x04, 0xAE, 0x68,
	0x03, 0xE6, 0xC5, 0x48, 0xE3,
	0x08, 0xE2, 0x66, 0x26, 0x10,
	0x0C, 0x27, 0x2A, 0xC3, 0x7A,
	0x0E, 0x15, 0x8A, 0xD3, 0xF6,
	0x09, 0x6F, 0xC9, 0xA9, 0x98,
	0x0C, 0x65, 0x1A, 0x5B, 0xF5,
	0x0B, 0xAC, 0x33, 0xCE, 0x9D,
	0x06, 0xB6, 0xEC, 0x1F, 0xFE,
	0x0A, 0x05, 0x2B, 0xCB, 0xB8,
	0x0E, 0xE9, 0x39, 0x73, 0x12,
	0x09, 0xC2, 0xF6, 0xCD, 0x0E,
	0x00, 0xAF, 0x03, 0xDF, 0x17,
	0x02, 0x09, 0x4E, 0xD1, 0xE7,
	0x0D, 0x8A, 0x91, 0xC2, 0xF0,
	0x0C, 0xC4, 0xC0, 0x28, 0x98,
	0x0E, 0x55, 0x86, 0xFD, 0x5C,
	0x0A, 0x15, 0xA7, 0xEC, 0x92,
	0x0D, 0x93, 0x57, 0x36, 0x18,
	0x0A, 0x34, 0xBF, 0xA2, 0xC7,
	0x09, 0x37, 0x0F, 0xBA, 0x0D,
	0x0D, 0xD8, 0x2D, 0x7D, 0x54,
	0x0A, 0x57, 0x97, 0x74, 0x1F,
	0x0A, 0x7A, 0x6A, 0x2D, 0xF3,
	0x05, 0xED, 0x9A, 0x14, 0x21,
	0x0F, 0x07, 0xBE, 0x2E, 0x64,
	0x00, 0x9D, 0xA7, 0xC9, 0x9B,
	0x05, 0x23, 0x8D, 0xAB, 0x89,
	0x08, 0xD0, 0xC2, 0x30, 0x9C,
	0x0B, 0x0D, 0xE9, 0xB9, 0x14,
	0x0B, 0x4F, 0xD9, 0x21, 0x98,
	0x0E, 0xA6, 0xE0, 0x3B, 0x94,
	0x09, 0x7F, 0x45, 0x84, 0xB2,
	0x0F, 0x1F, 0x74, 0x1A, 0xDB,
	0x02, 0x5A, 0xFE, 0xAE, 0x14,
	0x0F, 0x2E, 0xE0, 0x73, 0xA4,
	0x05, 0x84, 0xC8, 0x67, 0x0B,
	0x0B, 0x34, 0x3B, 0xC3, 0xFE,
	0x0F, 0x7C, 0x5C, 0xCC, 0x0D,
	0x03, 0x47, 0x1F, 0x3C, 0x6A,
	0x07, 0x1B, 0x57, 0x19, 0x51,
	0x00, 0x84, 0x61, 0x39, 0x56,
	0x09, 0xD3, 0x76, 0x2E, 0x56,
	0x0F, 0xB2, 0x4B, 0x7E, 0x4D,
	0x0C, 0x90, 0xE3, 0x28, 0xD2,
	0x0E, 0x8B, 0x1D, 0x65, 0x9E,
	0x0E, 0xE8, 0x35, 0xB3, 0x49,
	0x0D, 0xF5, 0xD0, 0x23, 0xDE,
	0x0D, 0xC0, 0xE7, 0x43, 0x6B,
	0x08, 0x90, 0xCE, 0x17, 0xB6,
	0x0A, 0x77, 0x83, 0xFA, 0x1E,
	0x08, 0xB9, 0x90, 0x4A, 0x76,
	0x0F, 0x6E, 0xEC, 0x54, 0x8E,
	0x06, 0xAE, 0x26, 0x22, 0x01,
	0x08, 0xC2, 0x72, 0xA8, 0x1D,
	0x0E, 0xE4, 0xD0, 0xA7, 0xFF,
	0x04, 0x96, 0xFC, 0x90, 0x59,
	0x00, 0xC6, 0x51, 0xA5, 0xFD,
	0x04, 0x3A, 0xCF, 0x3A, 0x7D,
	0x00, 0x6B, 0x6E, 0xC1, 0x6B,
	0x0A, 0xF0, 0xD2, 0xBC, 0xBB,
	0x01, 0xCE, 0x93, 0xD7, 0x51,
	0x01, 0x9C, 0x2F, 0x68, 0xFB,
	0x00, 0x0F, 0xF8, 0x3D, 0xF1
};



static u8 ak7739_pram2_basic[]= {
	0xB8, 0x00, 0x00,		// command code and address
	0x0C, 0x94, 0xED, 0x1E, 0x74,
	0x04, 0xA9, 0x1C, 0x6D, 0x55,
	0x00, 0x44, 0x02, 0x11, 0x99,
	0x09, 0x58, 0x6F, 0x4D, 0xD0,
	0x01, 0x5A, 0x7E, 0xC9, 0x38,
	0x01, 0x35, 0xB3, 0x09, 0x8C,
	0x03, 0x4B, 0xFA, 0x24, 0x75,
	0x0E, 0xF8, 0x5B, 0xA8, 0x96,
	0x0D, 0x82, 0xD7, 0xDD, 0x00,
	0x01, 0x79, 0x77, 0x05, 0x9D,
	0x03, 0xAE, 0xA2, 0x45, 0x78,
	0x0E, 0x89, 0x21, 0xD8, 0x9C,
	0x00, 0xAB, 0x42, 0xE6, 0x41,
	0x00, 0xDA, 0xF8, 0xFB, 0x31,
	0x0B, 0x3A, 0x9E, 0xB8, 0x5B,
	0x02, 0x04, 0x2A, 0x09, 0xC2,
	0x01, 0xDD, 0xD7, 0x91, 0x42,
	0x04, 0xFD, 0x96, 0x5B, 0xF2,
	0x08, 0x66, 0x07, 0x1D, 0xC6,
	0x0E, 0xF4, 0x98, 0xEB, 0x6C,
	0x00, 0xF6, 0x80, 0xAD, 0xBB,
	0x05, 0xAF, 0xA9, 0x07, 0x4A,
	0x0B, 0xEE, 0xC7, 0x3A, 0x0F,
	0x0D, 0x44, 0x87, 0x76, 0x37,
	0x03, 0x43, 0xBC, 0xB9, 0xA0,
	0x07, 0xC5, 0xCD, 0x8E, 0x50,
	0x04, 0x79, 0xF4, 0xE2, 0xA4,
	0x01, 0xB5, 0x70, 0x37, 0x53,
	0x09, 0x0E, 0xD7, 0x13, 0x62,
	0x0C, 0xFF, 0xC3, 0x85, 0x69,
	0x0A, 0x6D, 0x73, 0x66, 0x64,
	0x0D, 0xCF, 0xD1, 0x8D, 0x2F,
	0x09, 0xF8, 0x10, 0xD9, 0xE3,
	0x0F, 0x4A, 0xBA, 0x76, 0x41,
	0x0E, 0x9C, 0xE6, 0x95, 0xAF,
	0x0D, 0x47, 0xB5, 0x9E, 0xBA,
	0x0B, 0x45, 0x26, 0xF7, 0x66,
	0x07, 0xB1, 0x9E, 0xC1, 0xEF,
	0x0A, 0xD1, 0xC0, 0x27, 0x68,
	0x02, 0x2E, 0x24, 0x48, 0xE3,
	0x0B, 0xAB, 0xA6, 0xA2, 0xA0,
	0x0D, 0xEE, 0xC9, 0xC3, 0x7A,
	0x0F, 0x0C, 0xCC, 0x53, 0xF6,
	0x08, 0xA6, 0x28, 0xAF, 0x92,
	0x0D, 0xAC, 0xFE, 0x59, 0x9B,
	0x02, 0xE5, 0x72, 0xC7, 0xDD,
	0x04, 0xFF, 0x2B, 0x9A, 0xBE,
	0x0A, 0xCC, 0xCA, 0xCB, 0xB8,
	0x0D, 0xA1, 0xF9, 0xF5, 0x12,
	0x08, 0x0A, 0x17, 0xCD, 0x0E,
	0x01, 0xB6, 0x47, 0x5D, 0xA7,
	0x03, 0xC0, 0xAD, 0xD1, 0xE7,
	0x0C, 0xC3, 0x57, 0x46, 0xD5,
	0x05, 0x0D, 0xA1, 0x27, 0x12,
	0x0F, 0x9C, 0x62, 0xF2, 0x96,
	0x0B, 0x5C, 0x66, 0xEC, 0x92,
	0x0F, 0xDA, 0x93, 0x30, 0x68,
	0x0A, 0xFD, 0x5F, 0xA2, 0xC7,
	0x05, 0x5D, 0x8F, 0xBA, 0x1B,
	0x0D, 0xD8, 0x2C, 0x7B, 0x5E,
	0x0A, 0x57, 0x97, 0xF6, 0x73,
	0x02, 0x7A, 0xEA, 0x20, 0x13,
	0x05, 0xED, 0x9A, 0x19, 0x80,
	0x00, 0x07, 0xBE, 0x2E, 0x64
};


static u8 ak7739_cram1_basic[]= {
	0xB4, 0x00, 0x00,		// command code and address
	0x20, 0x00, 0x00, 0x00,
	0x20, 0x00, 0x00, 0x00,
	0x20, 0x00, 0x00, 0x00,
	0x20, 0x00, 0x00, 0x00,
	0x20, 0x00, 0x00, 0x00,
	0x20, 0x00, 0x00, 0x00,
	0x20, 0x00, 0x00, 0x00,
	0x20, 0x00, 0x00, 0x00,
	0x20, 0x00, 0x00, 0x00,
	0x20, 0x00, 0x00, 0x00,
	0x20, 0x00, 0x00, 0x00,
	0x20, 0x00, 0x00, 0x00,
};

static u8 ak7739_cram2_basic[]= {
	0xB4, 0x00, 0x00,		// command code and address
	0x20, 0x00, 0x00, 0x00,
	0x7F, 0xE9, 0x42, 0x9F,
	0x7F, 0xF2, 0x5A, 0x34,
	0x3F, 0x69, 0x06, 0xDB,
	0x81, 0x2D, 0xF2, 0x4B,
	0xC1, 0x2C, 0x8E, 0x40,
	0x7E, 0xD0, 0xA9, 0xAB,
	0x3F, 0x69, 0x06, 0xDB,
	0x3F, 0xC3, 0x73, 0x9D,
	0x80, 0x3C, 0xFF, 0x29,
	0xC0, 0x3C, 0x8C, 0x63,
	0x7F, 0xC3, 0x00, 0xD7,
	0x40, 0x00, 0x00, 0x00,
	0x3F, 0xA0, 0xBC, 0xF8,
	0x80, 0x60, 0x5F, 0x72,
	0xC0, 0x5F, 0x43, 0x08,
	0x7F, 0x9F, 0xA0, 0x8E,
	0x40, 0x00, 0x00, 0x00
};

static u8 ak7739_ofreg1_basic[]= {
	0xB2, 0x00, 0x00,  // OFREG Write Command  
	0x00, 0x00, 0x00, 0x00,
};

static u8 ak7739_ofreg2_basic[]= {
	0xB2, 0x00, 0x00, 		// command code and address
};


/* ak7739 register cache & default register settings */
static const struct reg_default ak7739_reg[] = {
	/* AUDIOHUB,PERI block */
  { 0x0000, 0x03 },  /* AK7739_C0_000_SYSCLOCK_SETTING1                */
  { 0x0001, 0x01 },  /* AK7739_C0_001_SYSCLOCK_SETTING2                */
  { 0x0002, 0x03 },  /* AK7739_C0_002_SYSCLOCK_SETTING3                */
  { 0x0003, 0x5c },  /* AK7739_C0_003_SYSCLOCK_SETTING4                */
  { 0x0004, 0x00 },  /* AK7739_C0_004_SYSCLOCK_SETTING5                */
  { 0x0008, 0x01 },  /* AK7739_C0_008_AHCLK_SETTING5                   */
  { 0x0009, 0x01 },  /* AK7739_C0_009_BUSCLOCK_SETTING                 */
  { 0x000A, 0x02 },  /* AK7739_C0_00A_BUSCLOCK_SETTING2                */
  { 0x000B, 0x00 },  /* AK7739_C0_00B_CLKO_SETTING1                    */
  { 0x000C, 0x00 },  /* AK7739_C0_00C_CLKO_SETTING2                    */
  { 0x000D, 0x02 },  /* AK7739_C0_00D_MasterSPI_SCLK_SETTING1          */
  { 0x000E, 0x01 },  /* AK7739_C0_00E_MasterSPI_SCLK_SETTING2          */
  { 0x0010, 0x00 },  /* AK7739_C0_010_SYNCDOMAIN1_SETTING1             */
  { 0x0011, 0x00 },  /* AK7739_C0_011_SYNCDOMAIN1_SETTING2             */
  { 0x0012, 0x17 },  /* AK7739_C0_012_SYNCDOMAIN1_SETTING3             */
  { 0x0013, 0x00 },  /* AK7739_C0_013_SYNCDOMAIN1_SETTING4             */
  { 0x0014, 0x00 },  /* AK7739_C0_014_SYNCDOMAIN2_SETTING1             */
  { 0x0015, 0x00 },  /* AK7739_C0_015_SYNCDOMAIN2_SETTING2             */
  { 0x0016, 0x00 },  /* AK7739_C0_016_SYNCDOMAIN2_SETTING3             */
  { 0x0017, 0x00 },  /* AK7739_C0_017_SYNCDOMAIN2_SETTING4             */
  { 0x0018, 0x80 },  /* AK7739_C0_018_SYNCDOMAIN3_SETTING1             */
  { 0x0019, 0x01 },  /* AK7739_C0_019_SYNCDOMAIN3_SETTING2             */
  { 0x001A, 0x17 },  /* AK7739_C0_01A_SYNCDOMAIN3_SETTING3             */
  { 0x001B, 0x00 },  /* AK7739_C0_01B_SYNCDOMAIN3_SETTING4             */
  { 0x001C, 0x00 },  /* AK7739_C0_01C_SYNCDOMAIN4_SETTING1             */
  { 0x001D, 0x00 },  /* AK7739_C0_01D_SYNCDOMAIN4_SETTING2             */
  { 0x001E, 0x00 },  /* AK7739_C0_01E_SYNCDOMAIN4_SETTING3             */
  { 0x001F, 0x00 },  /* AK7739_C0_01F_SYNCDOMAIN4_SETTING4             */
  { 0x0020, 0x00 },  /* AK7739_C0_020_SYNCDOMAIN5_SETTING1             */
  { 0x0021, 0x00 },  /* AK7739_C0_021_SYNCDOMAIN5_SETTING2             */
  { 0x0022, 0x00 },  /* AK7739_C0_022_SYNCDOMAIN5_SETTING3             */
  { 0x0023, 0x00 },  /* AK7739_C0_023_SYNCDOMAIN5_SETTING4             */
  { 0x0025, 0x00 },  /* AK7739_C0_025_SYNCDOMAIN6_SETTING2             */
  { 0x0026, 0x00 },  /* AK7739_C0_026_SYNCDOMAIN6_SETTING3             */
  { 0x0027, 0x00 },  /* AK7739_C0_027_SYNCDOMAIN6_SETTING4             */
  { 0x0029, 0x01 },  /* AK7739_C0_029_SYNCDOMAIN7_SETTING2             */
  { 0x002A, 0x47 },  /* AK7739_C0_02A_SYNCDOMAIN7_SETTING3             */
  { 0x002B, 0x00 },  /* AK7739_C0_02B_SYNCDOMAIN7_SETTING4             */
  { 0x0040, 0x00 },  /* AK7739_C0_040_BICK_FORMAT_SETTING1             */
  { 0x0041, 0x01 },  /* AK7739_C0_041_BICK_SYNCDOMAIN_SELECT1          */
  { 0x0042, 0x00 },  /* AK7739_C0_042_BICK_FORMAT_SETTING2             */
  { 0x0043, 0x02 },  /* AK7739_C0_043_BICK_SYNCDOMAIN_SELECT2          */
  { 0x0044, 0x00 },  /* AK7739_C0_044_BICK_FORMAT_SETTING3             */
  { 0x0045, 0x00 },  /* AK7739_C0_045_BICK_SYNCDOMAIN_SELECT3          */
  { 0x0046, 0x00 },  /* AK7739_C0_046_BICK_FORMAT_SETTING4             */
  { 0x0047, 0x00 },  /* AK7739_C0_047_BICK_SYNCDOMAIN_SELECT4          */
  { 0x0048, 0x00 },  /* AK7739_C0_048_BICK_FORMAT_SETTING5             */
  { 0x0049, 0x00 },  /* AK7739_C0_049_BICK_SYNCDOMAIN_SELECT5          */
  { 0x0050, 0x00 },  /* AK7739_C0_050_SDIN1_INPUT_FORMAT               */
  { 0x0051, 0x01 },  /* AK7739_C0_051_SDIN1_SYNCDOMAIN_SELECT          */
  { 0x0052, 0x00 },  /* AK7739_C0_052_SDIN2_INPUT_FORMAT               */
  { 0x0053, 0x00 },  /* AK7739_C0_053_SDIN2_SYNCDOMAIN_SELECT          */
  { 0x0054, 0x00 },  /* AK7739_C0_054_SDIN3_INPUT_FORMAT               */
  { 0x0055, 0x00 },  /* AK7739_C0_055_SDIN3_SYNCDOMAIN_SELECT          */
  { 0x0056, 0x00 },  /* AK7739_C0_056_SDIN4_INPUT_FORMAT               */
  { 0x0057, 0x00 },  /* AK7739_C0_057_SDIN4_SYNCDOMAIN_SELECT          */
  { 0x0058, 0x00 },  /* AK7739_C0_058_SDIN5_INPUT_FORMAT               */
  { 0x0059, 0x00 },  /* AK7739_C0_059_SDIN5_SYNCDOMAIN_SELECT          */
  { 0x005A, 0x00 },  /* AK7739_C0_05A_SDIN6_INPUT_FORMAT               */
  { 0x005B, 0x00 },  /* AK7739_C0_05B_SDIN6_SYNCDOMAIN_SELECT          */
  { 0x0060, 0x00 },  /* AK7739_C0_060_SDOUT1_OUTPUT_FORMAT             */
  { 0x0061, 0x00 },  /* AK7739_C0_061_SDOUT1_SYNCDOMAIN_SELECT         */
  { 0x0062, 0xB0 },  /* AK7739_C0_062_SDOUT2_OUTPUT_FORMAT             */
  { 0x0063, 0x00 },  /* AK7739_C0_063_SDOUT2_SYNCDOMAIN_SELECT         */
  { 0x0064, 0x00 },  /* AK7739_C0_064_SDOUT3_OUTPUT_FORMAT             */
  { 0x0065, 0x00 },  /* AK7739_C0_065_SDOUT3_SYNCDOMAIN_SELECT         */
  { 0x0066, 0x00 },  /* AK7739_C0_066_SDOUT4_OUTPUT_FORMAT             */
  { 0x0067, 0x00 },  /* AK7739_C0_067_SDOUT4_SYNCDOMAIN_SELECT         */
  { 0x0068, 0x00 },  /* AK7739_C0_068_SDOUT5_OUTPUT_FORMAT             */
  { 0x0069, 0x00 },  /* AK7739_C0_069_SDOUT5_SYNCDOMAIN_SELECT         */
  { 0x006A, 0x00 },  /* AK7739_C0_06A_SDOUT6_OUTPUT_FORMAT             */
  { 0x006B, 0x07 },  /* AK7739_C0_06B_SDOUT6_SYNCDOMAIN_SELECT         */
  { 0x0087, 0x00 },  /* AK7739_C0_087_OSMEM_SYNCDOMAIN_SELECT          */
  { 0x0088, 0x07 },  /* AK7739_C0_088_ADC1_SYNCDOMAIN_SELECT           */
  { 0x0089, 0x07 },  /* AK7739_C0_089_CODEC_SYNCDOMAIN_SELECT          */
  { 0x0098, 0x00 },  /* AK7739_C0_098_MIXER_CH1_INPUT_SELECT           */
  { 0x0099, 0x00 },  /* AK7739_C0_099_MIXER_CH1_INPUT_SELECT           */
  { 0x00A0, 0x03 },  /* AK7739_C0_0A0_SRC1_SYNCDOMAIN_SELECT           */
  { 0x00A1, 0x02 },  /* AK7739_C0_0A1_SRC2_SYNCDOMAIN_SELECT           */
  { 0x00A2, 0x00 },  /* AK7739_C0_0A2_SRC3_SYNCDOMAIN_SELECT           */
  { 0x00A3, 0x00 },  /* AK7739_C0_0A3_SRC4_SYNCDOMAIN_SELECT           */
  { 0x00A4, 0x00 },  /* AK7739_C0_0A4_SRC5_SYNCDOMAIN_SELECT           */
  { 0x00A5, 0x00 },  /* AK7739_C0_0A5_SRC6_SYNCDOMAIN_SELECT           */
  { 0x00A6, 0x00 },  /* AK7739_C0_0A6_SRC7_SYNCDOMAIN_SELECT           */
  { 0x00A7, 0x00 },  /* AK7739_C0_0A7_SRC8_SYNCDOMAIN_SELECT           */
  { 0x00B0, 0x07 },  /* AK7739_C0_0B0_DSP1_SYNCDOMAIN_SELECT           */
  { 0x00B1, 0x00 },  /* AK7739_C0_0B1_DSP2_SYNCDOMAIN_SELECT           */
  { 0x00C0, 0x07 },  /* AK7739_C0_0C0_DSP1_OUTPUT_SYNCDOMAIN_SELECT1   */
  { 0x00C1, 0x07 },  /* AK7739_C0_0C1_DSP1_OUTPUT_SYNCDOMAIN_SELECT2   */
  { 0x00C2, 0x00 },  /* AK7739_C0_0C2_DSP1_OUTPUT_SYNCDOMAIN_SELECT3   */
  { 0x00C3, 0x00 },  /* AK7739_C0_0C3_DSP1_OUTPUT_SYNCDOMAIN_SELECT4   */
  { 0x00C4, 0x00 },  /* AK7739_C0_0C4_DSP1_OUTPUT_SYNCDOMAIN_SELECT5   */
  { 0x00C5, 0x00 },  /* AK7739_C0_0C5_DSP1_OUTPUT_SYNCDOMAIN_SELECT6   */
  { 0x00C6, 0x00 },  /* AK7739_C0_0C6_DSP2_OUTPUT_SYNCDOMAIN_SELECT1   */
  { 0x00C7, 0x00 },  /* AK7739_C0_0C7_DSP2_OUTPUT_SYNCDOMAIN_SELECT2   */
  { 0x00C8, 0x00 },  /* AK7739_C0_0C8_DSP2_OUTPUT_SYNCDOMAIN_SELECT3   */
  { 0x00C9, 0x00 },  /* AK7739_C0_0C9_DSP2_OUTPUT_SYNCDOMAIN_SELECT4   */
  { 0x00CA, 0x00 },  /* AK7739_C0_0CA_DSP2_OUTPUT_SYNCDOMAIN_SELECT5   */
  { 0x00CB, 0x00 },  /* AK7739_C0_0CB_DSP2_OUTPUT_SYNCDOMAIN_SELECT6   */
  { 0x0106, 0x00 },  /* AK7739_C0_106_DIT_INPUT_DATA_SELECT            */
  { 0x0108, 0x08 },  /* AK7739_C0_108_DAC1_INPUT_DATA_SELECT           */
  { 0x0109, 0x09 },  /* AK7739_C0_109_DAC2_INPUT_DATA_SELECT           */
  { 0x0118, 0x00 },  /* AK7739_C0_118_MIXER1_CHA_INPUT_DATA_SELECT     */
  { 0x0119, 0x00 },  /* AK7739_C0_119_MIXER1_CHB_INPUT_DATA_SELECT     */
  { 0x011A, 0x00 },  /* AK7739_C0_11A_MIXER2_CHA_INPUT_DATA_SELECT     */
  { 0x011B, 0x00 },  /* AK7739_C0_11B_MIXER2_CHB_INPUT_DATA_SELECT     */
  { 0x0120, 0x00 },  /* AK7739_C0_120_SRC1_INPUT_DATA_SELECT           */
  { 0x0121, 0x00 },  /* AK7739_C0_121_SRC2_INPUT_DATA_SELECT           */
  { 0x0122, 0x00 },  /* AK7739_C0_122_SRC3_INPUT_DATA_SELECT           */
  { 0x0123, 0x00 },  /* AK7739_C0_123_SRC4_INPUT_DATA_SELECT           */
  { 0x0124, 0x00 },  /* AK7739_C0_124_SRC5_INPUT_DATA_SELECT           */
  { 0x0125, 0x00 },  /* AK7739_C0_125_SRC6_INPUT_DATA_SELECT           */
  { 0x0126, 0x00 },  /* AK7739_C0_126_SRC7_INPUT_DATA_SELECT           */
  { 0x0127, 0x00 },  /* AK7739_C0_127_SRC8_INPUT_DATA_SELECT           */
  { 0x0140, 0x00 },  /* AK7739_C0_140_SDOUT1A_OUTPUT_DATA_SELECT       */
  { 0x0141, 0x00 },  /* AK7739_C0_141_SDOUT1B_OUTPUT_DATA_SELECT       */
  { 0x0142, 0x00 },  /* AK7739_C0_142_SDOUT1C_OUTPUT_DATA_SELECT       */
  { 0x0143, 0x00 },  /* AK7739_C0_143_SDOUT1D_OUTPUT_DATA_SELECT       */
  { 0x0144, 0x00 },  /* AK7739_C0_144_SDOUT1E_OUTPUT_DATA_SELECT       */
  { 0x0145, 0x00 },  /* AK7739_C0_145_SDOUT1F_OUTPUT_DATA_SELECT       */
  { 0x0146, 0x00 },  /* AK7739_C0_146_SDOUT1G_OUTPUT_DATA_SELECT       */
  { 0x0147, 0x00 },  /* AK7739_C0_147_SDOUT1H_OUTPUT_DATA_SELECT       */
  { 0x0148, 0x00 },  /* AK7739_C0_148_SDOUT2A_OUTPUT_DATA_SELECT       */
  { 0x0149, 0x21 },  /* AK7739_C0_149_SDOUT2B_OUTPUT_DATA_SELECT       */
  { 0x014A, 0x21 },  /* AK7739_C0_14A_SDOUT2C_OUTPUT_DATA_SELECT       */
  { 0x014B, 0x00 },  /* AK7739_C0_14B_SDOUT2D_OUTPUT_DATA_SELECT       */
  { 0x014C, 0x00 },  /* AK7739_C0_14C_SDOUT2E_OUTPUT_DATA_SELECT       */
  { 0x014D, 0x00 },  /* AK7739_C0_14D_SDOUT2F_OUTPUT_DATA_SELECT       */
  { 0x014E, 0x00 },  /* AK7739_C0_14E_SDOUT2G_OUTPUT_DATA_SELECT       */
  { 0x014F, 0x00 },  /* AK7739_C0_14F_SDOUT2H_OUTPUT_DATA_SELECT       */
  { 0x0150, 0x00 },  /* AK7739_C0_150_SDOUT3A_OUTPUT_DATA_SELECT       */
  { 0x0151, 0x00 },  /* AK7739_C0_151_SDOUT3B_OUTPUT_DATA_SELECT       */
  { 0x0152, 0x00 },  /* AK7739_C0_152_SDOUT3C_OUTPUT_DATA_SELECT       */
  { 0x0153, 0x00 },  /* AK7739_C0_153_SDOUT3D_OUTPUT_DATA_SELECT       */
  { 0x0154, 0x00 },  /* AK7739_C0_154_SDOUT3E_OUTPUT_DATA_SELECT       */
  { 0x0155, 0x00 },  /* AK7739_C0_155_SDOUT3F_OUTPUT_DATA_SELECT       */
  { 0x0156, 0x00 },  /* AK7739_C0_156_SDOUT3G_OUTPUT_DATA_SELECT       */
  { 0x0157, 0x00 },  /* AK7739_C0_157_SDOUT3H_OUTPUT_DATA_SELECT       */
  { 0x0158, 0x00 },  /* AK7739_C0_158_SDOUT4A_OUTPUT_DATA_SELECT       */
  { 0x0159, 0x00 },  /* AK7739_C0_159_SDOUT4B_OUTPUT_DATA_SELECT       */
  { 0x015A, 0x00 },  /* AK7739_C0_15A_SDOUT4C_OUTPUT_DATA_SELECT       */
  { 0x015B, 0x00 },  /* AK7739_C0_15B_SDOUT4D_OUTPUT_DATA_SELECT       */
  { 0x015C, 0x00 },  /* AK7739_C0_15C_SDOUT4E_OUTPUT_DATA_SELECT       */
  { 0x015D, 0x00 },  /* AK7739_C0_15D_SDOUT4F_OUTPUT_DATA_SELECT       */
  { 0x015E, 0x00 },  /* AK7739_C0_15E_SDOUT4G_OUTPUT_DATA_SELECT       */
  { 0x015F, 0x00 },  /* AK7739_C0_15F_SDOUT4H_OUTPUT_DATA_SELECT       */
  { 0x0160, 0x00 },  /* AK7739_C0_160_SDOUT5A_OUTPUT_DATA_SELECT       */
  { 0x0161, 0x00 },  /* AK7739_C0_161_SDOUT5B_OUTPUT_DATA_SELECT       */
  { 0x0162, 0x00 },  /* AK7739_C0_162_SDOUT5C_OUTPUT_DATA_SELECT       */
  { 0x0163, 0x00 },  /* AK7739_C0_163_SDOUT5D_OUTPUT_DATA_SELECT       */
  { 0x0164, 0x00 },  /* AK7739_C0_164_SDOUT5E_OUTPUT_DATA_SELECT       */
  { 0x0165, 0x00 },  /* AK7739_C0_165_SDOUT5F_OUTPUT_DATA_SELECT       */
  { 0x0166, 0x00 },  /* AK7739_C0_166_SDOUT5G_OUTPUT_DATA_SELECT       */
  { 0x0167, 0x00 },  /* AK7739_C0_167_SDOUT5H_OUTPUT_DATA_SELECT       */
  { 0x0168, 0x08 },  /* AK7739_C0_168_SDOUT6A_OUTPUT_DATA_SELECT       */
  { 0x0169, 0x00 },  /* AK7739_C0_169_SDOUT6B_OUTPUT_DATA_SELECT       */
  { 0x016A, 0x00 },  /* AK7739_C0_16A_SDOUT6C_OUTPUT_DATA_SELECT       */
  { 0x016B, 0x00 },  /* AK7739_C0_16B_SDOUT6D_OUTPUT_DATA_SELECT       */
  { 0x016C, 0x00 },  /* AK7739_C0_16C_SDOUT6E_OUTPUT_DATA_SELECT       */
  { 0x016D, 0x00 },  /* AK7739_C0_16D_SDOUT6F_OUTPUT_DATA_SELECT       */
  { 0x016E, 0x00 },  /* AK7739_C0_16E_SDOUT6G_OUTPUT_DATA_SELECT       */
  { 0x016F, 0x00 },  /* AK7739_C0_16F_SDOUT6H_OUTPUT_DATA_SELECT       */
  { 0x0180, 0x08 },  /* AK7739_C0_180_DSP1_DIN1_INPUT_DATA_SELECT      */
  { 0x0181, 0x09 },  /* AK7739_C0_181_DSP1_DIN2_INPUT_DATA_SELECT      */
  { 0x0182, 0x00 },  /* AK7739_C0_182_DSP1_DIN3_INPUT_DATA_SELECT      */
  { 0x0183, 0x00 },  /* AK7739_C0_183_DSP1_DIN4_INPUT_DATA_SELECT      */
  { 0x0184, 0x00 },  /* AK7739_C0_184_DSP1_DIN5_INPUT_DATA_SELECT      */
  { 0x0185, 0x00 },  /* AK7739_C0_185_DSP1_DIN6_INPUT_DATA_SELECT      */
  { 0x0186, 0x00 },  /* AK7739_C0_186_DSP2_DIN1_INPUT_DATA_SELECT      */
  { 0x0187, 0x00 },  /* AK7739_C0_187_DSP2_DIN2_INPUT_DATA_SELECT      */
  { 0x0188, 0x00 },  /* AK7739_C0_188_DSP2_DIN3_INPUT_DATA_SELECT      */
  { 0x0189, 0x00 },  /* AK7739_C0_189_DSP2_DIN4_INPUT_DATA_SELECT      */
  { 0x018A, 0x00 },  /* AK7739_C0_18A_DSP2_DIN5_INPUT_DATA_SELECT      */
  { 0x018B, 0x00 },  /* AK7739_C0_18B_DSP2_DIN6_INPUT_DATA_SELECT      */
  { 0x0200, 0x00 },  /* AK7739_C0_200_SDOUT_OUTPUT_DATA_SELECT         */
  { 0x0201, 0xFC },  /* AK7739_C0_201_SDOUT_ENABLE_SETTING             */
  { 0x0202, 0x00 },  /* AK7739_C0_202_SDOUT_OUTPUT_MODE_SETTING        */
  { 0x0203, 0x00 },  /* AK7739_C0_203_SDIN_INPUT_DATA_SELECT           */
  { 0x0204, 0x00 },  /* AK7739_C0_204_MASTER_SPI_SELECT                */
  { 0x0205, 0x00 },  /* AK7739_C0_205_STO_FLAG_SETTING                 */
  { 0x0206, 0x00 },  /* AK7739_C0_206_LRCK4_5_OUTPUT_DATA_SELECT       */
  { 0x0210, 0x00 },  /* AK7739_C0_210_MASTER_SPI_TX00                  */
  { 0x0211, 0x00 },  /* AK7739_C0_211_MASTER_SPI_TX01                  */
  { 0x0212, 0x00 },  /* AK7739_C0_212_MASTER_SPI_TX02                  */
  { 0x0213, 0x00 },  /* AK7739_C0_213_MASTER_SPI_TX03                  */
  { 0x0214, 0x00 },  /* AK7739_C0_214_MASTER_SPI_TX04                  */
  { 0x0215, 0x00 },  /* AK7739_C0_215_MASTER_SPI_TX05                  */
  { 0x0216, 0x00 },  /* AK7739_C0_216_MASTER_SPI_TX06                  */
  { 0x0217, 0x00 },  /* AK7739_C0_217_MASTER_SPI_TX07                  */
  { 0x0218, 0x00 },  /* AK7739_C0_218_MASTER_SPI_TX08                  */
  { 0x0219, 0x00 },  /* AK7739_C0_219_MASTER_SPI_TX09                  */
  { 0x021A, 0x00 },  /* AK7739_C0_21A_MASTER_SPI_TX10                  */
  { 0x021B, 0x00 },  /* AK7739_C0_21B_MASTER_SPI_TX11                  */
  { 0x021C, 0x00 },  /* AK7739_C0_21C_MASTER_SPI_TX12                  */
  { 0x021D, 0x00 },  /* AK7739_C0_21D_MASTER_SPI_TX13                  */
  { 0x021E, 0x00 },  /* AK7739_C0_21E_MASTER_SPI_TX14                  */
  { 0x021F, 0x00 },  /* AK7739_C0_21F_MASTER_SPI_TX15                  */
  { 0x0220, 0x00 },  /* AK7739_C0_220_MASTER_SPI_TX16                  */
  { 0x0221, 0x00 },  /* AK7739_C0_221_MASTER_SPI_TX17                  */
  { 0x0222, 0x00 },  /* AK7739_C0_222_MASTER_SPI_TX18                  */
  { 0x0223, 0x00 },  /* AK7739_C0_223_MASTER_SPI_TX19                  */
  { 0x0224, 0x00 },  /* AK7739_C0_224_MASTER_SPI_TX20                  */
  { 0x0225, 0x00 },  /* AK7739_C0_225_MASTER_SPI_TX21                  */
  { 0x0226, 0x00 },  /* AK7739_C0_226_MASTER_SPI_TX22                  */
  { 0x0227, 0x00 },  /* AK7739_C0_227_MASTER_SPI_TX23                  */
  { 0x0228, 0x00 },  /* AK7739_C0_228_MASTER_SPI_TX24                  */
  { 0x0229, 0x00 },  /* AK7739_C0_229_MASTER_SPI_TX25                  */
  { 0x022A, 0x00 },  /* AK7739_C0_22A_MASTER_SPI_TX26                  */
  { 0x022B, 0x00 },  /* AK7739_C0_22B_MASTER_SPI_TX27                  */
  { 0x022C, 0x00 },  /* AK7739_C0_22C_MASTER_SPI_TX28                  */
  { 0x022D, 0x00 },  /* AK7739_C0_22D_MASTER_SPI_TX29                  */
  { 0x022E, 0x00 },  /* AK7739_C0_22E_MASTER_SPI_TX30                  */
  { 0x022F, 0x00 },  /* AK7739_C0_22F_MASTER_SPI_TX31                  */
  { 0x0300, 0x39 },	 /* AK7739_C0_300_DEVICE_ID                        */
  { 0x0301, 0x00 },	 /* AK7739_C0_301_REVISION_NUM                     */
  { 0x0302, 0x00 },	 /* AK7739_C0_302_CRC_ERROR_STATUS                 */
  { 0x0303, 0x01 },	 /* AK7739_C0_303_STO_READ_OUT                     */
  { 0x030F, 0x30 },	 /* AK7739_C0_30F_MASTER_SPI_STATUS                */
  { 0x0310, 0x00 },	 /* AK7739_C0_310_MASTER_SPI_RX00                  */
  { 0x0311, 0x00 },	 /* AK7739_C0_311_MASTER_SPI_RX01                  */
  { 0x0312, 0x00 },	 /* AK7739_C0_312_MASTER_SPI_RX02                  */
  { 0x0313, 0x00 },  /* AK7739_C0_313_MASTER_SPI_RX03                  */
  { 0x0314, 0x00 },  /* AK7739_C0_314_MASTER_SPI_RX04                  */
  { 0x0315, 0x00 },  /* AK7739_C0_315_MASTER_SPI_RX05                  */
  { 0x0316, 0x00 },  /* AK7739_C0_316_MASTER_SPI_RX06                  */
  { 0x0317, 0x00 },  /* AK7739_C0_317_MASTER_SPI_RX07                  */
  { 0x0318, 0x00 },  /* AK7739_C0_318_MASTER_SPI_RX08                  */
  { 0x0319, 0x00 },  /* AK7739_C0_319_MASTER_SPI_RX09                  */
  { 0x031A, 0x00 },  /* AK7739_C0_31A_MASTER_SPI_RX10                  */
  { 0x031B, 0x00 },  /* AK7739_C0_31B_MASTER_SPI_RX11                  */
  { 0x031C, 0x00 },  /* AK7739_C0_31C_MASTER_SPI_RX12                  */
  { 0x031D, 0x00 },  /* AK7739_C0_31D_MASTER_SPI_RX13                  */
  { 0x031E, 0x00 },  /* AK7739_C0_31E_MASTER_SPI_RX14                  */
  { 0x031F, 0x00 },  /* AK7739_C0_31F_MASTER_SPI_RX15                  */
  { 0x0320, 0x00 },	 /* AK7739_C0_320_MASTER_SPI_RX16                  */
  { 0x0321, 0x00 },  /* AK7739_C0_321_MASTER_SPI_RX17                  */
  { 0x0322, 0x00 },	 /* AK7739_C0_322_MASTER_SPI_RX18                  */
  { 0x0323, 0x00 },  /* AK7739_C0_323_MASTER_SPI_RX19                  */
  { 0x0324, 0x00 },  /* AK7739_C0_324_MASTER_SPI_RX20                  */
  { 0x0325, 0x00 },  /* AK7739_C0_325_MASTER_SPI_RX21                  */
  { 0x0326, 0x00 },  /* AK7739_C0_326_MASTER_SPI_RX22                  */
  { 0x0327, 0x00 },  /* AK7739_C0_327_MASTER_SPI_RX23                  */
  { 0x0328, 0x00 },  /* AK7739_C0_328_MASTER_SPI_RX24                  */
  { 0x0329, 0x00 },  /* AK7739_C0_329_MASTER_SPI_RX25                  */
  { 0x032A, 0x00 },  /* AK7739_C0_32A_MASTER_SPI_RX26                  */
  { 0x032B, 0x00 },  /* AK7739_C0_32B_MASTER_SPI_RX27                  */
  { 0x032C, 0x00 },  /* AK7739_C0_32C_MASTER_SPI_RX28                  */
  { 0x032D, 0x00 },  /* AK7739_C0_32D_MASTER_SPI_RX29                  */
  { 0x032E, 0x00 },  /* AK7739_C0_32E_MASTER_SPI_RX30                  */
  { 0x032F, 0x00 },  /* AK7739_C0_32F_MASTER_SPI_RX31                  */

  /* DSP block */
  { 0x1000, 0x01 },  /* AK7739_C1_000_DSP_RESET_CONTROL                */
  { 0x1001, 0x00 },  /* AK7739_C1_001_DSP_CLOCK_SETTING                */
  { 0x1002, 0x00 },  /* AK7739_C1_002_RAM_CLEAR_SETTING                */
  { 0x1003, 0x00 },  /* AK7739_C1_003_DSP_WATCHDOG_TIMER_FLAG_SETTING  */
  { 0x1004, 0x00 },  /* AK7739_C1_004_DSP_GPO_SETTING                  */
  { 0x1005, 0x00 },  /* AK7739_C1_005_DSP1,2,3_GPO_STATUS              */
  { 0x1008, 0x06 },  /* AK7739_C1_008_DSP_WATCHDOG_TIMER_ERROR_STATUS  */
  { 0x1011, 0x00 },  /* AK7739_C1_011_DSP1_DRAM_SETTING                */
  { 0x1012, 0x00 },  /* AK7739_C1_012_DSP2_DRAM_SETTING                */
  { 0x1013, 0x00 },  /* AK7739_C1_013_DSP3_DRAM_SETTING                */
  { 0x1020, 0x04 },  /* AK7739_C1_020_DSP1,2_DLYRAM_ASSIGNMENT         */
  { 0x1021, 0x00 },  /* AK7739_C1_021_DSP1_DLYRAM_SETTING              */
  { 0x1022, 0x00 },  /* AK7739_C1_022_DSP2_DLYRAM_SETTING              */
  { 0x1031, 0x10 },  /* AK7739_C1_031_DSP1_CRAM&DLP0_SETTING           */
  { 0x1032, 0x00 },  /* AK7739_C1_032_DSP2_CRAM&DLP0_SETTING           */
  { 0x1033, 0x00 },  /* AK7739_C1_033_DSP3_CRAM_SETTING                */
  { 0x1040, 0x00 },  /* AK7739_C1_040_DSP1_JX_SETTING                  */
  { 0x1041, 0x00 },  /* AK7739_C1_041_DSP2_JX_SETTING                  */
  { 0x1042, 0x00 },  /* AK7739_C1_042_DSP3_JX_SETTING                  */

  /* SRC block */
  { 0x2000, 0x00 },  /* AK7739_C2_000_SRC_POWER_MANAGEMENT             */
  { 0x2001, 0x00 },  /* AK7739_C2_001_SRC_FILTER_SETTING1              */
  { 0x2003, 0x00 },  /* AK7739_C2_003_SRC_PHASE_GROUP1                 */
  { 0x2005, 0x00 },  /* AK7739_C2_005_SRC_MUTE_SETTING1                */
  { 0x2006, 0x00 },  /* AK7739_C2_006_SRC_MUTE_SETTING2                */
  { 0x2007, 0x00 },  /* AK7739_C2_007_SRC_STO_FLAG_SETTING             */
  { 0x2010, 0x00 },  /* AK7739_C2_010_SRC_STATUS1                      */
  { 0x2101, 0x00 },  /* AK7739_C2_101_MONO_SRC_POWER_MANAGEMENT        */
  { 0x2102, 0x00 },  /* AK7739_C2_102_MONO_SRC_FILTER_SETTING          */
  { 0x2103, 0x00 },  /* AK7739_C2_103_MONO_SRC_PHASE_GROUP             */
  { 0x2104, 0x00 },  /* AK7739_C2_104_MONO_SRC_MUTE_SETTING            */
  { 0x2105, 0x00 },  /* AK7739_C2_105_MONO_SRC_STO_FLAG_SETTING        */
  { 0x2106, 0x00 },  /* AK7739_C2_106_MONO_SRC_PATH_SETTING            */
  { 0x2110, 0x00 },  /* AK7739_C2_110_MONO_SRC_STATUS1                 */
  { 0x2200, 0x00 },  /* AK7739_C2_200_DIT_POWER_MANAGEMENT             */
  { 0x2201, 0x00 },  /* AK7739_C2_201_DIT_STATUS_BIT1                  */
  { 0x2202, 0x04 },  /* AK7739_C2_202_DIT_STATUS_BIT2                  */
  { 0x2203, 0x02 },  /* AK7739_C2_203_DIT_STATUS_BIT3                  */
  { 0x2204, 0x00 },  /* AK7739_C2_204_DIT_STATUS_BIT4                  */
  { 0x2210, 0x00 },  /* AK7739_C2_210_MIXER1_SETTING                   */
  { 0x2211, 0x00 },  /* AK7739_C2_211_MIXER2_SETTING                   */

  /* CODEC block */
  { 0x3000, 0x36 },  /* AK7739_C3_000_POWER_MANAGEMENT                 */
  { 0x3001, 0x20 },  /* AK7739_C3_001_MICBIAS_POWER_MANAGEMENT         */
  { 0x3002, 0x01 },  /* AK7739_C3_002_RESET_CONTROL                    */
  { 0x3003, 0x01 },  /* AK7739_C3_003_SYSTEM_CLOCK_SETTING             */
  { 0x3004, 0x11 },  /* AK7739_C3_004_MIC_AMP_GAIN                     */
  { 0x3005, 0x30 },  /* AK7739_C3_005_ADC1_LCH_DIGITAL_VOLUME          */
  { 0x3006, 0x30 },  /* AK7739_C3_006_ADC1_RCH_DIGITAL_VOLUME          */
  { 0x3007, 0x30 },  /* AK7739_C3_007_ADC2_LCH_DIGITAL_VOLUME          */
  { 0x3008, 0x30 },  /* AK7739_C3_008_ADC2_RCH_DIGITAL_VOLUME          */
  { 0x3009, 0x04 },  /* AK7739_C3_009_ADC_DIGITAL_FILTER_SETTING       */
  { 0x300A, 0x15 },  /* AK7739_C3_00A_ADC_ANALOG_INPUT_SETTING         */
  { 0x300B, 0x00 },  /* AK7739_C3_00B_ADC_MUTE&HPF_CONTROL             */
  { 0x300C, 0x18 },  /* AK7739_C3_00C_DAC1_LCH_DIGITAL_VOLUME          */
  { 0x300D, 0x18 },  /* AK7739_C3_00D_DAC1_RCH_DIGITAL_VOLUME          */
  { 0x300E, 0x18 },  /* AK7739_C3_00E_DAC2_LCH_DIGITAL_VOLUME          */
  { 0x300F, 0x18 },  /* AK7739_C3_00F_DAC2_RCH_DIGITAL_VOLUME          */
  { 0x3013, 0x15 },  /* AK7739_C3_013_DAC_De-EMPHASIS_SETTING          */
  { 0x3014, 0x02 },  /* AK7739_C3_014_DAC_MUTE&FILTER_SETTING          */
  { 0x3015, 0x00 },  /* AK7739_C3_015_DIGITAL_MIC_CONTROL              */

// VIRT Register for DSP Connection
  { 0x3016, 0x00 },  /* AK7739_VIRT_C3_016_DSP1OUT1_MIX              */
  { 0x3017, 0x00 },  /* AK7739_VIRT_C3_017_DSP1OUT2_MIX              */
  { 0x3018, 0x00 },  /* AK7739_VIRT_C3_018_DSP1OUT3_MIX              */
  { 0x3019, 0x00 },  /* AK7739_VIRT_C3_019_DSP1OUT4_MIX              */
  { 0x301A, 0x00 },  /* AK7739_VIRT_C3_01A_DSP1OUT5_MIX              */
  { 0x301B, 0x00 },  /* AK7739_VIRT_C3_01B_DSP1OUT6_MIX              */
  { 0x301C, 0x00 },  /* AK7739_VIRT_C3_01C_DSP2OUT1_MIX              */
  { 0x301D, 0x00 },  /* AK7739_VIRT_C3_01D_DSP2OUT2_MIX              */
  { 0x301E, 0x00 },  /* AK7739_VIRT_C3_01E_DSP2OUT3_MIX              */
  { 0x301F, 0x00 },  /* AK7739_VIRT_C3_01F_DSP2OUT4_MIX              */
  { 0x3020, 0x00 },  /* AK7739_VIRT_C3_020_DSP2OUT5_MIX              */
  { 0x3021, 0x00 },  /* AK7739_VIRT_C3_021_DSP2OUT6_MIX              */
};

/* ak7739 register cache & default register settings */
static const struct reg_default ak7739_eq_reg[] = {
	/* AUDIOHUB,PERI block */
  { 0x0000, 0x03 },  /* AK7739_C0_000_SYSCLOCK_SETTING1                */
  { 0x0001, 0x01 },  /* AK7739_C0_001_SYSCLOCK_SETTING2                */
  { 0x0002, 0x03 },  /* AK7739_C0_002_SYSCLOCK_SETTING3                */
  { 0x0003, 0x5c },  /* AK7739_C0_003_SYSCLOCK_SETTING4                */
  { 0x0004, 0x00 },  /* AK7739_C0_004_SYSCLOCK_SETTING5                */
  { 0x0008, 0x01 },  /* AK7739_C0_008_AHCLK_SETTING5                   */
  { 0x0009, 0x01 },  /* AK7739_C0_009_BUSCLOCK_SETTING                 */
  { 0x000A, 0x02 },  /* AK7739_C0_00A_BUSCLOCK_SETTING2                */
  { 0x000B, 0x00 },  /* AK7739_C0_00B_CLKO_SETTING1                    */
  { 0x000C, 0x00 },  /* AK7739_C0_00C_CLKO_SETTING2                    */
  { 0x000D, 0x02 },  /* AK7739_C0_00D_MasterSPI_SCLK_SETTING1          */
  { 0x000E, 0x01 },  /* AK7739_C0_00E_MasterSPI_SCLK_SETTING2          */
  { 0x0010, 0x80 },  /* AK7739_C0_010_SYNCDOMAIN1_SETTING1             */
  { 0x0011, 0x01 },  /* AK7739_C0_011_SYNCDOMAIN1_SETTING2             */
  { 0x0012, 0x17 },  /* AK7739_C0_012_SYNCDOMAIN1_SETTING3             */
  { 0x0013, 0x00 },  /* AK7739_C0_013_SYNCDOMAIN1_SETTING4             */
  { 0x0014, 0x80 },  /* AK7739_C0_014_SYNCDOMAIN2_SETTING1             */
  { 0x0015, 0x01 },  /* AK7739_C0_015_SYNCDOMAIN2_SETTING2             */
  { 0x0016, 0x05 },  /* AK7739_C0_016_SYNCDOMAIN2_SETTING3             */
  { 0x0017, 0x04 },  /* AK7739_C0_017_SYNCDOMAIN2_SETTING4             */
  { 0x0018, 0x80 },  /* AK7739_C0_018_SYNCDOMAIN3_SETTING1             */
  { 0x0019, 0x01 },  /* AK7739_C0_019_SYNCDOMAIN3_SETTING2             */
  { 0x001A, 0x17 },  /* AK7739_C0_01A_SYNCDOMAIN3_SETTING3             */
  { 0x001B, 0x00 },  /* AK7739_C0_01B_SYNCDOMAIN3_SETTING4             */
  { 0x001C, 0x00 },  /* AK7739_C0_01C_SYNCDOMAIN4_SETTING1             */
  { 0x001D, 0x00 },  /* AK7739_C0_01D_SYNCDOMAIN4_SETTING2             */
  { 0x001E, 0x00 },  /* AK7739_C0_01E_SYNCDOMAIN4_SETTING3             */
  { 0x001F, 0x00 },  /* AK7739_C0_01F_SYNCDOMAIN4_SETTING4             */
  { 0x0020, 0x00 },  /* AK7739_C0_020_SYNCDOMAIN5_SETTING1             */
  { 0x0021, 0x00 },  /* AK7739_C0_021_SYNCDOMAIN5_SETTING2             */
  { 0x0022, 0x00 },  /* AK7739_C0_022_SYNCDOMAIN5_SETTING3             */
  { 0x0023, 0x00 },  /* AK7739_C0_023_SYNCDOMAIN5_SETTING4             */
  { 0x0025, 0x00 },  /* AK7739_C0_025_SYNCDOMAIN6_SETTING2             */
  { 0x0026, 0x00 },  /* AK7739_C0_026_SYNCDOMAIN6_SETTING3             */
  { 0x0027, 0x00 },  /* AK7739_C0_027_SYNCDOMAIN6_SETTING4             */
  { 0x0029, 0x01 },  /* AK7739_C0_029_SYNCDOMAIN7_SETTING2             */
  { 0x002A, 0x47 },  /* AK7739_C0_02A_SYNCDOMAIN7_SETTING3             */
  { 0x002B, 0x00 },  /* AK7739_C0_02B_SYNCDOMAIN7_SETTING4             */
  { 0x0040, 0x00 },  /* AK7739_C0_040_BICK_FORMAT_SETTING1             */
  { 0x0041, 0x01 },  /* AK7739_C0_041_BICK_SYNCDOMAIN_SELECT1          */
  { 0x0042, 0x00 },  /* AK7739_C0_042_BICK_FORMAT_SETTING2             */
  { 0x0043, 0x02 },  /* AK7739_C0_043_BICK_SYNCDOMAIN_SELECT2          */
  { 0x0044, 0x00 },  /* AK7739_C0_044_BICK_FORMAT_SETTING3             */
  { 0x0045, 0x00 },  /* AK7739_C0_045_BICK_SYNCDOMAIN_SELECT3          */
  { 0x0046, 0x00 },  /* AK7739_C0_046_BICK_FORMAT_SETTING4             */
  { 0x0047, 0x00 },  /* AK7739_C0_047_BICK_SYNCDOMAIN_SELECT4          */
  { 0x0048, 0x00 },  /* AK7739_C0_048_BICK_FORMAT_SETTING5             */
  { 0x0049, 0x00 },  /* AK7739_C0_049_BICK_SYNCDOMAIN_SELECT5          */
  { 0x0050, 0x00 },  /* AK7739_C0_050_SDIN1_INPUT_FORMAT               */
  { 0x0051, 0x01 },  /* AK7739_C0_051_SDIN1_SYNCDOMAIN_SELECT          */
  { 0x0052, 0x00 },  /* AK7739_C0_052_SDIN2_INPUT_FORMAT               */
  { 0x0053, 0x00 },  /* AK7739_C0_053_SDIN2_SYNCDOMAIN_SELECT          */
  { 0x0054, 0x00 },  /* AK7739_C0_054_SDIN3_INPUT_FORMAT               */
  { 0x0055, 0x00 },  /* AK7739_C0_055_SDIN3_SYNCDOMAIN_SELECT          */
  { 0x0056, 0x00 },  /* AK7739_C0_056_SDIN4_INPUT_FORMAT               */
  { 0x0057, 0x00 },  /* AK7739_C0_057_SDIN4_SYNCDOMAIN_SELECT          */
  { 0x0058, 0x00 },  /* AK7739_C0_058_SDIN5_INPUT_FORMAT               */
  { 0x0059, 0x00 },  /* AK7739_C0_059_SDIN5_SYNCDOMAIN_SELECT          */
  { 0x005A, 0x00 },  /* AK7739_C0_05A_SDIN6_INPUT_FORMAT               */
  { 0x005B, 0x00 },  /* AK7739_C0_05B_SDIN6_SYNCDOMAIN_SELECT          */
  { 0x0060, 0x00 },  /* AK7739_C0_060_SDOUT1_OUTPUT_FORMAT             */
  { 0x0061, 0x00 },  /* AK7739_C0_061_SDOUT1_SYNCDOMAIN_SELECT         */
  { 0x0062, 0xB0 },  /* AK7739_C0_062_SDOUT2_OUTPUT_FORMAT             */
  { 0x0063, 0x02 },  /* AK7739_C0_063_SDOUT2_SYNCDOMAIN_SELECT         */
  { 0x0064, 0x00 },  /* AK7739_C0_064_SDOUT3_OUTPUT_FORMAT             */
  { 0x0065, 0x00 },  /* AK7739_C0_065_SDOUT3_SYNCDOMAIN_SELECT         */
  { 0x0066, 0x00 },  /* AK7739_C0_066_SDOUT4_OUTPUT_FORMAT             */
  { 0x0067, 0x00 },  /* AK7739_C0_067_SDOUT4_SYNCDOMAIN_SELECT         */
  { 0x0068, 0x00 },  /* AK7739_C0_068_SDOUT5_OUTPUT_FORMAT             */
  { 0x0069, 0x00 },  /* AK7739_C0_069_SDOUT5_SYNCDOMAIN_SELECT         */
  { 0x006A, 0x00 },  /* AK7739_C0_06A_SDOUT6_OUTPUT_FORMAT             */
  { 0x006B, 0x07 },  /* AK7739_C0_06B_SDOUT6_SYNCDOMAIN_SELECT         */
  { 0x0087, 0x00 },  /* AK7739_C0_087_OSMEM_SYNCDOMAIN_SELECT          */
  { 0x0088, 0x01 },  /* AK7739_C0_088_ADC1_SYNCDOMAIN_SELECT           */
  { 0x0089, 0x01 },  /* AK7739_C0_089_CODEC_SYNCDOMAIN_SELECT          */
  { 0x0098, 0x07 },  /* AK7739_C0_098_MIXER_CH1_INPUT_SELECT           */
  { 0x0099, 0x00 },  /* AK7739_C0_099_MIXER_CH1_INPUT_SELECT           */
  { 0x00A0, 0x03 },  /* AK7739_C0_0A0_SRC1_SYNCDOMAIN_SELECT           */
  { 0x00A1, 0x02 },  /* AK7739_C0_0A1_SRC2_SYNCDOMAIN_SELECT           */
  { 0x00A2, 0x00 },  /* AK7739_C0_0A2_SRC3_SYNCDOMAIN_SELECT           */
  { 0x00A3, 0x00 },  /* AK7739_C0_0A3_SRC4_SYNCDOMAIN_SELECT           */
  { 0x00A4, 0x00 },  /* AK7739_C0_0A4_SRC5_SYNCDOMAIN_SELECT           */
  { 0x00A5, 0x00 },  /* AK7739_C0_0A5_SRC6_SYNCDOMAIN_SELECT           */
  { 0x00A6, 0x00 },  /* AK7739_C0_0A6_SRC7_SYNCDOMAIN_SELECT           */
  { 0x00A7, 0x00 },  /* AK7739_C0_0A7_SRC8_SYNCDOMAIN_SELECT           */
  { 0x00B0, 0x01 },  /* AK7739_C0_0B0_DSP1_SYNCDOMAIN_SELECT           */
  { 0x00B1, 0x01 },  /* AK7739_C0_0B1_DSP2_SYNCDOMAIN_SELECT           */
  { 0x00C0, 0x01 },  /* AK7739_C0_0C0_DSP1_OUTPUT_SYNCDOMAIN_SELECT1   */
  { 0x00C1, 0x01 },  /* AK7739_C0_0C1_DSP1_OUTPUT_SYNCDOMAIN_SELECT2   */
  { 0x00C2, 0x00 },  /* AK7739_C0_0C2_DSP1_OUTPUT_SYNCDOMAIN_SELECT3   */
  { 0x00C3, 0x00 },  /* AK7739_C0_0C3_DSP1_OUTPUT_SYNCDOMAIN_SELECT4   */
  { 0x00C4, 0x00 },  /* AK7739_C0_0C4_DSP1_OUTPUT_SYNCDOMAIN_SELECT5   */
  { 0x00C5, 0x00 },  /* AK7739_C0_0C5_DSP1_OUTPUT_SYNCDOMAIN_SELECT6   */
  { 0x00C6, 0x01 },  /* AK7739_C0_0C6_DSP2_OUTPUT_SYNCDOMAIN_SELECT1   */
  { 0x00C7, 0x00 },  /* AK7739_C0_0C7_DSP2_OUTPUT_SYNCDOMAIN_SELECT2   */
  { 0x00C8, 0x00 },  /* AK7739_C0_0C8_DSP2_OUTPUT_SYNCDOMAIN_SELECT3   */
  { 0x00C9, 0x00 },  /* AK7739_C0_0C9_DSP2_OUTPUT_SYNCDOMAIN_SELECT4   */
  { 0x00CA, 0x00 },  /* AK7739_C0_0CA_DSP2_OUTPUT_SYNCDOMAIN_SELECT5   */
  { 0x00CB, 0x00 },  /* AK7739_C0_0CB_DSP2_OUTPUT_SYNCDOMAIN_SELECT6   */
  { 0x0106, 0x00 },  /* AK7739_C0_106_DIT_INPUT_DATA_SELECT            */
  { 0x0108, 0x18 },  /* AK7739_C0_108_DAC1_INPUT_DATA_SELECT           */
  { 0x0109, 0x18 },  /* AK7739_C0_109_DAC2_INPUT_DATA_SELECT           */
  { 0x0118, 0x80 },  /* AK7739_C0_118_MIXER1_CHA_INPUT_DATA_SELECT     */
  { 0x0119, 0x09 },  /* AK7739_C0_119_MIXER1_CHB_INPUT_DATA_SELECT     */
  { 0x011A, 0x00 },  /* AK7739_C0_11A_MIXER2_CHA_INPUT_DATA_SELECT     */
  { 0x011B, 0x00 },  /* AK7739_C0_11B_MIXER2_CHB_INPUT_DATA_SELECT     */
  { 0x0120, 0x40 },  /* AK7739_C0_120_SRC1_INPUT_DATA_SELECT           */
  { 0x0121, 0x08 },  /* AK7739_C0_121_SRC2_INPUT_DATA_SELECT           */
  { 0x0122, 0x00 },  /* AK7739_C0_122_SRC3_INPUT_DATA_SELECT           */
  { 0x0123, 0x00 },  /* AK7739_C0_123_SRC4_INPUT_DATA_SELECT           */
  { 0x0124, 0x00 },  /* AK7739_C0_124_SRC5_INPUT_DATA_SELECT           */
  { 0x0125, 0x00 },  /* AK7739_C0_125_SRC6_INPUT_DATA_SELECT           */
  { 0x0126, 0x00 },  /* AK7739_C0_126_SRC7_INPUT_DATA_SELECT           */
  { 0x0127, 0x00 },  /* AK7739_C0_127_SRC8_INPUT_DATA_SELECT           */
  { 0x0140, 0x00 },  /* AK7739_C0_140_SDOUT1A_OUTPUT_DATA_SELECT       */
  { 0x0141, 0x00 },  /* AK7739_C0_141_SDOUT1B_OUTPUT_DATA_SELECT       */
  { 0x0142, 0x00 },  /* AK7739_C0_142_SDOUT1C_OUTPUT_DATA_SELECT       */
  { 0x0143, 0x00 },  /* AK7739_C0_143_SDOUT1D_OUTPUT_DATA_SELECT       */
  { 0x0144, 0x00 },  /* AK7739_C0_144_SDOUT1E_OUTPUT_DATA_SELECT       */
  { 0x0145, 0x00 },  /* AK7739_C0_145_SDOUT1F_OUTPUT_DATA_SELECT       */
  { 0x0146, 0x00 },  /* AK7739_C0_146_SDOUT1G_OUTPUT_DATA_SELECT       */
  { 0x0147, 0x00 },  /* AK7739_C0_147_SDOUT1H_OUTPUT_DATA_SELECT       */
  { 0x0148, 0x86 },  /* AK7739_C0_148_SDOUT2A_OUTPUT_DATA_SELECT       */
  { 0x0149, 0x21 },  /* AK7739_C0_149_SDOUT2B_OUTPUT_DATA_SELECT       */
  { 0x014A, 0x21 },  /* AK7739_C0_14A_SDOUT2C_OUTPUT_DATA_SELECT       */
  { 0x014B, 0x00 },  /* AK7739_C0_14B_SDOUT2D_OUTPUT_DATA_SELECT       */
  { 0x014C, 0x00 },  /* AK7739_C0_14C_SDOUT2E_OUTPUT_DATA_SELECT       */
  { 0x014D, 0x00 },  /* AK7739_C0_14D_SDOUT2F_OUTPUT_DATA_SELECT       */
  { 0x014E, 0x00 },  /* AK7739_C0_14E_SDOUT2G_OUTPUT_DATA_SELECT       */
  { 0x014F, 0x00 },  /* AK7739_C0_14F_SDOUT2H_OUTPUT_DATA_SELECT       */
  { 0x0150, 0x00 },  /* AK7739_C0_150_SDOUT3A_OUTPUT_DATA_SELECT       */
  { 0x0151, 0x00 },  /* AK7739_C0_151_SDOUT3B_OUTPUT_DATA_SELECT       */
  { 0x0152, 0x00 },  /* AK7739_C0_152_SDOUT3C_OUTPUT_DATA_SELECT       */
  { 0x0153, 0x00 },  /* AK7739_C0_153_SDOUT3D_OUTPUT_DATA_SELECT       */
  { 0x0154, 0x00 },  /* AK7739_C0_154_SDOUT3E_OUTPUT_DATA_SELECT       */
  { 0x0155, 0x00 },  /* AK7739_C0_155_SDOUT3F_OUTPUT_DATA_SELECT       */
  { 0x0156, 0x00 },  /* AK7739_C0_156_SDOUT3G_OUTPUT_DATA_SELECT       */
  { 0x0157, 0x00 },  /* AK7739_C0_157_SDOUT3H_OUTPUT_DATA_SELECT       */
  { 0x0158, 0x00 },  /* AK7739_C0_158_SDOUT4A_OUTPUT_DATA_SELECT       */
  { 0x0159, 0x00 },  /* AK7739_C0_159_SDOUT4B_OUTPUT_DATA_SELECT       */
  { 0x015A, 0x00 },  /* AK7739_C0_15A_SDOUT4C_OUTPUT_DATA_SELECT       */
  { 0x015B, 0x00 },  /* AK7739_C0_15B_SDOUT4D_OUTPUT_DATA_SELECT       */
  { 0x015C, 0x00 },  /* AK7739_C0_15C_SDOUT4E_OUTPUT_DATA_SELECT       */
  { 0x015D, 0x00 },  /* AK7739_C0_15D_SDOUT4F_OUTPUT_DATA_SELECT       */
  { 0x015E, 0x00 },  /* AK7739_C0_15E_SDOUT4G_OUTPUT_DATA_SELECT       */
  { 0x015F, 0x00 },  /* AK7739_C0_15F_SDOUT4H_OUTPUT_DATA_SELECT       */
  { 0x0160, 0x00 },  /* AK7739_C0_160_SDOUT5A_OUTPUT_DATA_SELECT       */
  { 0x0161, 0x00 },  /* AK7739_C0_161_SDOUT5B_OUTPUT_DATA_SELECT       */
  { 0x0162, 0x00 },  /* AK7739_C0_162_SDOUT5C_OUTPUT_DATA_SELECT       */
  { 0x0163, 0x00 },  /* AK7739_C0_163_SDOUT5D_OUTPUT_DATA_SELECT       */
  { 0x0164, 0x00 },  /* AK7739_C0_164_SDOUT5E_OUTPUT_DATA_SELECT       */
  { 0x0165, 0x00 },  /* AK7739_C0_165_SDOUT5F_OUTPUT_DATA_SELECT       */
  { 0x0166, 0x00 },  /* AK7739_C0_166_SDOUT5G_OUTPUT_DATA_SELECT       */
  { 0x0167, 0x00 },  /* AK7739_C0_167_SDOUT5H_OUTPUT_DATA_SELECT       */
  { 0x0168, 0x00 },  /* AK7739_C0_168_SDOUT6A_OUTPUT_DATA_SELECT       */
  { 0x0169, 0x00 },  /* AK7739_C0_169_SDOUT6B_OUTPUT_DATA_SELECT       */
  { 0x016A, 0x00 },  /* AK7739_C0_16A_SDOUT6C_OUTPUT_DATA_SELECT       */
  { 0x016B, 0x00 },  /* AK7739_C0_16B_SDOUT6D_OUTPUT_DATA_SELECT       */
  { 0x016C, 0x00 },  /* AK7739_C0_16C_SDOUT6E_OUTPUT_DATA_SELECT       */
  { 0x016D, 0x00 },  /* AK7739_C0_16D_SDOUT6F_OUTPUT_DATA_SELECT       */
  { 0x016E, 0x00 },  /* AK7739_C0_16E_SDOUT6G_OUTPUT_DATA_SELECT       */
  { 0x016F, 0x00 },  /* AK7739_C0_16F_SDOUT6H_OUTPUT_DATA_SELECT       */
  { 0x0180, 0x08 },  /* AK7739_C0_180_DSP1_DIN1_INPUT_DATA_SELECT      */
  { 0x0181, 0x09 },  /* AK7739_C0_181_DSP1_DIN2_INPUT_DATA_SELECT      */
  { 0x0182, 0x00 },  /* AK7739_C0_182_DSP1_DIN3_INPUT_DATA_SELECT      */
  { 0x0183, 0x00 },  /* AK7739_C0_183_DSP1_DIN4_INPUT_DATA_SELECT      */
  { 0x0184, 0x00 },  /* AK7739_C0_184_DSP1_DIN5_INPUT_DATA_SELECT      */
  { 0x0185, 0x00 },  /* AK7739_C0_185_DSP1_DIN6_INPUT_DATA_SELECT      */
  { 0x0186, 0x08 },  /* AK7739_C0_186_DSP2_DIN1_INPUT_DATA_SELECT      */
  { 0x0187, 0x00 },  /* AK7739_C0_187_DSP2_DIN2_INPUT_DATA_SELECT      */
  { 0x0188, 0x00 },  /* AK7739_C0_188_DSP2_DIN3_INPUT_DATA_SELECT      */
  { 0x0189, 0x00 },  /* AK7739_C0_189_DSP2_DIN4_INPUT_DATA_SELECT      */
  { 0x018A, 0x00 },  /* AK7739_C0_18A_DSP2_DIN5_INPUT_DATA_SELECT      */
  { 0x018B, 0x00 },  /* AK7739_C0_18B_DSP2_DIN6_INPUT_DATA_SELECT      */
  { 0x0200, 0x00 },  /* AK7739_C0_200_SDOUT_OUTPUT_DATA_SELECT         */
  { 0x0201, 0xFC },  /* AK7739_C0_201_SDOUT_ENABLE_SETTING             */
  { 0x0202, 0x00 },  /* AK7739_C0_202_SDOUT_OUTPUT_MODE_SETTING        */
  { 0x0203, 0x00 },  /* AK7739_C0_203_SDIN_INPUT_DATA_SELECT           */
  { 0x0204, 0x00 },  /* AK7739_C0_204_MASTER_SPI_SELECT                */
  { 0x0205, 0x00 },  /* AK7739_C0_205_STO_FLAG_SETTING                 */
  { 0x0206, 0x00 },  /* AK7739_C0_206_LRCK4_5_OUTPUT_DATA_SELECT       */
  { 0x0210, 0x00 },  /* AK7739_C0_210_MASTER_SPI_TX00                  */
  { 0x0211, 0x00 },  /* AK7739_C0_211_MASTER_SPI_TX01                  */
  { 0x0212, 0x00 },  /* AK7739_C0_212_MASTER_SPI_TX02                  */
  { 0x0213, 0x00 },  /* AK7739_C0_213_MASTER_SPI_TX03                  */
  { 0x0214, 0x00 },  /* AK7739_C0_214_MASTER_SPI_TX04                  */
  { 0x0215, 0x00 },  /* AK7739_C0_215_MASTER_SPI_TX05                  */
  { 0x0216, 0x00 },  /* AK7739_C0_216_MASTER_SPI_TX06                  */
  { 0x0217, 0x00 },  /* AK7739_C0_217_MASTER_SPI_TX07                  */
  { 0x0218, 0x00 },  /* AK7739_C0_218_MASTER_SPI_TX08                  */
  { 0x0219, 0x00 },  /* AK7739_C0_219_MASTER_SPI_TX09                  */
  { 0x021A, 0x00 },  /* AK7739_C0_21A_MASTER_SPI_TX10                  */
  { 0x021B, 0x00 },  /* AK7739_C0_21B_MASTER_SPI_TX11                  */
  { 0x021C, 0x00 },  /* AK7739_C0_21C_MASTER_SPI_TX12                  */
  { 0x021D, 0x00 },  /* AK7739_C0_21D_MASTER_SPI_TX13                  */
  { 0x021E, 0x00 },  /* AK7739_C0_21E_MASTER_SPI_TX14                  */
  { 0x021F, 0x00 },  /* AK7739_C0_21F_MASTER_SPI_TX15                  */
  { 0x0220, 0x00 },  /* AK7739_C0_220_MASTER_SPI_TX16                  */
  { 0x0221, 0x00 },  /* AK7739_C0_221_MASTER_SPI_TX17                  */
  { 0x0222, 0x00 },  /* AK7739_C0_222_MASTER_SPI_TX18                  */
  { 0x0223, 0x00 },  /* AK7739_C0_223_MASTER_SPI_TX19                  */
  { 0x0224, 0x00 },  /* AK7739_C0_224_MASTER_SPI_TX20                  */
  { 0x0225, 0x00 },  /* AK7739_C0_225_MASTER_SPI_TX21                  */
  { 0x0226, 0x00 },  /* AK7739_C0_226_MASTER_SPI_TX22                  */
  { 0x0227, 0x00 },  /* AK7739_C0_227_MASTER_SPI_TX23                  */
  { 0x0228, 0x00 },  /* AK7739_C0_228_MASTER_SPI_TX24                  */
  { 0x0229, 0x00 },  /* AK7739_C0_229_MASTER_SPI_TX25                  */
  { 0x022A, 0x00 },  /* AK7739_C0_22A_MASTER_SPI_TX26                  */
  { 0x022B, 0x00 },  /* AK7739_C0_22B_MASTER_SPI_TX27                  */
  { 0x022C, 0x00 },  /* AK7739_C0_22C_MASTER_SPI_TX28                  */
  { 0x022D, 0x00 },  /* AK7739_C0_22D_MASTER_SPI_TX29                  */
  { 0x022E, 0x00 },  /* AK7739_C0_22E_MASTER_SPI_TX30                  */
  { 0x022F, 0x00 },  /* AK7739_C0_22F_MASTER_SPI_TX31                  */
  { 0x0300, 0x39 },	 /* AK7739_C0_300_DEVICE_ID                        */
  { 0x0301, 0x00 },	 /* AK7739_C0_301_REVISION_NUM                     */
  { 0x0302, 0x00 },	 /* AK7739_C0_302_CRC_ERROR_STATUS                 */
  { 0x0303, 0x01 },	 /* AK7739_C0_303_STO_READ_OUT                     */
  { 0x030F, 0x30 },	 /* AK7739_C0_30F_MASTER_SPI_STATUS                */
  { 0x0310, 0x00 },	 /* AK7739_C0_310_MASTER_SPI_RX00                  */
  { 0x0311, 0x00 },	 /* AK7739_C0_311_MASTER_SPI_RX01                  */
  { 0x0312, 0x00 },	 /* AK7739_C0_312_MASTER_SPI_RX02                  */
  { 0x0313, 0x00 },  /* AK7739_C0_313_MASTER_SPI_RX03                  */
  { 0x0314, 0x00 },  /* AK7739_C0_314_MASTER_SPI_RX04                  */
  { 0x0315, 0x00 },  /* AK7739_C0_315_MASTER_SPI_RX05                  */
  { 0x0316, 0x00 },  /* AK7739_C0_316_MASTER_SPI_RX06                  */
  { 0x0317, 0x00 },  /* AK7739_C0_317_MASTER_SPI_RX07                  */
  { 0x0318, 0x00 },  /* AK7739_C0_318_MASTER_SPI_RX08                  */
  { 0x0319, 0x00 },  /* AK7739_C0_319_MASTER_SPI_RX09                  */
  { 0x031A, 0x00 },  /* AK7739_C0_31A_MASTER_SPI_RX10                  */
  { 0x031B, 0x00 },  /* AK7739_C0_31B_MASTER_SPI_RX11                  */
  { 0x031C, 0x00 },  /* AK7739_C0_31C_MASTER_SPI_RX12                  */
  { 0x031D, 0x00 },  /* AK7739_C0_31D_MASTER_SPI_RX13                  */
  { 0x031E, 0x00 },  /* AK7739_C0_31E_MASTER_SPI_RX14                  */
  { 0x031F, 0x00 },  /* AK7739_C0_31F_MASTER_SPI_RX15                  */
  { 0x0320, 0x00 },	 /* AK7739_C0_320_MASTER_SPI_RX16                  */
  { 0x0321, 0x00 },  /* AK7739_C0_321_MASTER_SPI_RX17                  */
  { 0x0322, 0x00 },	 /* AK7739_C0_322_MASTER_SPI_RX18                  */
  { 0x0323, 0x00 },  /* AK7739_C0_323_MASTER_SPI_RX19                  */
  { 0x0324, 0x00 },  /* AK7739_C0_324_MASTER_SPI_RX20                  */
  { 0x0325, 0x00 },  /* AK7739_C0_325_MASTER_SPI_RX21                  */
  { 0x0326, 0x00 },  /* AK7739_C0_326_MASTER_SPI_RX22                  */
  { 0x0327, 0x00 },  /* AK7739_C0_327_MASTER_SPI_RX23                  */
  { 0x0328, 0x00 },  /* AK7739_C0_328_MASTER_SPI_RX24                  */
  { 0x0329, 0x00 },  /* AK7739_C0_329_MASTER_SPI_RX25                  */
  { 0x032A, 0x00 },  /* AK7739_C0_32A_MASTER_SPI_RX26                  */
  { 0x032B, 0x00 },  /* AK7739_C0_32B_MASTER_SPI_RX27                  */
  { 0x032C, 0x00 },  /* AK7739_C0_32C_MASTER_SPI_RX28                  */
  { 0x032D, 0x00 },  /* AK7739_C0_32D_MASTER_SPI_RX29                  */
  { 0x032E, 0x00 },  /* AK7739_C0_32E_MASTER_SPI_RX30                  */
  { 0x032F, 0x00 },  /* AK7739_C0_32F_MASTER_SPI_RX31                  */

  /* DSP block */
  { 0x1000, 0x03 },  /* AK7739_C1_000_DSP_RESET_CONTROL                */
  { 0x1001, 0x00 },  /* AK7739_C1_001_DSP_CLOCK_SETTING                */
  { 0x1002, 0x00 },  /* AK7739_C1_002_RAM_CLEAR_SETTING                */
  { 0x1003, 0x00 },  /* AK7739_C1_003_DSP_WATCHDOG_TIMER_FLAG_SETTING  */
  { 0x1004, 0x00 },  /* AK7739_C1_004_DSP_GPO_SETTING                  */
  { 0x1005, 0x00 },  /* AK7739_C1_005_DSP1,2,3_GPO_STATUS              */
  { 0x1008, 0x07 },  /* AK7739_C1_008_DSP_WATCHDOG_TIMER_ERROR_STATUS  */
  { 0x1011, 0x00 },  /* AK7739_C1_011_DSP1_DRAM_SETTING                */
  { 0x1012, 0x00 },  /* AK7739_C1_012_DSP2_DRAM_SETTING                */
  { 0x1013, 0x00 },  /* AK7739_C1_013_DSP3_DRAM_SETTING                */
  { 0x1020, 0x04 },  /* AK7739_C1_020_DSP1,2_DLYRAM_ASSIGNMENT         */
  { 0x1021, 0x00 },  /* AK7739_C1_021_DSP1_DLYRAM_SETTING              */
  { 0x1022, 0x00 },  /* AK7739_C1_022_DSP2_DLYRAM_SETTING              */
  { 0x1031, 0x10 },  /* AK7739_C1_031_DSP1_CRAM&DLP0_SETTING           */
  { 0x1032, 0x00 },  /* AK7739_C1_032_DSP2_CRAM&DLP0_SETTING           */
  { 0x1033, 0x00 },  /* AK7739_C1_033_DSP3_CRAM_SETTING                */
  { 0x1040, 0x00 },  /* AK7739_C1_040_DSP1_JX_SETTING                  */
  { 0x1041, 0x00 },  /* AK7739_C1_041_DSP2_JX_SETTING                  */
  { 0x1042, 0x00 },  /* AK7739_C1_042_DSP3_JX_SETTING                  */

  /* SRC block */
  { 0x2000, 0xC0 },  /* AK7739_C2_000_SRC_POWER_MANAGEMENT             */
  { 0x2001, 0x00 },  /* AK7739_C2_001_SRC_FILTER_SETTING1              */
  { 0x2003, 0x00 },  /* AK7739_C2_003_SRC_PHASE_GROUP1                 */
  { 0x2005, 0x00 },  /* AK7739_C2_005_SRC_MUTE_SETTING1                */
  { 0x2006, 0x00 },  /* AK7739_C2_006_SRC_MUTE_SETTING2                */
  { 0x2007, 0x00 },  /* AK7739_C2_007_SRC_STO_FLAG_SETTING             */
  { 0x2010, 0x00 },  /* AK7739_C2_010_SRC_STATUS1                      */
  { 0x2101, 0x30 },  /* AK7739_C2_101_MONO_SRC_POWER_MANAGEMENT        */
  { 0x2102, 0x00 },  /* AK7739_C2_102_MONO_SRC_FILTER_SETTING          */
  { 0x2103, 0x00 },  /* AK7739_C2_103_MONO_SRC_PHASE_GROUP             */
  { 0x2104, 0x00 },  /* AK7739_C2_104_MONO_SRC_MUTE_SETTING            */
  { 0x2105, 0x00 },  /* AK7739_C2_105_MONO_SRC_STO_FLAG_SETTING        */
  { 0x2106, 0x00 },  /* AK7739_C2_106_MONO_SRC_PATH_SETTING            */
  { 0x2110, 0x00 },  /* AK7739_C2_110_MONO_SRC_STATUS1                 */
  { 0x2200, 0x00 },  /* AK7739_C2_200_DIT_POWER_MANAGEMENT             */
  { 0x2201, 0x00 },  /* AK7739_C2_201_DIT_STATUS_BIT1                  */
  { 0x2202, 0x04 },  /* AK7739_C2_202_DIT_STATUS_BIT2                  */
  { 0x2203, 0x02 },  /* AK7739_C2_203_DIT_STATUS_BIT3                  */
  { 0x2204, 0x00 },  /* AK7739_C2_204_DIT_STATUS_BIT4                  */
  { 0x2210, 0x00 },  /* AK7739_C2_210_MIXER1_SETTING                   */
  { 0x2211, 0x00 },  /* AK7739_C2_211_MIXER2_SETTING                   */

  /* CODEC block */
  { 0x3000, 0x36 },  /* AK7739_C3_000_POWER_MANAGEMENT                 */
  { 0x3001, 0x20 },  /* AK7739_C3_001_MICBIAS_POWER_MANAGEMENT         */
  { 0x3002, 0x01 },  /* AK7739_C3_002_RESET_CONTROL                    */
  { 0x3003, 0x01 },  /* AK7739_C3_003_SYSTEM_CLOCK_SETTING             */
  { 0x3004, 0x11 },  /* AK7739_C3_004_MIC_AMP_GAIN                     */
  { 0x3005, 0x30 },  /* AK7739_C3_005_ADC1_LCH_DIGITAL_VOLUME          */
  { 0x3006, 0x30 },  /* AK7739_C3_006_ADC1_RCH_DIGITAL_VOLUME          */
  { 0x3007, 0x30 },  /* AK7739_C3_007_ADC2_LCH_DIGITAL_VOLUME          */
  { 0x3008, 0x30 },  /* AK7739_C3_008_ADC2_RCH_DIGITAL_VOLUME          */
  { 0x3009, 0x04 },  /* AK7739_C3_009_ADC_DIGITAL_FILTER_SETTING       */
  { 0x300A, 0x15 },  /* AK7739_C3_00A_ADC_ANALOG_INPUT_SETTING         */
  { 0x300B, 0x00 },  /* AK7739_C3_00B_ADC_MUTE&HPF_CONTROL             */
  { 0x300C, 0x18 },  /* AK7739_C3_00C_DAC1_LCH_DIGITAL_VOLUME          */
  { 0x300D, 0x18 },  /* AK7739_C3_00D_DAC1_RCH_DIGITAL_VOLUME          */
  { 0x300E, 0x18 },  /* AK7739_C3_00E_DAC2_LCH_DIGITAL_VOLUME          */
  { 0x300F, 0x18 },  /* AK7739_C3_00F_DAC2_RCH_DIGITAL_VOLUME          */
  { 0x3013, 0x15 },  /* AK7739_C3_013_DAC_De-EMPHASIS_SETTING          */
  { 0x3014, 0x02 },  /* AK7739_C3_014_DAC_MUTE&FILTER_SETTING          */
  { 0x3015, 0x00 },  /* AK7739_C3_015_DIGITAL_MIC_CONTROL              */

// VIRT Register for DSP Connection
  { 0x3016, 0x00 },  /* AK7739_VIRT_C3_016_DSP1OUT1_MIX              */
  { 0x3017, 0x00 },  /* AK7739_VIRT_C3_017_DSP1OUT2_MIX              */
  { 0x3018, 0x00 },  /* AK7739_VIRT_C3_018_DSP1OUT3_MIX              */
  { 0x3019, 0x00 },  /* AK7739_VIRT_C3_019_DSP1OUT4_MIX              */
  { 0x301A, 0x00 },  /* AK7739_VIRT_C3_01A_DSP1OUT5_MIX              */
  { 0x301B, 0x00 },  /* AK7739_VIRT_C3_01B_DSP1OUT6_MIX              */
  { 0x301C, 0x00 },  /* AK7739_VIRT_C3_01C_DSP2OUT1_MIX              */
  { 0x301D, 0x00 },  /* AK7739_VIRT_C3_01D_DSP2OUT2_MIX              */
  { 0x301E, 0x00 },  /* AK7739_VIRT_C3_01E_DSP2OUT3_MIX              */
  { 0x301F, 0x00 },  /* AK7739_VIRT_C3_01F_DSP2OUT4_MIX              */
  { 0x3020, 0x00 },  /* AK7739_VIRT_C3_020_DSP2OUT5_MIX              */
  { 0x3021, 0x00 },  /* AK7739_VIRT_C3_021_DSP2OUT6_MIX              */
};


/* ak7739 register cache & default register settings */
static const struct reg_default ak7739_mixer_reg[] = {
	/* AUDIOHUB,PERI block */
  { 0x0000, 0x03 },  /* AK7739_C0_000_SYSCLOCK_SETTING1                */
  { 0x0001, 0x01 },  /* AK7739_C0_001_SYSCLOCK_SETTING2                */
  { 0x0002, 0x03 },  /* AK7739_C0_002_SYSCLOCK_SETTING3                */
  { 0x0003, 0x5c },  /* AK7739_C0_003_SYSCLOCK_SETTING4                */
  { 0x0004, 0x00 },  /* AK7739_C0_004_SYSCLOCK_SETTING5                */
  { 0x0008, 0x01 },  /* AK7739_C0_008_AHCLK_SETTING5                   */
  { 0x0009, 0x01 },  /* AK7739_C0_009_BUSCLOCK_SETTING                 */
  { 0x000A, 0x02 },  /* AK7739_C0_00A_BUSCLOCK_SETTING2                */
  { 0x000B, 0x00 },  /* AK7739_C0_00B_CLKO_SETTING1                    */
  { 0x000C, 0x00 },  /* AK7739_C0_00C_CLKO_SETTING2                    */
  { 0x000D, 0x02 },  /* AK7739_C0_00D_MasterSPI_SCLK_SETTING1          */
  { 0x000E, 0x01 },  /* AK7739_C0_00E_MasterSPI_SCLK_SETTING2          */
  { 0x0010, 0x80 },  /* AK7739_C0_010_SYNCDOMAIN1_SETTING1             */
  { 0x0011, 0x01 },  /* AK7739_C0_011_SYNCDOMAIN1_SETTING2             */
  { 0x0012, 0x17 },  /* AK7739_C0_012_SYNCDOMAIN1_SETTING3             */
  { 0x0013, 0x00 },  /* AK7739_C0_013_SYNCDOMAIN1_SETTING4             */
  { 0x0014, 0x80 },  /* AK7739_C0_014_SYNCDOMAIN2_SETTING1             */
  { 0x0015, 0x01 },  /* AK7739_C0_015_SYNCDOMAIN2_SETTING2             */
  { 0x0016, 0x05 },  /* AK7739_C0_016_SYNCDOMAIN2_SETTING3             */
  { 0x0017, 0x04 },  /* AK7739_C0_017_SYNCDOMAIN2_SETTING4             */
  { 0x0018, 0x80 },  /* AK7739_C0_018_SYNCDOMAIN3_SETTING1             */
  { 0x0019, 0x01 },  /* AK7739_C0_019_SYNCDOMAIN3_SETTING2             */
  { 0x001A, 0x17 },  /* AK7739_C0_01A_SYNCDOMAIN3_SETTING3             */
  { 0x001B, 0x00 },  /* AK7739_C0_01B_SYNCDOMAIN3_SETTING4             */
  { 0x001C, 0x00 },  /* AK7739_C0_01C_SYNCDOMAIN4_SETTING1             */
  { 0x001D, 0x00 },  /* AK7739_C0_01D_SYNCDOMAIN4_SETTING2             */
  { 0x001E, 0x00 },  /* AK7739_C0_01E_SYNCDOMAIN4_SETTING3             */
  { 0x001F, 0x00 },  /* AK7739_C0_01F_SYNCDOMAIN4_SETTING4             */
  { 0x0020, 0x00 },  /* AK7739_C0_020_SYNCDOMAIN5_SETTING1             */
  { 0x0021, 0x00 },  /* AK7739_C0_021_SYNCDOMAIN5_SETTING2             */
  { 0x0022, 0x00 },  /* AK7739_C0_022_SYNCDOMAIN5_SETTING3             */
  { 0x0023, 0x00 },  /* AK7739_C0_023_SYNCDOMAIN5_SETTING4             */
  { 0x0025, 0x00 },  /* AK7739_C0_025_SYNCDOMAIN6_SETTING2             */
  { 0x0026, 0x00 },  /* AK7739_C0_026_SYNCDOMAIN6_SETTING3             */
  { 0x0027, 0x00 },  /* AK7739_C0_027_SYNCDOMAIN6_SETTING4             */
  { 0x0029, 0x01 },  /* AK7739_C0_029_SYNCDOMAIN7_SETTING2             */
  { 0x002A, 0x47 },  /* AK7739_C0_02A_SYNCDOMAIN7_SETTING3             */
  { 0x002B, 0x00 },  /* AK7739_C0_02B_SYNCDOMAIN7_SETTING4             */
  { 0x0040, 0x00 },  /* AK7739_C0_040_BICK_FORMAT_SETTING1             */
  { 0x0041, 0x01 },  /* AK7739_C0_041_BICK_SYNCDOMAIN_SELECT1          */
  { 0x0042, 0x00 },  /* AK7739_C0_042_BICK_FORMAT_SETTING2             */
  { 0x0043, 0x02 },  /* AK7739_C0_043_BICK_SYNCDOMAIN_SELECT2          */
  { 0x0044, 0x00 },  /* AK7739_C0_044_BICK_FORMAT_SETTING3             */
  { 0x0045, 0x00 },  /* AK7739_C0_045_BICK_SYNCDOMAIN_SELECT3          */
  { 0x0046, 0x00 },  /* AK7739_C0_046_BICK_FORMAT_SETTING4             */
  { 0x0047, 0x00 },  /* AK7739_C0_047_BICK_SYNCDOMAIN_SELECT4          */
  { 0x0048, 0x00 },  /* AK7739_C0_048_BICK_FORMAT_SETTING5             */
  { 0x0049, 0x00 },  /* AK7739_C0_049_BICK_SYNCDOMAIN_SELECT5          */
  { 0x0050, 0x00 },  /* AK7739_C0_050_SDIN1_INPUT_FORMAT               */
  { 0x0051, 0x01 },  /* AK7739_C0_051_SDIN1_SYNCDOMAIN_SELECT          */
  { 0x0052, 0x00 },  /* AK7739_C0_052_SDIN2_INPUT_FORMAT               */
  { 0x0053, 0x00 },  /* AK7739_C0_053_SDIN2_SYNCDOMAIN_SELECT          */
  { 0x0054, 0x00 },  /* AK7739_C0_054_SDIN3_INPUT_FORMAT               */
  { 0x0055, 0x00 },  /* AK7739_C0_055_SDIN3_SYNCDOMAIN_SELECT          */
  { 0x0056, 0x00 },  /* AK7739_C0_056_SDIN4_INPUT_FORMAT               */
  { 0x0057, 0x00 },  /* AK7739_C0_057_SDIN4_SYNCDOMAIN_SELECT          */
  { 0x0058, 0x00 },  /* AK7739_C0_058_SDIN5_INPUT_FORMAT               */
  { 0x0059, 0x00 },  /* AK7739_C0_059_SDIN5_SYNCDOMAIN_SELECT          */
  { 0x005A, 0x00 },  /* AK7739_C0_05A_SDIN6_INPUT_FORMAT               */
  { 0x005B, 0x00 },  /* AK7739_C0_05B_SDIN6_SYNCDOMAIN_SELECT          */
  { 0x0060, 0x00 },  /* AK7739_C0_060_SDOUT1_OUTPUT_FORMAT             */
  { 0x0061, 0x00 },  /* AK7739_C0_061_SDOUT1_SYNCDOMAIN_SELECT         */
  { 0x0062, 0xB0 },  /* AK7739_C0_062_SDOUT2_OUTPUT_FORMAT             */
  { 0x0063, 0x02 },  /* AK7739_C0_063_SDOUT2_SYNCDOMAIN_SELECT         */
  { 0x0064, 0x00 },  /* AK7739_C0_064_SDOUT3_OUTPUT_FORMAT             */
  { 0x0065, 0x00 },  /* AK7739_C0_065_SDOUT3_SYNCDOMAIN_SELECT         */
  { 0x0066, 0x00 },  /* AK7739_C0_066_SDOUT4_OUTPUT_FORMAT             */
  { 0x0067, 0x00 },  /* AK7739_C0_067_SDOUT4_SYNCDOMAIN_SELECT         */
  { 0x0068, 0x00 },  /* AK7739_C0_068_SDOUT5_OUTPUT_FORMAT             */
  { 0x0069, 0x00 },  /* AK7739_C0_069_SDOUT5_SYNCDOMAIN_SELECT         */
  { 0x006A, 0x00 },  /* AK7739_C0_06A_SDOUT6_OUTPUT_FORMAT             */
  { 0x006B, 0x07 },  /* AK7739_C0_06B_SDOUT6_SYNCDOMAIN_SELECT         */
  { 0x0087, 0x00 },  /* AK7739_C0_087_OSMEM_SYNCDOMAIN_SELECT          */
  { 0x0088, 0x07 },  /* AK7739_C0_088_ADC1_SYNCDOMAIN_SELECT           */
  { 0x0089, 0x07 },  /* AK7739_C0_089_CODEC_SYNCDOMAIN_SELECT          */
  { 0x0098, 0x07 },  /* AK7739_C0_098_MIXER_CH1_INPUT_SELECT           */
  { 0x0099, 0x00 },  /* AK7739_C0_099_MIXER_CH1_INPUT_SELECT           */
  { 0x00A0, 0x03 },  /* AK7739_C0_0A0_SRC1_SYNCDOMAIN_SELECT           */
  { 0x00A1, 0x02 },  /* AK7739_C0_0A1_SRC2_SYNCDOMAIN_SELECT           */
  { 0x00A2, 0x00 },  /* AK7739_C0_0A2_SRC3_SYNCDOMAIN_SELECT           */
  { 0x00A3, 0x00 },  /* AK7739_C0_0A3_SRC4_SYNCDOMAIN_SELECT           */
  { 0x00A4, 0x00 },  /* AK7739_C0_0A4_SRC5_SYNCDOMAIN_SELECT           */
  { 0x00A5, 0x00 },  /* AK7739_C0_0A5_SRC6_SYNCDOMAIN_SELECT           */
  { 0x00A6, 0x00 },  /* AK7739_C0_0A6_SRC7_SYNCDOMAIN_SELECT           */
  { 0x00A7, 0x00 },  /* AK7739_C0_0A7_SRC8_SYNCDOMAIN_SELECT           */
  { 0x00B0, 0x07 },  /* AK7739_C0_0B0_DSP1_SYNCDOMAIN_SELECT           */
  { 0x00B1, 0x01 },  /* AK7739_C0_0B1_DSP2_SYNCDOMAIN_SELECT           */
  { 0x00C0, 0x07 },  /* AK7739_C0_0C0_DSP1_OUTPUT_SYNCDOMAIN_SELECT1   */
  { 0x00C1, 0x07 },  /* AK7739_C0_0C1_DSP1_OUTPUT_SYNCDOMAIN_SELECT2   */
  { 0x00C2, 0x00 },  /* AK7739_C0_0C2_DSP1_OUTPUT_SYNCDOMAIN_SELECT3   */
  { 0x00C3, 0x00 },  /* AK7739_C0_0C3_DSP1_OUTPUT_SYNCDOMAIN_SELECT4   */
  { 0x00C4, 0x00 },  /* AK7739_C0_0C4_DSP1_OUTPUT_SYNCDOMAIN_SELECT5   */
  { 0x00C5, 0x00 },  /* AK7739_C0_0C5_DSP1_OUTPUT_SYNCDOMAIN_SELECT6   */
  { 0x00C6, 0x01 },  /* AK7739_C0_0C6_DSP2_OUTPUT_SYNCDOMAIN_SELECT1   */
  { 0x00C7, 0x00 },  /* AK7739_C0_0C7_DSP2_OUTPUT_SYNCDOMAIN_SELECT2   */
  { 0x00C8, 0x00 },  /* AK7739_C0_0C8_DSP2_OUTPUT_SYNCDOMAIN_SELECT3   */
  { 0x00C9, 0x00 },  /* AK7739_C0_0C9_DSP2_OUTPUT_SYNCDOMAIN_SELECT4   */
  { 0x00CA, 0x00 },  /* AK7739_C0_0CA_DSP2_OUTPUT_SYNCDOMAIN_SELECT5   */
  { 0x00CB, 0x00 },  /* AK7739_C0_0CB_DSP2_OUTPUT_SYNCDOMAIN_SELECT6   */
  { 0x0106, 0x00 },  /* AK7739_C0_106_DIT_INPUT_DATA_SELECT            */
  { 0x0108, 0x18 },  /* AK7739_C0_108_DAC1_INPUT_DATA_SELECT           */
  { 0x0109, 0x18 },  /* AK7739_C0_109_DAC2_INPUT_DATA_SELECT           */
  { 0x0118, 0x08 },  /* AK7739_C0_118_MIXER1_CHA_INPUT_DATA_SELECT     */
  { 0x0119, 0x09 },  /* AK7739_C0_119_MIXER1_CHB_INPUT_DATA_SELECT     */
  { 0x011A, 0x00 },  /* AK7739_C0_11A_MIXER2_CHA_INPUT_DATA_SELECT     */
  { 0x011B, 0x00 },  /* AK7739_C0_11B_MIXER2_CHB_INPUT_DATA_SELECT     */
  { 0x0120, 0x40 },  /* AK7739_C0_120_SRC1_INPUT_DATA_SELECT           */
  { 0x0121, 0x08 },  /* AK7739_C0_121_SRC2_INPUT_DATA_SELECT           */
  { 0x0122, 0x00 },  /* AK7739_C0_122_SRC3_INPUT_DATA_SELECT           */
  { 0x0123, 0x00 },  /* AK7739_C0_123_SRC4_INPUT_DATA_SELECT           */
  { 0x0124, 0x00 },  /* AK7739_C0_124_SRC5_INPUT_DATA_SELECT           */
  { 0x0125, 0x00 },  /* AK7739_C0_125_SRC6_INPUT_DATA_SELECT           */
  { 0x0126, 0x00 },  /* AK7739_C0_126_SRC7_INPUT_DATA_SELECT           */
  { 0x0127, 0x00 },  /* AK7739_C0_127_SRC8_INPUT_DATA_SELECT           */
  { 0x0140, 0x00 },  /* AK7739_C0_140_SDOUT1A_OUTPUT_DATA_SELECT       */
  { 0x0141, 0x00 },  /* AK7739_C0_141_SDOUT1B_OUTPUT_DATA_SELECT       */
  { 0x0142, 0x00 },  /* AK7739_C0_142_SDOUT1C_OUTPUT_DATA_SELECT       */
  { 0x0143, 0x00 },  /* AK7739_C0_143_SDOUT1D_OUTPUT_DATA_SELECT       */
  { 0x0144, 0x00 },  /* AK7739_C0_144_SDOUT1E_OUTPUT_DATA_SELECT       */
  { 0x0145, 0x00 },  /* AK7739_C0_145_SDOUT1F_OUTPUT_DATA_SELECT       */
  { 0x0146, 0x00 },  /* AK7739_C0_146_SDOUT1G_OUTPUT_DATA_SELECT       */
  { 0x0147, 0x00 },  /* AK7739_C0_147_SDOUT1H_OUTPUT_DATA_SELECT       */
  { 0x0148, 0x86 },  /* AK7739_C0_148_SDOUT2A_OUTPUT_DATA_SELECT       */
  { 0x0149, 0x21 },  /* AK7739_C0_149_SDOUT2B_OUTPUT_DATA_SELECT       */
  { 0x014A, 0x21 },  /* AK7739_C0_14A_SDOUT2C_OUTPUT_DATA_SELECT       */
  { 0x014B, 0x00 },  /* AK7739_C0_14B_SDOUT2D_OUTPUT_DATA_SELECT       */
  { 0x014C, 0x00 },  /* AK7739_C0_14C_SDOUT2E_OUTPUT_DATA_SELECT       */
  { 0x014D, 0x00 },  /* AK7739_C0_14D_SDOUT2F_OUTPUT_DATA_SELECT       */
  { 0x014E, 0x00 },  /* AK7739_C0_14E_SDOUT2G_OUTPUT_DATA_SELECT       */
  { 0x014F, 0x00 },  /* AK7739_C0_14F_SDOUT2H_OUTPUT_DATA_SELECT       */
  { 0x0150, 0x00 },  /* AK7739_C0_150_SDOUT3A_OUTPUT_DATA_SELECT       */
  { 0x0151, 0x00 },  /* AK7739_C0_151_SDOUT3B_OUTPUT_DATA_SELECT       */
  { 0x0152, 0x00 },  /* AK7739_C0_152_SDOUT3C_OUTPUT_DATA_SELECT       */
  { 0x0153, 0x00 },  /* AK7739_C0_153_SDOUT3D_OUTPUT_DATA_SELECT       */
  { 0x0154, 0x00 },  /* AK7739_C0_154_SDOUT3E_OUTPUT_DATA_SELECT       */
  { 0x0155, 0x00 },  /* AK7739_C0_155_SDOUT3F_OUTPUT_DATA_SELECT       */
  { 0x0156, 0x00 },  /* AK7739_C0_156_SDOUT3G_OUTPUT_DATA_SELECT       */
  { 0x0157, 0x00 },  /* AK7739_C0_157_SDOUT3H_OUTPUT_DATA_SELECT       */
  { 0x0158, 0x00 },  /* AK7739_C0_158_SDOUT4A_OUTPUT_DATA_SELECT       */
  { 0x0159, 0x00 },  /* AK7739_C0_159_SDOUT4B_OUTPUT_DATA_SELECT       */
  { 0x015A, 0x00 },  /* AK7739_C0_15A_SDOUT4C_OUTPUT_DATA_SELECT       */
  { 0x015B, 0x00 },  /* AK7739_C0_15B_SDOUT4D_OUTPUT_DATA_SELECT       */
  { 0x015C, 0x00 },  /* AK7739_C0_15C_SDOUT4E_OUTPUT_DATA_SELECT       */
  { 0x015D, 0x00 },  /* AK7739_C0_15D_SDOUT4F_OUTPUT_DATA_SELECT       */
  { 0x015E, 0x00 },  /* AK7739_C0_15E_SDOUT4G_OUTPUT_DATA_SELECT       */
  { 0x015F, 0x00 },  /* AK7739_C0_15F_SDOUT4H_OUTPUT_DATA_SELECT       */
  { 0x0160, 0x00 },  /* AK7739_C0_160_SDOUT5A_OUTPUT_DATA_SELECT       */
  { 0x0161, 0x00 },  /* AK7739_C0_161_SDOUT5B_OUTPUT_DATA_SELECT       */
  { 0x0162, 0x00 },  /* AK7739_C0_162_SDOUT5C_OUTPUT_DATA_SELECT       */
  { 0x0163, 0x00 },  /* AK7739_C0_163_SDOUT5D_OUTPUT_DATA_SELECT       */
  { 0x0164, 0x00 },  /* AK7739_C0_164_SDOUT5E_OUTPUT_DATA_SELECT       */
  { 0x0165, 0x00 },  /* AK7739_C0_165_SDOUT5F_OUTPUT_DATA_SELECT       */
  { 0x0166, 0x00 },  /* AK7739_C0_166_SDOUT5G_OUTPUT_DATA_SELECT       */
  { 0x0167, 0x00 },  /* AK7739_C0_167_SDOUT5H_OUTPUT_DATA_SELECT       */
  { 0x0168, 0x00 },  /* AK7739_C0_168_SDOUT6A_OUTPUT_DATA_SELECT       */
  { 0x0169, 0x00 },  /* AK7739_C0_169_SDOUT6B_OUTPUT_DATA_SELECT       */
  { 0x016A, 0x00 },  /* AK7739_C0_16A_SDOUT6C_OUTPUT_DATA_SELECT       */
  { 0x016B, 0x00 },  /* AK7739_C0_16B_SDOUT6D_OUTPUT_DATA_SELECT       */
  { 0x016C, 0x00 },  /* AK7739_C0_16C_SDOUT6E_OUTPUT_DATA_SELECT       */
  { 0x016D, 0x00 },  /* AK7739_C0_16D_SDOUT6F_OUTPUT_DATA_SELECT       */
  { 0x016E, 0x00 },  /* AK7739_C0_16E_SDOUT6G_OUTPUT_DATA_SELECT       */
  { 0x016F, 0x00 },  /* AK7739_C0_16F_SDOUT6H_OUTPUT_DATA_SELECT       */
  { 0x0180, 0x08 },  /* AK7739_C0_180_DSP1_DIN1_INPUT_DATA_SELECT      */
  { 0x0181, 0x09 },  /* AK7739_C0_181_DSP1_DIN2_INPUT_DATA_SELECT      */
  { 0x0182, 0x00 },  /* AK7739_C0_182_DSP1_DIN3_INPUT_DATA_SELECT      */
  { 0x0183, 0x00 },  /* AK7739_C0_183_DSP1_DIN4_INPUT_DATA_SELECT      */
  { 0x0184, 0x00 },  /* AK7739_C0_184_DSP1_DIN5_INPUT_DATA_SELECT      */
  { 0x0185, 0x00 },  /* AK7739_C0_185_DSP1_DIN6_INPUT_DATA_SELECT      */
  { 0x0186, 0x08 },  /* AK7739_C0_186_DSP2_DIN1_INPUT_DATA_SELECT      */
  { 0x0187, 0x00 },  /* AK7739_C0_187_DSP2_DIN2_INPUT_DATA_SELECT      */
  { 0x0188, 0x00 },  /* AK7739_C0_188_DSP2_DIN3_INPUT_DATA_SELECT      */
  { 0x0189, 0x00 },  /* AK7739_C0_189_DSP2_DIN4_INPUT_DATA_SELECT      */
  { 0x018A, 0x00 },  /* AK7739_C0_18A_DSP2_DIN5_INPUT_DATA_SELECT      */
  { 0x018B, 0x00 },  /* AK7739_C0_18B_DSP2_DIN6_INPUT_DATA_SELECT      */
  { 0x0200, 0x00 },  /* AK7739_C0_200_SDOUT_OUTPUT_DATA_SELECT         */
  { 0x0201, 0xFC },  /* AK7739_C0_201_SDOUT_ENABLE_SETTING             */
  { 0x0202, 0x00 },  /* AK7739_C0_202_SDOUT_OUTPUT_MODE_SETTING        */
  { 0x0203, 0x00 },  /* AK7739_C0_203_SDIN_INPUT_DATA_SELECT           */
  { 0x0204, 0x00 },  /* AK7739_C0_204_MASTER_SPI_SELECT                */
  { 0x0205, 0x00 },  /* AK7739_C0_205_STO_FLAG_SETTING                 */
  { 0x0206, 0x00 },  /* AK7739_C0_206_LRCK4_5_OUTPUT_DATA_SELECT       */
  { 0x0210, 0x00 },  /* AK7739_C0_210_MASTER_SPI_TX00                  */
  { 0x0211, 0x00 },  /* AK7739_C0_211_MASTER_SPI_TX01                  */
  { 0x0212, 0x00 },  /* AK7739_C0_212_MASTER_SPI_TX02                  */
  { 0x0213, 0x00 },  /* AK7739_C0_213_MASTER_SPI_TX03                  */
  { 0x0214, 0x00 },  /* AK7739_C0_214_MASTER_SPI_TX04                  */
  { 0x0215, 0x00 },  /* AK7739_C0_215_MASTER_SPI_TX05                  */
  { 0x0216, 0x00 },  /* AK7739_C0_216_MASTER_SPI_TX06                  */
  { 0x0217, 0x00 },  /* AK7739_C0_217_MASTER_SPI_TX07                  */
  { 0x0218, 0x00 },  /* AK7739_C0_218_MASTER_SPI_TX08                  */
  { 0x0219, 0x00 },  /* AK7739_C0_219_MASTER_SPI_TX09                  */
  { 0x021A, 0x00 },  /* AK7739_C0_21A_MASTER_SPI_TX10                  */
  { 0x021B, 0x00 },  /* AK7739_C0_21B_MASTER_SPI_TX11                  */
  { 0x021C, 0x00 },  /* AK7739_C0_21C_MASTER_SPI_TX12                  */
  { 0x021D, 0x00 },  /* AK7739_C0_21D_MASTER_SPI_TX13                  */
  { 0x021E, 0x00 },  /* AK7739_C0_21E_MASTER_SPI_TX14                  */
  { 0x021F, 0x00 },  /* AK7739_C0_21F_MASTER_SPI_TX15                  */
  { 0x0220, 0x00 },  /* AK7739_C0_220_MASTER_SPI_TX16                  */
  { 0x0221, 0x00 },  /* AK7739_C0_221_MASTER_SPI_TX17                  */
  { 0x0222, 0x00 },  /* AK7739_C0_222_MASTER_SPI_TX18                  */
  { 0x0223, 0x00 },  /* AK7739_C0_223_MASTER_SPI_TX19                  */
  { 0x0224, 0x00 },  /* AK7739_C0_224_MASTER_SPI_TX20                  */
  { 0x0225, 0x00 },  /* AK7739_C0_225_MASTER_SPI_TX21                  */
  { 0x0226, 0x00 },  /* AK7739_C0_226_MASTER_SPI_TX22                  */
  { 0x0227, 0x00 },  /* AK7739_C0_227_MASTER_SPI_TX23                  */
  { 0x0228, 0x00 },  /* AK7739_C0_228_MASTER_SPI_TX24                  */
  { 0x0229, 0x00 },  /* AK7739_C0_229_MASTER_SPI_TX25                  */
  { 0x022A, 0x00 },  /* AK7739_C0_22A_MASTER_SPI_TX26                  */
  { 0x022B, 0x00 },  /* AK7739_C0_22B_MASTER_SPI_TX27                  */
  { 0x022C, 0x00 },  /* AK7739_C0_22C_MASTER_SPI_TX28                  */
  { 0x022D, 0x00 },  /* AK7739_C0_22D_MASTER_SPI_TX29                  */
  { 0x022E, 0x00 },  /* AK7739_C0_22E_MASTER_SPI_TX30                  */
  { 0x022F, 0x00 },  /* AK7739_C0_22F_MASTER_SPI_TX31                  */
  { 0x0300, 0x39 },	 /* AK7739_C0_300_DEVICE_ID                        */
  { 0x0301, 0x00 },	 /* AK7739_C0_301_REVISION_NUM                     */
  { 0x0302, 0x00 },	 /* AK7739_C0_302_CRC_ERROR_STATUS                 */
  { 0x0303, 0x00 },	 /* AK7739_C0_303_STO_READ_OUT                     */
  { 0x030F, 0x30 },	 /* AK7739_C0_30F_MASTER_SPI_STATUS                */
  { 0x0310, 0x00 },	 /* AK7739_C0_310_MASTER_SPI_RX00                  */
  { 0x0311, 0x00 },	 /* AK7739_C0_311_MASTER_SPI_RX01                  */
  { 0x0312, 0x00 },	 /* AK7739_C0_312_MASTER_SPI_RX02                  */
  { 0x0313, 0x00 },  /* AK7739_C0_313_MASTER_SPI_RX03                  */
  { 0x0314, 0x00 },  /* AK7739_C0_314_MASTER_SPI_RX04                  */
  { 0x0315, 0x00 },  /* AK7739_C0_315_MASTER_SPI_RX05                  */
  { 0x0316, 0x00 },  /* AK7739_C0_316_MASTER_SPI_RX06                  */
  { 0x0317, 0x00 },  /* AK7739_C0_317_MASTER_SPI_RX07                  */
  { 0x0318, 0x00 },  /* AK7739_C0_318_MASTER_SPI_RX08                  */
  { 0x0319, 0x00 },  /* AK7739_C0_319_MASTER_SPI_RX09                  */
  { 0x031A, 0x00 },  /* AK7739_C0_31A_MASTER_SPI_RX10                  */
  { 0x031B, 0x00 },  /* AK7739_C0_31B_MASTER_SPI_RX11                  */
  { 0x031C, 0x00 },  /* AK7739_C0_31C_MASTER_SPI_RX12                  */
  { 0x031D, 0x00 },  /* AK7739_C0_31D_MASTER_SPI_RX13                  */
  { 0x031E, 0x00 },  /* AK7739_C0_31E_MASTER_SPI_RX14                  */
  { 0x031F, 0x00 },  /* AK7739_C0_31F_MASTER_SPI_RX15                  */
  { 0x0320, 0x00 },	 /* AK7739_C0_320_MASTER_SPI_RX16                  */
  { 0x0321, 0x00 },  /* AK7739_C0_321_MASTER_SPI_RX17                  */
  { 0x0322, 0x00 },	 /* AK7739_C0_322_MASTER_SPI_RX18                  */
  { 0x0323, 0x00 },  /* AK7739_C0_323_MASTER_SPI_RX19                  */
  { 0x0324, 0x00 },  /* AK7739_C0_324_MASTER_SPI_RX20                  */
  { 0x0325, 0x00 },  /* AK7739_C0_325_MASTER_SPI_RX21                  */
  { 0x0326, 0x00 },  /* AK7739_C0_326_MASTER_SPI_RX22                  */
  { 0x0327, 0x00 },  /* AK7739_C0_327_MASTER_SPI_RX23                  */
  { 0x0328, 0x00 },  /* AK7739_C0_328_MASTER_SPI_RX24                  */
  { 0x0329, 0x00 },  /* AK7739_C0_329_MASTER_SPI_RX25                  */
  { 0x032A, 0x00 },  /* AK7739_C0_32A_MASTER_SPI_RX26                  */
  { 0x032B, 0x00 },  /* AK7739_C0_32B_MASTER_SPI_RX27                  */
  { 0x032C, 0x00 },  /* AK7739_C0_32C_MASTER_SPI_RX28                  */
  { 0x032D, 0x00 },  /* AK7739_C0_32D_MASTER_SPI_RX29                  */
  { 0x032E, 0x00 },  /* AK7739_C0_32E_MASTER_SPI_RX30                  */
  { 0x032F, 0x00 },  /* AK7739_C0_32F_MASTER_SPI_RX31                  */

  /* DSP block */
  { 0x1000, 0x03 },  /* AK7739_C1_000_DSP_RESET_CONTROL                */
  { 0x1001, 0x00 },  /* AK7739_C1_001_DSP_CLOCK_SETTING                */
  { 0x1002, 0x00 },  /* AK7739_C1_002_RAM_CLEAR_SETTING                */
  { 0x1003, 0x00 },  /* AK7739_C1_003_DSP_WATCHDOG_TIMER_FLAG_SETTING  */
  { 0x1004, 0x00 },  /* AK7739_C1_004_DSP_GPO_SETTING                  */
  { 0x1005, 0x00 },  /* AK7739_C1_005_DSP1,2,3_GPO_STATUS              */
  { 0x1008, 0x06 },  /* AK7739_C1_008_DSP_WATCHDOG_TIMER_ERROR_STATUS  */
  { 0x1011, 0x00 },  /* AK7739_C1_011_DSP1_DRAM_SETTING                */
  { 0x1012, 0x00 },  /* AK7739_C1_012_DSP2_DRAM_SETTING                */
  { 0x1013, 0x00 },  /* AK7739_C1_013_DSP3_DRAM_SETTING                */
  { 0x1020, 0x04 },  /* AK7739_C1_020_DSP1,2_DLYRAM_ASSIGNMENT         */
  { 0x1021, 0x00 },  /* AK7739_C1_021_DSP1_DLYRAM_SETTING              */
  { 0x1022, 0x00 },  /* AK7739_C1_022_DSP2_DLYRAM_SETTING              */
  { 0x1031, 0x10 },  /* AK7739_C1_031_DSP1_CRAM&DLP0_SETTING           */
  { 0x1032, 0x00 },  /* AK7739_C1_032_DSP2_CRAM&DLP0_SETTING           */
  { 0x1033, 0x00 },  /* AK7739_C1_033_DSP3_CRAM_SETTING                */
  { 0x1040, 0x00 },  /* AK7739_C1_040_DSP1_JX_SETTING                  */
  { 0x1041, 0x00 },  /* AK7739_C1_041_DSP2_JX_SETTING                  */
  { 0x1042, 0x00 },  /* AK7739_C1_042_DSP3_JX_SETTING                  */

  /* SRC block */
  { 0x2000, 0xC0 },  /* AK7739_C2_000_SRC_POWER_MANAGEMENT             */
  { 0x2001, 0x00 },  /* AK7739_C2_001_SRC_FILTER_SETTING1              */
  { 0x2003, 0x00 },  /* AK7739_C2_003_SRC_PHASE_GROUP1                 */
  { 0x2005, 0x00 },  /* AK7739_C2_005_SRC_MUTE_SETTING1                */
  { 0x2006, 0x00 },  /* AK7739_C2_006_SRC_MUTE_SETTING2                */
  { 0x2007, 0x00 },  /* AK7739_C2_007_SRC_STO_FLAG_SETTING             */
  { 0x2010, 0x00 },  /* AK7739_C2_010_SRC_STATUS1                      */
  { 0x2101, 0x00 },  /* AK7739_C2_101_MONO_SRC_POWER_MANAGEMENT        */
  { 0x2102, 0x00 },  /* AK7739_C2_102_MONO_SRC_FILTER_SETTING          */
  { 0x2103, 0x00 },  /* AK7739_C2_103_MONO_SRC_PHASE_GROUP             */
  { 0x2104, 0x00 },  /* AK7739_C2_104_MONO_SRC_MUTE_SETTING            */
  { 0x2105, 0x00 },  /* AK7739_C2_105_MONO_SRC_STO_FLAG_SETTING        */
  { 0x2106, 0x00 },  /* AK7739_C2_106_MONO_SRC_PATH_SETTING            */
  { 0x2110, 0x00 },  /* AK7739_C2_110_MONO_SRC_STATUS1                 */
  { 0x2200, 0x00 },  /* AK7739_C2_200_DIT_POWER_MANAGEMENT             */
  { 0x2201, 0x00 },  /* AK7739_C2_201_DIT_STATUS_BIT1                  */
  { 0x2202, 0x04 },  /* AK7739_C2_202_DIT_STATUS_BIT2                  */
  { 0x2203, 0x02 },  /* AK7739_C2_203_DIT_STATUS_BIT3                  */
  { 0x2204, 0x00 },  /* AK7739_C2_204_DIT_STATUS_BIT4                  */
  { 0x2210, 0x00 },  /* AK7739_C2_210_MIXER1_SETTING                   */
  { 0x2211, 0x00 },  /* AK7739_C2_211_MIXER2_SETTING                   */

  /* CODEC block */
  { 0x3000, 0x36 },  /* AK7739_C3_000_POWER_MANAGEMENT                 */
  { 0x3001, 0x20 },  /* AK7739_C3_001_MICBIAS_POWER_MANAGEMENT         */
  { 0x3002, 0x01 },  /* AK7739_C3_002_RESET_CONTROL                    */
  { 0x3003, 0x01 },  /* AK7739_C3_003_SYSTEM_CLOCK_SETTING             */
  { 0x3004, 0x11 },  /* AK7739_C3_004_MIC_AMP_GAIN                     */
  { 0x3005, 0x30 },  /* AK7739_C3_005_ADC1_LCH_DIGITAL_VOLUME          */
  { 0x3006, 0x30 },  /* AK7739_C3_006_ADC1_RCH_DIGITAL_VOLUME          */
  { 0x3007, 0x30 },  /* AK7739_C3_007_ADC2_LCH_DIGITAL_VOLUME          */
  { 0x3008, 0x30 },  /* AK7739_C3_008_ADC2_RCH_DIGITAL_VOLUME          */
  { 0x3009, 0x04 },  /* AK7739_C3_009_ADC_DIGITAL_FILTER_SETTING       */
  { 0x300A, 0x15 },  /* AK7739_C3_00A_ADC_ANALOG_INPUT_SETTING         */
  { 0x300B, 0x00 },  /* AK7739_C3_00B_ADC_MUTE&HPF_CONTROL             */
  { 0x300C, 0x18 },  /* AK7739_C3_00C_DAC1_LCH_DIGITAL_VOLUME          */
  { 0x300D, 0x18 },  /* AK7739_C3_00D_DAC1_RCH_DIGITAL_VOLUME          */
  { 0x300E, 0x18 },  /* AK7739_C3_00E_DAC2_LCH_DIGITAL_VOLUME          */
  { 0x300F, 0x18 },  /* AK7739_C3_00F_DAC2_RCH_DIGITAL_VOLUME          */
  { 0x3013, 0x15 },  /* AK7739_C3_013_DAC_De-EMPHASIS_SETTING          */
  { 0x3014, 0x02 },  /* AK7739_C3_014_DAC_MUTE&FILTER_SETTING          */
  { 0x3015, 0x00 },  /* AK7739_C3_015_DIGITAL_MIC_CONTROL              */

// VIRT Register for DSP Connection
  { 0x3016, 0x00 },  /* AK7739_VIRT_C3_016_DSP1OUT1_MIX              */
  { 0x3017, 0x00 },  /* AK7739_VIRT_C3_017_DSP1OUT2_MIX              */
  { 0x3018, 0x00 },  /* AK7739_VIRT_C3_018_DSP1OUT3_MIX              */
  { 0x3019, 0x00 },  /* AK7739_VIRT_C3_019_DSP1OUT4_MIX              */
  { 0x301A, 0x00 },  /* AK7739_VIRT_C3_01A_DSP1OUT5_MIX              */
  { 0x301B, 0x00 },  /* AK7739_VIRT_C3_01B_DSP1OUT6_MIX              */
  { 0x301C, 0x00 },  /* AK7739_VIRT_C3_01C_DSP2OUT1_MIX              */
  { 0x301D, 0x00 },  /* AK7739_VIRT_C3_01D_DSP2OUT2_MIX              */
  { 0x301E, 0x00 },  /* AK7739_VIRT_C3_01E_DSP2OUT3_MIX              */
  { 0x301F, 0x00 },  /* AK7739_VIRT_C3_01F_DSP2OUT4_MIX              */
  { 0x3020, 0x00 },  /* AK7739_VIRT_C3_020_DSP2OUT5_MIX              */
  { 0x3021, 0x00 },  /* AK7739_VIRT_C3_021_DSP2OUT6_MIX              */
};

/* ak7739 register cache & default register settings */
static const struct reg_default ak7739_i2s_reg[] = {
	/* AUDIOHUB,PERI block */
  { 0x0000, 0x03 },  /* AK7739_C0_000_SYSCLOCK_SETTING1                */
  { 0x0001, 0x01 },  /* AK7739_C0_001_SYSCLOCK_SETTING2                */
  { 0x0002, 0x03 },  /* AK7739_C0_002_SYSCLOCK_SETTING3                */
  { 0x0003, 0x5c },  /* AK7739_C0_003_SYSCLOCK_SETTING4                */
  { 0x0004, 0x00 },  /* AK7739_C0_004_SYSCLOCK_SETTING5                */
  { 0x0008, 0x01 },  /* AK7739_C0_008_AHCLK_SETTING5                   */
  { 0x0009, 0x01 },  /* AK7739_C0_009_BUSCLOCK_SETTING                 */
  { 0x000A, 0x02 },  /* AK7739_C0_00A_BUSCLOCK_SETTING2                */
  { 0x000B, 0x00 },  /* AK7739_C0_00B_CLKO_SETTING1                    */
  { 0x000C, 0x00 },  /* AK7739_C0_00C_CLKO_SETTING2                    */
  { 0x000D, 0x02 },  /* AK7739_C0_00D_MasterSPI_SCLK_SETTING1          */
  { 0x000E, 0x01 },  /* AK7739_C0_00E_MasterSPI_SCLK_SETTING2          */
  { 0x0010, 0x00 },  /* AK7739_C0_010_SYNCDOMAIN1_SETTING1             */
  { 0x0011, 0x01 },  /* AK7739_C0_011_SYNCDOMAIN1_SETTING2             */
  { 0x0012, 0x17 },  /* AK7739_C0_012_SYNCDOMAIN1_SETTING3             */
  { 0x0013, 0x00 },  /* AK7739_C0_013_SYNCDOMAIN1_SETTING4             */
  { 0x0014, 0x80 },  /* AK7739_C0_014_SYNCDOMAIN2_SETTING1             */
  { 0x0015, 0x01 },  /* AK7739_C0_015_SYNCDOMAIN2_SETTING2             */
  { 0x0016, 0x05 },  /* AK7739_C0_016_SYNCDOMAIN2_SETTING3             */
  { 0x0017, 0x04 },  /* AK7739_C0_017_SYNCDOMAIN2_SETTING4             */
  { 0x0018, 0x80 },  /* AK7739_C0_018_SYNCDOMAIN3_SETTING1             */
  { 0x0019, 0x01 },  /* AK7739_C0_019_SYNCDOMAIN3_SETTING2             */
  { 0x001A, 0x17 },  /* AK7739_C0_01A_SYNCDOMAIN3_SETTING3             */
  { 0x001B, 0x00 },  /* AK7739_C0_01B_SYNCDOMAIN3_SETTING4             */
  { 0x001C, 0x00 },  /* AK7739_C0_01C_SYNCDOMAIN4_SETTING1             */
  { 0x001D, 0x00 },  /* AK7739_C0_01D_SYNCDOMAIN4_SETTING2             */
  { 0x001E, 0x00 },  /* AK7739_C0_01E_SYNCDOMAIN4_SETTING3             */
  { 0x001F, 0x00 },  /* AK7739_C0_01F_SYNCDOMAIN4_SETTING4             */
  { 0x0020, 0x00 },  /* AK7739_C0_020_SYNCDOMAIN5_SETTING1             */
  { 0x0021, 0x00 },  /* AK7739_C0_021_SYNCDOMAIN5_SETTING2             */
  { 0x0022, 0x00 },  /* AK7739_C0_022_SYNCDOMAIN5_SETTING3             */
  { 0x0023, 0x00 },  /* AK7739_C0_023_SYNCDOMAIN5_SETTING4             */
  { 0x0025, 0x00 },  /* AK7739_C0_025_SYNCDOMAIN6_SETTING2             */
  { 0x0026, 0x00 },  /* AK7739_C0_026_SYNCDOMAIN6_SETTING3             */
  { 0x0027, 0x00 },  /* AK7739_C0_027_SYNCDOMAIN6_SETTING4             */
  { 0x0029, 0x01 },  /* AK7739_C0_029_SYNCDOMAIN7_SETTING2             */
  { 0x002A, 0x47 },  /* AK7739_C0_02A_SYNCDOMAIN7_SETTING3             */
  { 0x002B, 0x00 },  /* AK7739_C0_02B_SYNCDOMAIN7_SETTING4             */
  { 0x0040, 0x00 },  /* AK7739_C0_040_BICK_FORMAT_SETTING1             */
  { 0x0041, 0x01 },  /* AK7739_C0_041_BICK_SYNCDOMAIN_SELECT1          */
  { 0x0042, 0x00 },  /* AK7739_C0_042_BICK_FORMAT_SETTING2             */
  { 0x0043, 0x02 },  /* AK7739_C0_043_BICK_SYNCDOMAIN_SELECT2          */
  { 0x0044, 0x00 },  /* AK7739_C0_044_BICK_FORMAT_SETTING3             */
  { 0x0045, 0x00 },  /* AK7739_C0_045_BICK_SYNCDOMAIN_SELECT3          */
  { 0x0046, 0x00 },  /* AK7739_C0_046_BICK_FORMAT_SETTING4             */
  { 0x0047, 0x00 },  /* AK7739_C0_047_BICK_SYNCDOMAIN_SELECT4          */
  { 0x0048, 0x00 },  /* AK7739_C0_048_BICK_FORMAT_SETTING5             */
  { 0x0049, 0x00 },  /* AK7739_C0_049_BICK_SYNCDOMAIN_SELECT5          */
  { 0x0050, 0x32 },  /* AK7739_C0_050_SDIN1_INPUT_FORMAT               */
  { 0x0051, 0x01 },  /* AK7739_C0_051_SDIN1_SYNCDOMAIN_SELECT          */
  { 0x0052, 0x00 },  /* AK7739_C0_052_SDIN2_INPUT_FORMAT               */
  { 0x0053, 0x00 },  /* AK7739_C0_053_SDIN2_SYNCDOMAIN_SELECT          */
  { 0x0054, 0x00 },  /* AK7739_C0_054_SDIN3_INPUT_FORMAT               */
  { 0x0055, 0x00 },  /* AK7739_C0_055_SDIN3_SYNCDOMAIN_SELECT          */
  { 0x0056, 0x00 },  /* AK7739_C0_056_SDIN4_INPUT_FORMAT               */
  { 0x0057, 0x00 },  /* AK7739_C0_057_SDIN4_SYNCDOMAIN_SELECT          */
  { 0x0058, 0x00 },  /* AK7739_C0_058_SDIN5_INPUT_FORMAT               */
  { 0x0059, 0x00 },  /* AK7739_C0_059_SDIN5_SYNCDOMAIN_SELECT          */
  { 0x005A, 0x00 },  /* AK7739_C0_05A_SDIN6_INPUT_FORMAT               */
  { 0x005B, 0x00 },  /* AK7739_C0_05B_SDIN6_SYNCDOMAIN_SELECT          */
  { 0x0060, 0x32 },  /* AK7739_C0_060_SDOUT1_OUTPUT_FORMAT             */
  { 0x0061, 0x00 },  /* AK7739_C0_061_SDOUT1_SYNCDOMAIN_SELECT         */
  { 0x0062, 0xB0 },  /* AK7739_C0_062_SDOUT2_OUTPUT_FORMAT             */
  { 0x0063, 0x00 },  /* AK7739_C0_063_SDOUT2_SYNCDOMAIN_SELECT         */
  { 0x0064, 0x00 },  /* AK7739_C0_064_SDOUT3_OUTPUT_FORMAT             */
  { 0x0065, 0x00 },  /* AK7739_C0_065_SDOUT3_SYNCDOMAIN_SELECT         */
  { 0x0066, 0x00 },  /* AK7739_C0_066_SDOUT4_OUTPUT_FORMAT             */
  { 0x0067, 0x00 },  /* AK7739_C0_067_SDOUT4_SYNCDOMAIN_SELECT         */
  { 0x0068, 0x00 },  /* AK7739_C0_068_SDOUT5_OUTPUT_FORMAT             */
  { 0x0069, 0x00 },  /* AK7739_C0_069_SDOUT5_SYNCDOMAIN_SELECT         */
  { 0x006A, 0x00 },  /* AK7739_C0_06A_SDOUT6_OUTPUT_FORMAT             */
  { 0x006B, 0x07 },  /* AK7739_C0_06B_SDOUT6_SYNCDOMAIN_SELECT         */
  { 0x0087, 0x00 },  /* AK7739_C0_087_OSMEM_SYNCDOMAIN_SELECT          */
  { 0x0088, 0x01 },  /* AK7739_C0_088_ADC1_SYNCDOMAIN_SELECT           */
  { 0x0089, 0x01 },  /* AK7739_C0_089_CODEC_SYNCDOMAIN_SELECT          */
  { 0x0098, 0x00 },  /* AK7739_C0_098_MIXER_CH1_INPUT_SELECT           */
  { 0x0099, 0x00 },  /* AK7739_C0_099_MIXER_CH1_INPUT_SELECT           */
  { 0x00A0, 0x03 },  /* AK7739_C0_0A0_SRC1_SYNCDOMAIN_SELECT           */
  { 0x00A1, 0x02 },  /* AK7739_C0_0A1_SRC2_SYNCDOMAIN_SELECT           */
  { 0x00A2, 0x00 },  /* AK7739_C0_0A2_SRC3_SYNCDOMAIN_SELECT           */
  { 0x00A3, 0x00 },  /* AK7739_C0_0A3_SRC4_SYNCDOMAIN_SELECT           */
  { 0x00A4, 0x00 },  /* AK7739_C0_0A4_SRC5_SYNCDOMAIN_SELECT           */
  { 0x00A5, 0x00 },  /* AK7739_C0_0A5_SRC6_SYNCDOMAIN_SELECT           */
  { 0x00A6, 0x00 },  /* AK7739_C0_0A6_SRC7_SYNCDOMAIN_SELECT           */
  { 0x00A7, 0x00 },  /* AK7739_C0_0A7_SRC8_SYNCDOMAIN_SELECT           */
  { 0x00B0, 0x01 },  /* AK7739_C0_0B0_DSP1_SYNCDOMAIN_SELECT           */
  { 0x00B1, 0x00 },  /* AK7739_C0_0B1_DSP2_SYNCDOMAIN_SELECT           */
  { 0x00C0, 0x01 },  /* AK7739_C0_0C0_DSP1_OUTPUT_SYNCDOMAIN_SELECT1   */
  { 0x00C1, 0x01 },  /* AK7739_C0_0C1_DSP1_OUTPUT_SYNCDOMAIN_SELECT2   */
  { 0x00C2, 0x00 },  /* AK7739_C0_0C2_DSP1_OUTPUT_SYNCDOMAIN_SELECT3   */
  { 0x00C3, 0x00 },  /* AK7739_C0_0C3_DSP1_OUTPUT_SYNCDOMAIN_SELECT4   */
  { 0x00C4, 0x00 },  /* AK7739_C0_0C4_DSP1_OUTPUT_SYNCDOMAIN_SELECT5   */
  { 0x00C5, 0x00 },  /* AK7739_C0_0C5_DSP1_OUTPUT_SYNCDOMAIN_SELECT6   */
  { 0x00C6, 0x00 },  /* AK7739_C0_0C6_DSP2_OUTPUT_SYNCDOMAIN_SELECT1   */
  { 0x00C7, 0x00 },  /* AK7739_C0_0C7_DSP2_OUTPUT_SYNCDOMAIN_SELECT2   */
  { 0x00C8, 0x00 },  /* AK7739_C0_0C8_DSP2_OUTPUT_SYNCDOMAIN_SELECT3   */
  { 0x00C9, 0x00 },  /* AK7739_C0_0C9_DSP2_OUTPUT_SYNCDOMAIN_SELECT4   */
  { 0x00CA, 0x00 },  /* AK7739_C0_0CA_DSP2_OUTPUT_SYNCDOMAIN_SELECT5   */
  { 0x00CB, 0x00 },  /* AK7739_C0_0CB_DSP2_OUTPUT_SYNCDOMAIN_SELECT6   */
  { 0x0106, 0x00 },  /* AK7739_C0_106_DIT_INPUT_DATA_SELECT            */
  { 0x0108, 0x20 },  /* AK7739_C0_108_DAC1_INPUT_DATA_SELECT           */
  { 0x0109, 0x20 },  /* AK7739_C0_109_DAC2_INPUT_DATA_SELECT           */
  { 0x0118, 0x00 },  /* AK7739_C0_118_MIXER1_CHA_INPUT_DATA_SELECT     */
  { 0x0119, 0x00 },  /* AK7739_C0_119_MIXER1_CHB_INPUT_DATA_SELECT     */
  { 0x011A, 0x00 },  /* AK7739_C0_11A_MIXER2_CHA_INPUT_DATA_SELECT     */
  { 0x011B, 0x00 },  /* AK7739_C0_11B_MIXER2_CHB_INPUT_DATA_SELECT     */
  { 0x0120, 0x40 },  /* AK7739_C0_120_SRC1_INPUT_DATA_SELECT           */
  { 0x0121, 0x00 },  /* AK7739_C0_121_SRC2_INPUT_DATA_SELECT           */
  { 0x0122, 0x00 },  /* AK7739_C0_122_SRC3_INPUT_DATA_SELECT           */
  { 0x0123, 0x00 },  /* AK7739_C0_123_SRC4_INPUT_DATA_SELECT           */
  { 0x0124, 0x00 },  /* AK7739_C0_124_SRC5_INPUT_DATA_SELECT           */
  { 0x0125, 0x00 },  /* AK7739_C0_125_SRC6_INPUT_DATA_SELECT           */
  { 0x0126, 0x00 },  /* AK7739_C0_126_SRC7_INPUT_DATA_SELECT           */
  { 0x0127, 0x00 },  /* AK7739_C0_127_SRC8_INPUT_DATA_SELECT           */
  { 0x0140, 0x00 },  /* AK7739_C0_140_SDOUT1A_OUTPUT_DATA_SELECT       */
  { 0x0141, 0x00 },  /* AK7739_C0_141_SDOUT1B_OUTPUT_DATA_SELECT       */
  { 0x0142, 0x00 },  /* AK7739_C0_142_SDOUT1C_OUTPUT_DATA_SELECT       */
  { 0x0143, 0x00 },  /* AK7739_C0_143_SDOUT1D_OUTPUT_DATA_SELECT       */
  { 0x0144, 0x00 },  /* AK7739_C0_144_SDOUT1E_OUTPUT_DATA_SELECT       */
  { 0x0145, 0x00 },  /* AK7739_C0_145_SDOUT1F_OUTPUT_DATA_SELECT       */
  { 0x0146, 0x00 },  /* AK7739_C0_146_SDOUT1G_OUTPUT_DATA_SELECT       */
  { 0x0147, 0x00 },  /* AK7739_C0_147_SDOUT1H_OUTPUT_DATA_SELECT       */
  { 0x0148, 0x80 },  /* AK7739_C0_148_SDOUT2A_OUTPUT_DATA_SELECT       */
  { 0x0149, 0x21 },  /* AK7739_C0_149_SDOUT2B_OUTPUT_DATA_SELECT       */
  { 0x014A, 0x21 },  /* AK7739_C0_14A_SDOUT2C_OUTPUT_DATA_SELECT       */
  { 0x014B, 0x00 },  /* AK7739_C0_14B_SDOUT2D_OUTPUT_DATA_SELECT       */
  { 0x014C, 0x00 },  /* AK7739_C0_14C_SDOUT2E_OUTPUT_DATA_SELECT       */
  { 0x014D, 0x00 },  /* AK7739_C0_14D_SDOUT2F_OUTPUT_DATA_SELECT       */
  { 0x014E, 0x00 },  /* AK7739_C0_14E_SDOUT2G_OUTPUT_DATA_SELECT       */
  { 0x014F, 0x00 },  /* AK7739_C0_14F_SDOUT2H_OUTPUT_DATA_SELECT       */
  { 0x0150, 0x00 },  /* AK7739_C0_150_SDOUT3A_OUTPUT_DATA_SELECT       */
  { 0x0151, 0x00 },  /* AK7739_C0_151_SDOUT3B_OUTPUT_DATA_SELECT       */
  { 0x0152, 0x00 },  /* AK7739_C0_152_SDOUT3C_OUTPUT_DATA_SELECT       */
  { 0x0153, 0x00 },  /* AK7739_C0_153_SDOUT3D_OUTPUT_DATA_SELECT       */
  { 0x0154, 0x00 },  /* AK7739_C0_154_SDOUT3E_OUTPUT_DATA_SELECT       */
  { 0x0155, 0x00 },  /* AK7739_C0_155_SDOUT3F_OUTPUT_DATA_SELECT       */
  { 0x0156, 0x00 },  /* AK7739_C0_156_SDOUT3G_OUTPUT_DATA_SELECT       */
  { 0x0157, 0x00 },  /* AK7739_C0_157_SDOUT3H_OUTPUT_DATA_SELECT       */
  { 0x0158, 0x00 },  /* AK7739_C0_158_SDOUT4A_OUTPUT_DATA_SELECT       */
  { 0x0159, 0x00 },  /* AK7739_C0_159_SDOUT4B_OUTPUT_DATA_SELECT       */
  { 0x015A, 0x00 },  /* AK7739_C0_15A_SDOUT4C_OUTPUT_DATA_SELECT       */
  { 0x015B, 0x00 },  /* AK7739_C0_15B_SDOUT4D_OUTPUT_DATA_SELECT       */
  { 0x015C, 0x00 },  /* AK7739_C0_15C_SDOUT4E_OUTPUT_DATA_SELECT       */
  { 0x015D, 0x00 },  /* AK7739_C0_15D_SDOUT4F_OUTPUT_DATA_SELECT       */
  { 0x015E, 0x00 },  /* AK7739_C0_15E_SDOUT4G_OUTPUT_DATA_SELECT       */
  { 0x015F, 0x00 },  /* AK7739_C0_15F_SDOUT4H_OUTPUT_DATA_SELECT       */
  { 0x0160, 0x00 },  /* AK7739_C0_160_SDOUT5A_OUTPUT_DATA_SELECT       */
  { 0x0161, 0x00 },  /* AK7739_C0_161_SDOUT5B_OUTPUT_DATA_SELECT       */
  { 0x0162, 0x00 },  /* AK7739_C0_162_SDOUT5C_OUTPUT_DATA_SELECT       */
  { 0x0163, 0x00 },  /* AK7739_C0_163_SDOUT5D_OUTPUT_DATA_SELECT       */
  { 0x0164, 0x00 },  /* AK7739_C0_164_SDOUT5E_OUTPUT_DATA_SELECT       */
  { 0x0165, 0x00 },  /* AK7739_C0_165_SDOUT5F_OUTPUT_DATA_SELECT       */
  { 0x0166, 0x00 },  /* AK7739_C0_166_SDOUT5G_OUTPUT_DATA_SELECT       */
  { 0x0167, 0x00 },  /* AK7739_C0_167_SDOUT5H_OUTPUT_DATA_SELECT       */
  { 0x0168, 0x00 },  /* AK7739_C0_168_SDOUT6A_OUTPUT_DATA_SELECT       */
  { 0x0169, 0x00 },  /* AK7739_C0_169_SDOUT6B_OUTPUT_DATA_SELECT       */
  { 0x016A, 0x00 },  /* AK7739_C0_16A_SDOUT6C_OUTPUT_DATA_SELECT       */
  { 0x016B, 0x00 },  /* AK7739_C0_16B_SDOUT6D_OUTPUT_DATA_SELECT       */
  { 0x016C, 0x00 },  /* AK7739_C0_16C_SDOUT6E_OUTPUT_DATA_SELECT       */
  { 0x016D, 0x00 },  /* AK7739_C0_16D_SDOUT6F_OUTPUT_DATA_SELECT       */
  { 0x016E, 0x00 },  /* AK7739_C0_16E_SDOUT6G_OUTPUT_DATA_SELECT       */
  { 0x016F, 0x00 },  /* AK7739_C0_16F_SDOUT6H_OUTPUT_DATA_SELECT       */
  { 0x0180, 0x08 },  /* AK7739_C0_180_DSP1_DIN1_INPUT_DATA_SELECT      */
  { 0x0181, 0x09 },  /* AK7739_C0_181_DSP1_DIN2_INPUT_DATA_SELECT      */
  { 0x0182, 0x00 },  /* AK7739_C0_182_DSP1_DIN3_INPUT_DATA_SELECT      */
  { 0x0183, 0x00 },  /* AK7739_C0_183_DSP1_DIN4_INPUT_DATA_SELECT      */
  { 0x0184, 0x00 },  /* AK7739_C0_184_DSP1_DIN5_INPUT_DATA_SELECT      */
  { 0x0185, 0x00 },  /* AK7739_C0_185_DSP1_DIN6_INPUT_DATA_SELECT      */
  { 0x0186, 0x00 },  /* AK7739_C0_186_DSP2_DIN1_INPUT_DATA_SELECT      */
  { 0x0187, 0x00 },  /* AK7739_C0_187_DSP2_DIN2_INPUT_DATA_SELECT      */
  { 0x0188, 0x00 },  /* AK7739_C0_188_DSP2_DIN3_INPUT_DATA_SELECT      */
  { 0x0189, 0x00 },  /* AK7739_C0_189_DSP2_DIN4_INPUT_DATA_SELECT      */
  { 0x018A, 0x00 },  /* AK7739_C0_18A_DSP2_DIN5_INPUT_DATA_SELECT      */
  { 0x018B, 0x00 },  /* AK7739_C0_18B_DSP2_DIN6_INPUT_DATA_SELECT      */
  { 0x0200, 0x00 },  /* AK7739_C0_200_SDOUT_OUTPUT_DATA_SELECT         */
  { 0x0201, 0xFC },  /* AK7739_C0_201_SDOUT_ENABLE_SETTING             */
  { 0x0202, 0x00 },  /* AK7739_C0_202_SDOUT_OUTPUT_MODE_SETTING        */
  { 0x0203, 0x00 },  /* AK7739_C0_203_SDIN_INPUT_DATA_SELECT           */
  { 0x0204, 0x00 },  /* AK7739_C0_204_MASTER_SPI_SELECT                */
  { 0x0205, 0x00 },  /* AK7739_C0_205_STO_FLAG_SETTING                 */
  { 0x0206, 0x00 },  /* AK7739_C0_206_LRCK4_5_OUTPUT_DATA_SELECT       */
  { 0x0210, 0x00 },  /* AK7739_C0_210_MASTER_SPI_TX00                  */
  { 0x0211, 0x00 },  /* AK7739_C0_211_MASTER_SPI_TX01                  */
  { 0x0212, 0x00 },  /* AK7739_C0_212_MASTER_SPI_TX02                  */
  { 0x0213, 0x00 },  /* AK7739_C0_213_MASTER_SPI_TX03                  */
  { 0x0214, 0x00 },  /* AK7739_C0_214_MASTER_SPI_TX04                  */
  { 0x0215, 0x00 },  /* AK7739_C0_215_MASTER_SPI_TX05                  */
  { 0x0216, 0x00 },  /* AK7739_C0_216_MASTER_SPI_TX06                  */
  { 0x0217, 0x00 },  /* AK7739_C0_217_MASTER_SPI_TX07                  */
  { 0x0218, 0x00 },  /* AK7739_C0_218_MASTER_SPI_TX08                  */
  { 0x0219, 0x00 },  /* AK7739_C0_219_MASTER_SPI_TX09                  */
  { 0x021A, 0x00 },  /* AK7739_C0_21A_MASTER_SPI_TX10                  */
  { 0x021B, 0x00 },  /* AK7739_C0_21B_MASTER_SPI_TX11                  */
  { 0x021C, 0x00 },  /* AK7739_C0_21C_MASTER_SPI_TX12                  */
  { 0x021D, 0x00 },  /* AK7739_C0_21D_MASTER_SPI_TX13                  */
  { 0x021E, 0x00 },  /* AK7739_C0_21E_MASTER_SPI_TX14                  */
  { 0x021F, 0x00 },  /* AK7739_C0_21F_MASTER_SPI_TX15                  */
  { 0x0220, 0x00 },  /* AK7739_C0_220_MASTER_SPI_TX16                  */
  { 0x0221, 0x00 },  /* AK7739_C0_221_MASTER_SPI_TX17                  */
  { 0x0222, 0x00 },  /* AK7739_C0_222_MASTER_SPI_TX18                  */
  { 0x0223, 0x00 },  /* AK7739_C0_223_MASTER_SPI_TX19                  */
  { 0x0224, 0x00 },  /* AK7739_C0_224_MASTER_SPI_TX20                  */
  { 0x0225, 0x00 },  /* AK7739_C0_225_MASTER_SPI_TX21                  */
  { 0x0226, 0x00 },  /* AK7739_C0_226_MASTER_SPI_TX22                  */
  { 0x0227, 0x00 },  /* AK7739_C0_227_MASTER_SPI_TX23                  */
  { 0x0228, 0x00 },  /* AK7739_C0_228_MASTER_SPI_TX24                  */
  { 0x0229, 0x00 },  /* AK7739_C0_229_MASTER_SPI_TX25                  */
  { 0x022A, 0x00 },  /* AK7739_C0_22A_MASTER_SPI_TX26                  */
  { 0x022B, 0x00 },  /* AK7739_C0_22B_MASTER_SPI_TX27                  */
  { 0x022C, 0x00 },  /* AK7739_C0_22C_MASTER_SPI_TX28                  */
  { 0x022D, 0x00 },  /* AK7739_C0_22D_MASTER_SPI_TX29                  */
  { 0x022E, 0x00 },  /* AK7739_C0_22E_MASTER_SPI_TX30                  */
  { 0x022F, 0x00 },  /* AK7739_C0_22F_MASTER_SPI_TX31                  */
  { 0x0300, 0x39 },	 /* AK7739_C0_300_DEVICE_ID                        */
  { 0x0301, 0x00 },	 /* AK7739_C0_301_REVISION_NUM                     */
  { 0x0302, 0x00 },	 /* AK7739_C0_302_CRC_ERROR_STATUS                 */
  { 0x0303, 0x00 },	 /* AK7739_C0_303_STO_READ_OUT                     */
  { 0x030F, 0x30 },	 /* AK7739_C0_30F_MASTER_SPI_STATUS                */
  { 0x0310, 0x00 },	 /* AK7739_C0_310_MASTER_SPI_RX00                  */
  { 0x0311, 0x00 },	 /* AK7739_C0_311_MASTER_SPI_RX01                  */
  { 0x0312, 0x00 },	 /* AK7739_C0_312_MASTER_SPI_RX02                  */
  { 0x0313, 0x00 },  /* AK7739_C0_313_MASTER_SPI_RX03                  */
  { 0x0314, 0x00 },  /* AK7739_C0_314_MASTER_SPI_RX04                  */
  { 0x0315, 0x00 },  /* AK7739_C0_315_MASTER_SPI_RX05                  */
  { 0x0316, 0x00 },  /* AK7739_C0_316_MASTER_SPI_RX06                  */
  { 0x0317, 0x00 },  /* AK7739_C0_317_MASTER_SPI_RX07                  */
  { 0x0318, 0x00 },  /* AK7739_C0_318_MASTER_SPI_RX08                  */
  { 0x0319, 0x00 },  /* AK7739_C0_319_MASTER_SPI_RX09                  */
  { 0x031A, 0x00 },  /* AK7739_C0_31A_MASTER_SPI_RX10                  */
  { 0x031B, 0x00 },  /* AK7739_C0_31B_MASTER_SPI_RX11                  */
  { 0x031C, 0x00 },  /* AK7739_C0_31C_MASTER_SPI_RX12                  */
  { 0x031D, 0x00 },  /* AK7739_C0_31D_MASTER_SPI_RX13                  */
  { 0x031E, 0x00 },  /* AK7739_C0_31E_MASTER_SPI_RX14                  */
  { 0x031F, 0x00 },  /* AK7739_C0_31F_MASTER_SPI_RX15                  */
  { 0x0320, 0x00 },	 /* AK7739_C0_320_MASTER_SPI_RX16                  */
  { 0x0321, 0x00 },  /* AK7739_C0_321_MASTER_SPI_RX17                  */
  { 0x0322, 0x00 },	 /* AK7739_C0_322_MASTER_SPI_RX18                  */
  { 0x0323, 0x00 },  /* AK7739_C0_323_MASTER_SPI_RX19                  */
  { 0x0324, 0x00 },  /* AK7739_C0_324_MASTER_SPI_RX20                  */
  { 0x0325, 0x00 },  /* AK7739_C0_325_MASTER_SPI_RX21                  */
  { 0x0326, 0x00 },  /* AK7739_C0_326_MASTER_SPI_RX22                  */
  { 0x0327, 0x00 },  /* AK7739_C0_327_MASTER_SPI_RX23                  */
  { 0x0328, 0x00 },  /* AK7739_C0_328_MASTER_SPI_RX24                  */
  { 0x0329, 0x00 },  /* AK7739_C0_329_MASTER_SPI_RX25                  */
  { 0x032A, 0x00 },  /* AK7739_C0_32A_MASTER_SPI_RX26                  */
  { 0x032B, 0x00 },  /* AK7739_C0_32B_MASTER_SPI_RX27                  */
  { 0x032C, 0x00 },  /* AK7739_C0_32C_MASTER_SPI_RX28                  */
  { 0x032D, 0x00 },  /* AK7739_C0_32D_MASTER_SPI_RX29                  */
  { 0x032E, 0x00 },  /* AK7739_C0_32E_MASTER_SPI_RX30                  */
  { 0x032F, 0x00 },  /* AK7739_C0_32F_MASTER_SPI_RX31                  */

  /* DSP block */
  { 0x1000, 0x00 },  /* AK7739_C1_000_DSP_RESET_CONTROL                */
  { 0x1001, 0x00 },  /* AK7739_C1_001_DSP_CLOCK_SETTING                */
  { 0x1002, 0x00 },  /* AK7739_C1_002_RAM_CLEAR_SETTING                */
  { 0x1003, 0x00 },  /* AK7739_C1_003_DSP_WATCHDOG_TIMER_FLAG_SETTING  */
  { 0x1004, 0x00 },  /* AK7739_C1_004_DSP_GPO_SETTING                  */
  { 0x1005, 0x00 },  /* AK7739_C1_005_DSP1,2,3_GPO_STATUS              */
  { 0x1008, 0x00 },  /* AK7739_C1_008_DSP_WATCHDOG_TIMER_ERROR_STATUS  */
  { 0x1011, 0x00 },  /* AK7739_C1_011_DSP1_DRAM_SETTING                */
  { 0x1012, 0x00 },  /* AK7739_C1_012_DSP2_DRAM_SETTING                */
  { 0x1013, 0x00 },  /* AK7739_C1_013_DSP3_DRAM_SETTING                */
  { 0x1020, 0x04 },  /* AK7739_C1_020_DSP1,2_DLYRAM_ASSIGNMENT         */
  { 0x1021, 0x00 },  /* AK7739_C1_021_DSP1_DLYRAM_SETTING              */
  { 0x1022, 0x00 },  /* AK7739_C1_022_DSP2_DLYRAM_SETTING              */
  { 0x1031, 0x10 },  /* AK7739_C1_031_DSP1_CRAM&DLP0_SETTING           */
  { 0x1032, 0x00 },  /* AK7739_C1_032_DSP2_CRAM&DLP0_SETTING           */
  { 0x1033, 0x00 },  /* AK7739_C1_033_DSP3_CRAM_SETTING                */
  { 0x1040, 0x00 },  /* AK7739_C1_040_DSP1_JX_SETTING                  */
  { 0x1041, 0x00 },  /* AK7739_C1_041_DSP2_JX_SETTING                  */
  { 0x1042, 0x00 },  /* AK7739_C1_042_DSP3_JX_SETTING                  */

  /* SRC block */
  { 0x2000, 0x80 },  /* AK7739_C2_000_SRC_POWER_MANAGEMENT             */
  { 0x2001, 0x00 },  /* AK7739_C2_001_SRC_FILTER_SETTING1              */
  { 0x2003, 0x00 },  /* AK7739_C2_003_SRC_PHASE_GROUP1                 */
  { 0x2005, 0x00 },  /* AK7739_C2_005_SRC_MUTE_SETTING1                */
  { 0x2006, 0x00 },  /* AK7739_C2_006_SRC_MUTE_SETTING2                */
  { 0x2007, 0x00 },  /* AK7739_C2_007_SRC_STO_FLAG_SETTING             */
  { 0x2010, 0x00 },  /* AK7739_C2_010_SRC_STATUS1                      */
  { 0x2101, 0x30 },  /* AK7739_C2_101_MONO_SRC_POWER_MANAGEMENT        */
  { 0x2102, 0x00 },  /* AK7739_C2_102_MONO_SRC_FILTER_SETTING          */
  { 0x2103, 0x00 },  /* AK7739_C2_103_MONO_SRC_PHASE_GROUP             */
  { 0x2104, 0x00 },  /* AK7739_C2_104_MONO_SRC_MUTE_SETTING            */
  { 0x2105, 0x00 },  /* AK7739_C2_105_MONO_SRC_STO_FLAG_SETTING        */
  { 0x2106, 0x00 },  /* AK7739_C2_106_MONO_SRC_PATH_SETTING            */
  { 0x2110, 0x00 },  /* AK7739_C2_110_MONO_SRC_STATUS1                 */
  { 0x2200, 0x00 },  /* AK7739_C2_200_DIT_POWER_MANAGEMENT             */
  { 0x2201, 0x00 },  /* AK7739_C2_201_DIT_STATUS_BIT1                  */
  { 0x2202, 0x04 },  /* AK7739_C2_202_DIT_STATUS_BIT2                  */
  { 0x2203, 0x02 },  /* AK7739_C2_203_DIT_STATUS_BIT3                  */
  { 0x2204, 0x00 },  /* AK7739_C2_204_DIT_STATUS_BIT4                  */
  { 0x2210, 0x00 },  /* AK7739_C2_210_MIXER1_SETTING                   */
  { 0x2211, 0x00 },  /* AK7739_C2_211_MIXER2_SETTING                   */

  /* CODEC block */
  { 0x3000, 0x36 },  /* AK7739_C3_000_POWER_MANAGEMENT                 */
  { 0x3001, 0x20 },  /* AK7739_C3_001_MICBIAS_POWER_MANAGEMENT         */
  { 0x3002, 0x01 },  /* AK7739_C3_002_RESET_CONTROL                    */
  { 0x3003, 0x01 },  /* AK7739_C3_003_SYSTEM_CLOCK_SETTING             */
  { 0x3004, 0x11 },  /* AK7739_C3_004_MIC_AMP_GAIN                     */
  { 0x3005, 0x30 },  /* AK7739_C3_005_ADC1_LCH_DIGITAL_VOLUME          */
  { 0x3006, 0x30 },  /* AK7739_C3_006_ADC1_RCH_DIGITAL_VOLUME          */
  { 0x3007, 0x30 },  /* AK7739_C3_007_ADC2_LCH_DIGITAL_VOLUME          */
  { 0x3008, 0x30 },  /* AK7739_C3_008_ADC2_RCH_DIGITAL_VOLUME          */
  { 0x3009, 0x04 },  /* AK7739_C3_009_ADC_DIGITAL_FILTER_SETTING       */
  { 0x300A, 0x15 },  /* AK7739_C3_00A_ADC_ANALOG_INPUT_SETTING         */
  { 0x300B, 0x00 },  /* AK7739_C3_00B_ADC_MUTE&HPF_CONTROL             */
  { 0x300C, 0x18 },  /* AK7739_C3_00C_DAC1_LCH_DIGITAL_VOLUME          */
  { 0x300D, 0x18 },  /* AK7739_C3_00D_DAC1_RCH_DIGITAL_VOLUME          */
  { 0x300E, 0x18 },  /* AK7739_C3_00E_DAC2_LCH_DIGITAL_VOLUME          */
  { 0x300F, 0x18 },  /* AK7739_C3_00F_DAC2_RCH_DIGITAL_VOLUME          */
  { 0x3013, 0x15 },  /* AK7739_C3_013_DAC_De-EMPHASIS_SETTING          */
  { 0x3014, 0x02 },  /* AK7739_C3_014_DAC_MUTE&FILTER_SETTING          */
  { 0x3015, 0x00 },  /* AK7739_C3_015_DIGITAL_MIC_CONTROL              */

// VIRT Register for DSP Connection
  { 0x3016, 0x00 },  /* AK7739_VIRT_C3_016_DSP1OUT1_MIX              */
  { 0x3017, 0x00 },  /* AK7739_VIRT_C3_017_DSP1OUT2_MIX              */
  { 0x3018, 0x00 },  /* AK7739_VIRT_C3_018_DSP1OUT3_MIX              */
  { 0x3019, 0x00 },  /* AK7739_VIRT_C3_019_DSP1OUT4_MIX              */
  { 0x301A, 0x00 },  /* AK7739_VIRT_C3_01A_DSP1OUT5_MIX              */
  { 0x301B, 0x00 },  /* AK7739_VIRT_C3_01B_DSP1OUT6_MIX              */
  { 0x301C, 0x00 },  /* AK7739_VIRT_C3_01C_DSP2OUT1_MIX              */
  { 0x301D, 0x00 },  /* AK7739_VIRT_C3_01D_DSP2OUT2_MIX              */
  { 0x301E, 0x00 },  /* AK7739_VIRT_C3_01E_DSP2OUT3_MIX              */
  { 0x301F, 0x00 },  /* AK7739_VIRT_C3_01F_DSP2OUT4_MIX              */
  { 0x3020, 0x00 },  /* AK7739_VIRT_C3_020_DSP2OUT5_MIX              */
  { 0x3021, 0x00 },  /* AK7739_VIRT_C3_021_DSP2OUT6_MIX              */
};

/* ak7739 register cache & default register settings */
static const struct reg_default ak7739_tdm_reg[] = {
	/* AUDIOHUB,PERI block */
  { 0x0000, 0x03 },  /* AK7739_C0_000_SYSCLOCK_SETTING1                */
  { 0x0001, 0x01 },  /* AK7739_C0_001_SYSCLOCK_SETTING2                */
  { 0x0002, 0x03 },  /* AK7739_C0_002_SYSCLOCK_SETTING3                */
  { 0x0003, 0x5c },  /* AK7739_C0_003_SYSCLOCK_SETTING4                */
  { 0x0004, 0x00 },  /* AK7739_C0_004_SYSCLOCK_SETTING5                */
  { 0x0008, 0x01 },  /* AK7739_C0_008_AHCLK_SETTING5                   */
  { 0x0009, 0x01 },  /* AK7739_C0_009_BUSCLOCK_SETTING                 */
  { 0x000A, 0x02 },  /* AK7739_C0_00A_BUSCLOCK_SETTING2                */
  { 0x000B, 0x00 },  /* AK7739_C0_00B_CLKO_SETTING1                    */
  { 0x000C, 0x00 },  /* AK7739_C0_00C_CLKO_SETTING2                    */
  { 0x000D, 0x02 },  /* AK7739_C0_00D_MasterSPI_SCLK_SETTING1          */
  { 0x000E, 0x01 },  /* AK7739_C0_00E_MasterSPI_SCLK_SETTING2          */
  { 0x0010, 0x00 },  /* AK7739_C0_010_SYNCDOMAIN1_SETTING1             */
  { 0x0011, 0x01 },  /* AK7739_C0_011_SYNCDOMAIN1_SETTING2             */
  { 0x0012, 0x17 },  /* AK7739_C0_012_SYNCDOMAIN1_SETTING3             */
  { 0x0013, 0x00 },  /* AK7739_C0_013_SYNCDOMAIN1_SETTING4             */
  { 0x0014, 0x80 },  /* AK7739_C0_014_SYNCDOMAIN2_SETTING1             */
  { 0x0015, 0x01 },  /* AK7739_C0_015_SYNCDOMAIN2_SETTING2             */
  { 0x0016, 0x05 },  /* AK7739_C0_016_SYNCDOMAIN2_SETTING3             */
  { 0x0017, 0x04 },  /* AK7739_C0_017_SYNCDOMAIN2_SETTING4             */
  { 0x0018, 0x80 },  /* AK7739_C0_018_SYNCDOMAIN3_SETTING1             */
  { 0x0019, 0x01 },  /* AK7739_C0_019_SYNCDOMAIN3_SETTING2             */
  { 0x001A, 0x17 },  /* AK7739_C0_01A_SYNCDOMAIN3_SETTING3             */
  { 0x001B, 0x00 },  /* AK7739_C0_01B_SYNCDOMAIN3_SETTING4             */
  { 0x001C, 0x00 },  /* AK7739_C0_01C_SYNCDOMAIN4_SETTING1             */
  { 0x001D, 0x00 },  /* AK7739_C0_01D_SYNCDOMAIN4_SETTING2             */
  { 0x001E, 0x00 },  /* AK7739_C0_01E_SYNCDOMAIN4_SETTING3             */
  { 0x001F, 0x00 },  /* AK7739_C0_01F_SYNCDOMAIN4_SETTING4             */
  { 0x0020, 0x00 },  /* AK7739_C0_020_SYNCDOMAIN5_SETTING1             */
  { 0x0021, 0x00 },  /* AK7739_C0_021_SYNCDOMAIN5_SETTING2             */
  { 0x0022, 0x00 },  /* AK7739_C0_022_SYNCDOMAIN5_SETTING3             */
  { 0x0023, 0x00 },  /* AK7739_C0_023_SYNCDOMAIN5_SETTING4             */
  { 0x0025, 0x00 },  /* AK7739_C0_025_SYNCDOMAIN6_SETTING2             */
  { 0x0026, 0x00 },  /* AK7739_C0_026_SYNCDOMAIN6_SETTING3             */
  { 0x0027, 0x00 },  /* AK7739_C0_027_SYNCDOMAIN6_SETTING4             */
  { 0x0029, 0x01 },  /* AK7739_C0_029_SYNCDOMAIN7_SETTING2             */
  { 0x002A, 0x47 },  /* AK7739_C0_02A_SYNCDOMAIN7_SETTING3             */
  { 0x002B, 0x00 },  /* AK7739_C0_02B_SYNCDOMAIN7_SETTING4             */
  { 0x0040, 0x00 },  /* AK7739_C0_040_BICK_FORMAT_SETTING1             */
  { 0x0041, 0x01 },  /* AK7739_C0_041_BICK_SYNCDOMAIN_SELECT1          */
  { 0x0042, 0x00 },  /* AK7739_C0_042_BICK_FORMAT_SETTING2             */
  { 0x0043, 0x02 },  /* AK7739_C0_043_BICK_SYNCDOMAIN_SELECT2          */
  { 0x0044, 0x00 },  /* AK7739_C0_044_BICK_FORMAT_SETTING3             */
  { 0x0045, 0x00 },  /* AK7739_C0_045_BICK_SYNCDOMAIN_SELECT3          */
  { 0x0046, 0x00 },  /* AK7739_C0_046_BICK_FORMAT_SETTING4             */
  { 0x0047, 0x00 },  /* AK7739_C0_047_BICK_SYNCDOMAIN_SELECT4          */
  { 0x0048, 0x00 },  /* AK7739_C0_048_BICK_FORMAT_SETTING5             */
  { 0x0049, 0x00 },  /* AK7739_C0_049_BICK_SYNCDOMAIN_SELECT5          */
  { 0x0050, 0x32 },  /* AK7739_C0_050_SDIN1_INPUT_FORMAT               */
  { 0x0051, 0x01 },  /* AK7739_C0_051_SDIN1_SYNCDOMAIN_SELECT          */
  { 0x0052, 0x00 },  /* AK7739_C0_052_SDIN2_INPUT_FORMAT               */
  { 0x0053, 0x00 },  /* AK7739_C0_053_SDIN2_SYNCDOMAIN_SELECT          */
  { 0x0054, 0x00 },  /* AK7739_C0_054_SDIN3_INPUT_FORMAT               */
  { 0x0055, 0x00 },  /* AK7739_C0_055_SDIN3_SYNCDOMAIN_SELECT          */
  { 0x0056, 0x00 },  /* AK7739_C0_056_SDIN4_INPUT_FORMAT               */
  { 0x0057, 0x00 },  /* AK7739_C0_057_SDIN4_SYNCDOMAIN_SELECT          */
  { 0x0058, 0x00 },  /* AK7739_C0_058_SDIN5_INPUT_FORMAT               */
  { 0x0059, 0x00 },  /* AK7739_C0_059_SDIN5_SYNCDOMAIN_SELECT          */
  { 0x005A, 0x00 },  /* AK7739_C0_05A_SDIN6_INPUT_FORMAT               */
  { 0x005B, 0x00 },  /* AK7739_C0_05B_SDIN6_SYNCDOMAIN_SELECT          */
  { 0x0060, 0x32 },  /* AK7739_C0_060_SDOUT1_OUTPUT_FORMAT             */
  { 0x0061, 0x01 },  /* AK7739_C0_061_SDOUT1_SYNCDOMAIN_SELECT         */
  { 0x0062, 0xB0 },  /* AK7739_C0_062_SDOUT2_OUTPUT_FORMAT             */
  { 0x0063, 0x02 },  /* AK7739_C0_063_SDOUT2_SYNCDOMAIN_SELECT         */
  { 0x0064, 0x00 },  /* AK7739_C0_064_SDOUT3_OUTPUT_FORMAT             */
  { 0x0065, 0x00 },  /* AK7739_C0_065_SDOUT3_SYNCDOMAIN_SELECT         */
  { 0x0066, 0x00 },  /* AK7739_C0_066_SDOUT4_OUTPUT_FORMAT             */
  { 0x0067, 0x00 },  /* AK7739_C0_067_SDOUT4_SYNCDOMAIN_SELECT         */
  { 0x0068, 0x00 },  /* AK7739_C0_068_SDOUT5_OUTPUT_FORMAT             */
  { 0x0069, 0x00 },  /* AK7739_C0_069_SDOUT5_SYNCDOMAIN_SELECT         */
  { 0x006A, 0x00 },  /* AK7739_C0_06A_SDOUT6_OUTPUT_FORMAT             */
  { 0x006B, 0x00 },  /* AK7739_C0_06B_SDOUT6_SYNCDOMAIN_SELECT         */
  { 0x0087, 0x00 },  /* AK7739_C0_087_OSMEM_SYNCDOMAIN_SELECT          */
  { 0x0088, 0x07 },  /* AK7739_C0_088_ADC1_SYNCDOMAIN_SELECT           */
  { 0x0089, 0x07 },  /* AK7739_C0_089_CODEC_SYNCDOMAIN_SELECT          */
  { 0x0098, 0x07 },  /* AK7739_C0_098_MIXER_CH1_INPUT_SELECT           */
  { 0x0099, 0x00 },  /* AK7739_C0_099_MIXER_CH1_INPUT_SELECT           */
  { 0x00A0, 0x03 },  /* AK7739_C0_0A0_SRC1_SYNCDOMAIN_SELECT           */
  { 0x00A1, 0x03 },  /* AK7739_C0_0A1_SRC2_SYNCDOMAIN_SELECT           */
  { 0x00A2, 0x00 },  /* AK7739_C0_0A2_SRC3_SYNCDOMAIN_SELECT           */
  { 0x00A3, 0x00 },  /* AK7739_C0_0A3_SRC4_SYNCDOMAIN_SELECT           */
  { 0x00A4, 0x00 },  /* AK7739_C0_0A4_SRC5_SYNCDOMAIN_SELECT           */
  { 0x00A5, 0x00 },  /* AK7739_C0_0A5_SRC6_SYNCDOMAIN_SELECT           */
  { 0x00A6, 0x00 },  /* AK7739_C0_0A6_SRC7_SYNCDOMAIN_SELECT           */
  { 0x00A7, 0x00 },  /* AK7739_C0_0A7_SRC8_SYNCDOMAIN_SELECT           */
  { 0x00B0, 0x07 },  /* AK7739_C0_0B0_DSP1_SYNCDOMAIN_SELECT           */
  { 0x00B1, 0x00 },  /* AK7739_C0_0B1_DSP2_SYNCDOMAIN_SELECT           */
  { 0x00C0, 0x07 },  /* AK7739_C0_0C0_DSP1_OUTPUT_SYNCDOMAIN_SELECT1   */
  { 0x00C1, 0x00 },  /* AK7739_C0_0C1_DSP1_OUTPUT_SYNCDOMAIN_SELECT2   */
  { 0x00C2, 0x00 },  /* AK7739_C0_0C2_DSP1_OUTPUT_SYNCDOMAIN_SELECT3   */
  { 0x00C3, 0x00 },  /* AK7739_C0_0C3_DSP1_OUTPUT_SYNCDOMAIN_SELECT4   */
  { 0x00C4, 0x00 },  /* AK7739_C0_0C4_DSP1_OUTPUT_SYNCDOMAIN_SELECT5   */
  { 0x00C5, 0x00 },  /* AK7739_C0_0C5_DSP1_OUTPUT_SYNCDOMAIN_SELECT6   */
  { 0x00C6, 0x00 },  /* AK7739_C0_0C6_DSP2_OUTPUT_SYNCDOMAIN_SELECT1   */
  { 0x00C7, 0x00 },  /* AK7739_C0_0C7_DSP2_OUTPUT_SYNCDOMAIN_SELECT2   */
  { 0x00C8, 0x00 },  /* AK7739_C0_0C8_DSP2_OUTPUT_SYNCDOMAIN_SELECT3   */
  { 0x00C9, 0x00 },  /* AK7739_C0_0C9_DSP2_OUTPUT_SYNCDOMAIN_SELECT4   */
  { 0x00CA, 0x00 },  /* AK7739_C0_0CA_DSP2_OUTPUT_SYNCDOMAIN_SELECT5   */
  { 0x00CB, 0x00 },  /* AK7739_C0_0CB_DSP2_OUTPUT_SYNCDOMAIN_SELECT6   */
  { 0x0106, 0x00 },  /* AK7739_C0_106_DIT_INPUT_DATA_SELECT            */
  { 0x0108, 0x21 },  /* AK7739_C0_108_DAC1_INPUT_DATA_SELECT           */
  { 0x0109, 0x20 },  /* AK7739_C0_109_DAC2_INPUT_DATA_SELECT           */
  { 0x0118, 0x08 },  /* AK7739_C0_118_MIXER1_CHA_INPUT_DATA_SELECT     */
  { 0x0119, 0x09 },  /* AK7739_C0_119_MIXER1_CHB_INPUT_DATA_SELECT     */
  { 0x011A, 0x00 },  /* AK7739_C0_11A_MIXER2_CHA_INPUT_DATA_SELECT     */
  { 0x011B, 0x00 },  /* AK7739_C0_11B_MIXER2_CHB_INPUT_DATA_SELECT     */
  { 0x0120, 0x40 },  /* AK7739_C0_120_SRC1_INPUT_DATA_SELECT           */
  { 0x0121, 0x08 },  /* AK7739_C0_121_SRC2_INPUT_DATA_SELECT           */
  { 0x0122, 0x00 },  /* AK7739_C0_122_SRC3_INPUT_DATA_SELECT           */
  { 0x0123, 0x00 },  /* AK7739_C0_123_SRC4_INPUT_DATA_SELECT           */
  { 0x0124, 0x00 },  /* AK7739_C0_124_SRC5_INPUT_DATA_SELECT           */
  { 0x0125, 0x00 },  /* AK7739_C0_125_SRC6_INPUT_DATA_SELECT           */
  { 0x0126, 0x00 },  /* AK7739_C0_126_SRC7_INPUT_DATA_SELECT           */
  { 0x0127, 0x00 },  /* AK7739_C0_127_SRC8_INPUT_DATA_SELECT           */
  { 0x0140, 0x21 },  /* AK7739_C0_140_SDOUT1A_OUTPUT_DATA_SELECT       */
  { 0x0141, 0x00 },  /* AK7739_C0_141_SDOUT1B_OUTPUT_DATA_SELECT       */
  { 0x0142, 0x00 },  /* AK7739_C0_142_SDOUT1C_OUTPUT_DATA_SELECT       */
  { 0x0143, 0x00 },  /* AK7739_C0_143_SDOUT1D_OUTPUT_DATA_SELECT       */
  { 0x0144, 0x00 },  /* AK7739_C0_144_SDOUT1E_OUTPUT_DATA_SELECT       */
  { 0x0145, 0x00 },  /* AK7739_C0_145_SDOUT1F_OUTPUT_DATA_SELECT       */
  { 0x0146, 0x00 },  /* AK7739_C0_146_SDOUT1G_OUTPUT_DATA_SELECT       */
  { 0x0147, 0x00 },  /* AK7739_C0_147_SDOUT1H_OUTPUT_DATA_SELECT       */
  { 0x0148, 0x80 },  /* AK7739_C0_148_SDOUT2A_OUTPUT_DATA_SELECT       */
  { 0x0149, 0x21 },  /* AK7739_C0_149_SDOUT2B_OUTPUT_DATA_SELECT       */
  { 0x014A, 0x21 },  /* AK7739_C0_14A_SDOUT2C_OUTPUT_DATA_SELECT       */
  { 0x014B, 0x00 },  /* AK7739_C0_14B_SDOUT2D_OUTPUT_DATA_SELECT       */
  { 0x014C, 0x00 },  /* AK7739_C0_14C_SDOUT2E_OUTPUT_DATA_SELECT       */
  { 0x014D, 0x00 },  /* AK7739_C0_14D_SDOUT2F_OUTPUT_DATA_SELECT       */
  { 0x014E, 0x00 },  /* AK7739_C0_14E_SDOUT2G_OUTPUT_DATA_SELECT       */
  { 0x014F, 0x00 },  /* AK7739_C0_14F_SDOUT2H_OUTPUT_DATA_SELECT       */
  { 0x0150, 0x00 },  /* AK7739_C0_150_SDOUT3A_OUTPUT_DATA_SELECT       */
  { 0x0151, 0x00 },  /* AK7739_C0_151_SDOUT3B_OUTPUT_DATA_SELECT       */
  { 0x0152, 0x00 },  /* AK7739_C0_152_SDOUT3C_OUTPUT_DATA_SELECT       */
  { 0x0153, 0x00 },  /* AK7739_C0_153_SDOUT3D_OUTPUT_DATA_SELECT       */
  { 0x0154, 0x00 },  /* AK7739_C0_154_SDOUT3E_OUTPUT_DATA_SELECT       */
  { 0x0155, 0x00 },  /* AK7739_C0_155_SDOUT3F_OUTPUT_DATA_SELECT       */
  { 0x0156, 0x00 },  /* AK7739_C0_156_SDOUT3G_OUTPUT_DATA_SELECT       */
  { 0x0157, 0x00 },  /* AK7739_C0_157_SDOUT3H_OUTPUT_DATA_SELECT       */
  { 0x0158, 0x00 },  /* AK7739_C0_158_SDOUT4A_OUTPUT_DATA_SELECT       */
  { 0x0159, 0x00 },  /* AK7739_C0_159_SDOUT4B_OUTPUT_DATA_SELECT       */
  { 0x015A, 0x00 },  /* AK7739_C0_15A_SDOUT4C_OUTPUT_DATA_SELECT       */
  { 0x015B, 0x00 },  /* AK7739_C0_15B_SDOUT4D_OUTPUT_DATA_SELECT       */
  { 0x015C, 0x00 },  /* AK7739_C0_15C_SDOUT4E_OUTPUT_DATA_SELECT       */
  { 0x015D, 0x00 },  /* AK7739_C0_15D_SDOUT4F_OUTPUT_DATA_SELECT       */
  { 0x015E, 0x00 },  /* AK7739_C0_15E_SDOUT4G_OUTPUT_DATA_SELECT       */
  { 0x015F, 0x00 },  /* AK7739_C0_15F_SDOUT4H_OUTPUT_DATA_SELECT       */
  { 0x0160, 0x00 },  /* AK7739_C0_160_SDOUT5A_OUTPUT_DATA_SELECT       */
  { 0x0161, 0x00 },  /* AK7739_C0_161_SDOUT5B_OUTPUT_DATA_SELECT       */
  { 0x0162, 0x00 },  /* AK7739_C0_162_SDOUT5C_OUTPUT_DATA_SELECT       */
  { 0x0163, 0x00 },  /* AK7739_C0_163_SDOUT5D_OUTPUT_DATA_SELECT       */
  { 0x0164, 0x00 },  /* AK7739_C0_164_SDOUT5E_OUTPUT_DATA_SELECT       */
  { 0x0165, 0x00 },  /* AK7739_C0_165_SDOUT5F_OUTPUT_DATA_SELECT       */
  { 0x0166, 0x00 },  /* AK7739_C0_166_SDOUT5G_OUTPUT_DATA_SELECT       */
  { 0x0167, 0x00 },  /* AK7739_C0_167_SDOUT5H_OUTPUT_DATA_SELECT       */
  { 0x0168, 0x00 },  /* AK7739_C0_168_SDOUT6A_OUTPUT_DATA_SELECT       */
  { 0x0169, 0x00 },  /* AK7739_C0_169_SDOUT6B_OUTPUT_DATA_SELECT       */
  { 0x016A, 0x00 },  /* AK7739_C0_16A_SDOUT6C_OUTPUT_DATA_SELECT       */
  { 0x016B, 0x00 },  /* AK7739_C0_16B_SDOUT6D_OUTPUT_DATA_SELECT       */
  { 0x016C, 0x00 },  /* AK7739_C0_16C_SDOUT6E_OUTPUT_DATA_SELECT       */
  { 0x016D, 0x00 },  /* AK7739_C0_16D_SDOUT6F_OUTPUT_DATA_SELECT       */
  { 0x016E, 0x00 },  /* AK7739_C0_16E_SDOUT6G_OUTPUT_DATA_SELECT       */
  { 0x016F, 0x00 },  /* AK7739_C0_16F_SDOUT6H_OUTPUT_DATA_SELECT       */
  { 0x0180, 0x21 },  /* AK7739_C0_180_DSP1_DIN1_INPUT_DATA_SELECT      */
  { 0x0181, 0x00 },  /* AK7739_C0_181_DSP1_DIN2_INPUT_DATA_SELECT      */
  { 0x0182, 0x00 },  /* AK7739_C0_182_DSP1_DIN3_INPUT_DATA_SELECT      */
  { 0x0183, 0x00 },  /* AK7739_C0_183_DSP1_DIN4_INPUT_DATA_SELECT      */
  { 0x0184, 0x00 },  /* AK7739_C0_184_DSP1_DIN5_INPUT_DATA_SELECT      */
  { 0x0185, 0x00 },  /* AK7739_C0_185_DSP1_DIN6_INPUT_DATA_SELECT      */
  { 0x0186, 0x00 },  /* AK7739_C0_186_DSP2_DIN1_INPUT_DATA_SELECT      */
  { 0x0187, 0x00 },  /* AK7739_C0_187_DSP2_DIN2_INPUT_DATA_SELECT      */
  { 0x0188, 0x00 },  /* AK7739_C0_188_DSP2_DIN3_INPUT_DATA_SELECT      */
  { 0x0189, 0x00 },  /* AK7739_C0_189_DSP2_DIN4_INPUT_DATA_SELECT      */
  { 0x018A, 0x00 },  /* AK7739_C0_18A_DSP2_DIN5_INPUT_DATA_SELECT      */
  { 0x018B, 0x00 },  /* AK7739_C0_18B_DSP2_DIN6_INPUT_DATA_SELECT      */
  { 0x0200, 0x00 },  /* AK7739_C0_200_SDOUT_OUTPUT_DATA_SELECT         */
  { 0x0201, 0xFC },  /* AK7739_C0_201_SDOUT_ENABLE_SETTING             */
  { 0x0202, 0x00 },  /* AK7739_C0_202_SDOUT_OUTPUT_MODE_SETTING        */
  { 0x0203, 0x00 },  /* AK7739_C0_203_SDIN_INPUT_DATA_SELECT           */
  { 0x0204, 0x00 },  /* AK7739_C0_204_MASTER_SPI_SELECT                */
  { 0x0205, 0x00 },  /* AK7739_C0_205_STO_FLAG_SETTING                 */
  { 0x0206, 0x00 },  /* AK7739_C0_206_LRCK4_5_OUTPUT_DATA_SELECT       */
  { 0x0210, 0x00 },  /* AK7739_C0_210_MASTER_SPI_TX00                  */
  { 0x0211, 0x00 },  /* AK7739_C0_211_MASTER_SPI_TX01                  */
  { 0x0212, 0x00 },  /* AK7739_C0_212_MASTER_SPI_TX02                  */
  { 0x0213, 0x00 },  /* AK7739_C0_213_MASTER_SPI_TX03                  */
  { 0x0214, 0x00 },  /* AK7739_C0_214_MASTER_SPI_TX04                  */
  { 0x0215, 0x00 },  /* AK7739_C0_215_MASTER_SPI_TX05                  */
  { 0x0216, 0x00 },  /* AK7739_C0_216_MASTER_SPI_TX06                  */
  { 0x0217, 0x00 },  /* AK7739_C0_217_MASTER_SPI_TX07                  */
  { 0x0218, 0x00 },  /* AK7739_C0_218_MASTER_SPI_TX08                  */
  { 0x0219, 0x00 },  /* AK7739_C0_219_MASTER_SPI_TX09                  */
  { 0x021A, 0x00 },  /* AK7739_C0_21A_MASTER_SPI_TX10                  */
  { 0x021B, 0x00 },  /* AK7739_C0_21B_MASTER_SPI_TX11                  */
  { 0x021C, 0x00 },  /* AK7739_C0_21C_MASTER_SPI_TX12                  */
  { 0x021D, 0x00 },  /* AK7739_C0_21D_MASTER_SPI_TX13                  */
  { 0x021E, 0x00 },  /* AK7739_C0_21E_MASTER_SPI_TX14                  */
  { 0x021F, 0x00 },  /* AK7739_C0_21F_MASTER_SPI_TX15                  */
  { 0x0220, 0x00 },  /* AK7739_C0_220_MASTER_SPI_TX16                  */
  { 0x0221, 0x00 },  /* AK7739_C0_221_MASTER_SPI_TX17                  */
  { 0x0222, 0x00 },  /* AK7739_C0_222_MASTER_SPI_TX18                  */
  { 0x0223, 0x00 },  /* AK7739_C0_223_MASTER_SPI_TX19                  */
  { 0x0224, 0x00 },  /* AK7739_C0_224_MASTER_SPI_TX20                  */
  { 0x0225, 0x00 },  /* AK7739_C0_225_MASTER_SPI_TX21                  */
  { 0x0226, 0x00 },  /* AK7739_C0_226_MASTER_SPI_TX22                  */
  { 0x0227, 0x00 },  /* AK7739_C0_227_MASTER_SPI_TX23                  */
  { 0x0228, 0x00 },  /* AK7739_C0_228_MASTER_SPI_TX24                  */
  { 0x0229, 0x00 },  /* AK7739_C0_229_MASTER_SPI_TX25                  */
  { 0x022A, 0x00 },  /* AK7739_C0_22A_MASTER_SPI_TX26                  */
  { 0x022B, 0x00 },  /* AK7739_C0_22B_MASTER_SPI_TX27                  */
  { 0x022C, 0x00 },  /* AK7739_C0_22C_MASTER_SPI_TX28                  */
  { 0x022D, 0x00 },  /* AK7739_C0_22D_MASTER_SPI_TX29                  */
  { 0x022E, 0x00 },  /* AK7739_C0_22E_MASTER_SPI_TX30                  */
  { 0x022F, 0x00 },  /* AK7739_C0_22F_MASTER_SPI_TX31                  */
  { 0x0300, 0x39 },	 /* AK7739_C0_300_DEVICE_ID                        */
  { 0x0301, 0x00 },	 /* AK7739_C0_301_REVISION_NUM                     */
  { 0x0302, 0x00 },	 /* AK7739_C0_302_CRC_ERROR_STATUS                 */
  { 0x0303, 0x00 },	 /* AK7739_C0_303_STO_READ_OUT                     */
  { 0x030F, 0x30 },	 /* AK7739_C0_30F_MASTER_SPI_STATUS                */
  { 0x0310, 0x00 },	 /* AK7739_C0_310_MASTER_SPI_RX00                  */
  { 0x0311, 0x00 },	 /* AK7739_C0_311_MASTER_SPI_RX01                  */
  { 0x0312, 0x00 },	 /* AK7739_C0_312_MASTER_SPI_RX02                  */
  { 0x0313, 0x00 },  /* AK7739_C0_313_MASTER_SPI_RX03                  */
  { 0x0314, 0x00 },  /* AK7739_C0_314_MASTER_SPI_RX04                  */
  { 0x0315, 0x00 },  /* AK7739_C0_315_MASTER_SPI_RX05                  */
  { 0x0316, 0x00 },  /* AK7739_C0_316_MASTER_SPI_RX06                  */
  { 0x0317, 0x00 },  /* AK7739_C0_317_MASTER_SPI_RX07                  */
  { 0x0318, 0x00 },  /* AK7739_C0_318_MASTER_SPI_RX08                  */
  { 0x0319, 0x00 },  /* AK7739_C0_319_MASTER_SPI_RX09                  */
  { 0x031A, 0x00 },  /* AK7739_C0_31A_MASTER_SPI_RX10                  */
  { 0x031B, 0x00 },  /* AK7739_C0_31B_MASTER_SPI_RX11                  */
  { 0x031C, 0x00 },  /* AK7739_C0_31C_MASTER_SPI_RX12                  */
  { 0x031D, 0x00 },  /* AK7739_C0_31D_MASTER_SPI_RX13                  */
  { 0x031E, 0x00 },  /* AK7739_C0_31E_MASTER_SPI_RX14                  */
  { 0x031F, 0x00 },  /* AK7739_C0_31F_MASTER_SPI_RX15                  */
  { 0x0320, 0x00 },	 /* AK7739_C0_320_MASTER_SPI_RX16                  */
  { 0x0321, 0x00 },  /* AK7739_C0_321_MASTER_SPI_RX17                  */
  { 0x0322, 0x00 },	 /* AK7739_C0_322_MASTER_SPI_RX18                  */
  { 0x0323, 0x00 },  /* AK7739_C0_323_MASTER_SPI_RX19                  */
  { 0x0324, 0x00 },  /* AK7739_C0_324_MASTER_SPI_RX20                  */
  { 0x0325, 0x00 },  /* AK7739_C0_325_MASTER_SPI_RX21                  */
  { 0x0326, 0x00 },  /* AK7739_C0_326_MASTER_SPI_RX22                  */
  { 0x0327, 0x00 },  /* AK7739_C0_327_MASTER_SPI_RX23                  */
  { 0x0328, 0x00 },  /* AK7739_C0_328_MASTER_SPI_RX24                  */
  { 0x0329, 0x00 },  /* AK7739_C0_329_MASTER_SPI_RX25                  */
  { 0x032A, 0x00 },  /* AK7739_C0_32A_MASTER_SPI_RX26                  */
  { 0x032B, 0x00 },  /* AK7739_C0_32B_MASTER_SPI_RX27                  */
  { 0x032C, 0x00 },  /* AK7739_C0_32C_MASTER_SPI_RX28                  */
  { 0x032D, 0x00 },  /* AK7739_C0_32D_MASTER_SPI_RX29                  */
  { 0x032E, 0x00 },  /* AK7739_C0_32E_MASTER_SPI_RX30                  */
  { 0x032F, 0x00 },  /* AK7739_C0_32F_MASTER_SPI_RX31                  */

  /* DSP block */
  { 0x1000, 0x05 },  /* AK7739_C1_000_DSP_RESET_CONTROL                */
  { 0x1001, 0x00 },  /* AK7739_C1_001_DSP_CLOCK_SETTING                */
  { 0x1002, 0x00 },  /* AK7739_C1_002_RAM_CLEAR_SETTING                */
  { 0x1003, 0x00 },  /* AK7739_C1_003_DSP_WATCHDOG_TIMER_FLAG_SETTING  */
  { 0x1004, 0x00 },  /* AK7739_C1_004_DSP_GPO_SETTING                  */
  { 0x1005, 0x00 },  /* AK7739_C1_005_DSP1,2,3_GPO_STATUS              */
  { 0x1008, 0x00 },  /* AK7739_C1_008_DSP_WATCHDOG_TIMER_ERROR_STATUS  */
  { 0x1011, 0x00 },  /* AK7739_C1_011_DSP1_DRAM_SETTING                */
  { 0x1012, 0x00 },  /* AK7739_C1_012_DSP2_DRAM_SETTING                */
  { 0x1013, 0x00 },  /* AK7739_C1_013_DSP3_DRAM_SETTING                */
  { 0x1020, 0x04 },  /* AK7739_C1_020_DSP1,2_DLYRAM_ASSIGNMENT         */
  { 0x1021, 0x00 },  /* AK7739_C1_021_DSP1_DLYRAM_SETTING              */
  { 0x1022, 0x00 },  /* AK7739_C1_022_DSP2_DLYRAM_SETTING              */
  { 0x1031, 0x10 },  /* AK7739_C1_031_DSP1_CRAM&DLP0_SETTING           */
  { 0x1032, 0x00 },  /* AK7739_C1_032_DSP2_CRAM&DLP0_SETTING           */
  { 0x1033, 0x00 },  /* AK7739_C1_033_DSP3_CRAM_SETTING                */
  { 0x1040, 0x00 },  /* AK7739_C1_040_DSP1_JX_SETTING                  */
  { 0x1041, 0x00 },  /* AK7739_C1_041_DSP2_JX_SETTING                  */
  { 0x1042, 0x00 },  /* AK7739_C1_042_DSP3_JX_SETTING                  */

  /* SRC block */
  { 0x2000, 0xE0 },  /* AK7739_C2_000_SRC_POWER_MANAGEMENT             */
  { 0x2001, 0x00 },  /* AK7739_C2_001_SRC_FILTER_SETTING1              */
  { 0x2003, 0x00 },  /* AK7739_C2_003_SRC_PHASE_GROUP1                 */
  { 0x2005, 0x00 },  /* AK7739_C2_005_SRC_MUTE_SETTING1                */
  { 0x2006, 0x00 },  /* AK7739_C2_006_SRC_MUTE_SETTING2                */
  { 0x2007, 0x00 },  /* AK7739_C2_007_SRC_STO_FLAG_SETTING             */
  { 0x2010, 0x00 },  /* AK7739_C2_010_SRC_STATUS1                      */
  { 0x2101, 0x30 },  /* AK7739_C2_101_MONO_SRC_POWER_MANAGEMENT        */
  { 0x2102, 0x00 },  /* AK7739_C2_102_MONO_SRC_FILTER_SETTING          */
  { 0x2103, 0x00 },  /* AK7739_C2_103_MONO_SRC_PHASE_GROUP             */
  { 0x2104, 0x00 },  /* AK7739_C2_104_MONO_SRC_MUTE_SETTING            */
  { 0x2105, 0x00 },  /* AK7739_C2_105_MONO_SRC_STO_FLAG_SETTING        */
  { 0x2106, 0x00 },  /* AK7739_C2_106_MONO_SRC_PATH_SETTING            */
  { 0x2110, 0x00 },  /* AK7739_C2_110_MONO_SRC_STATUS1                 */
  { 0x2200, 0x00 },  /* AK7739_C2_200_DIT_POWER_MANAGEMENT             */
  { 0x2201, 0x00 },  /* AK7739_C2_201_DIT_STATUS_BIT1                  */
  { 0x2202, 0x04 },  /* AK7739_C2_202_DIT_STATUS_BIT2                  */
  { 0x2203, 0x02 },  /* AK7739_C2_203_DIT_STATUS_BIT3                  */
  { 0x2204, 0x00 },  /* AK7739_C2_204_DIT_STATUS_BIT4                  */
  { 0x2210, 0x00 },  /* AK7739_C2_210_MIXER1_SETTING                   */
  { 0x2211, 0x00 },  /* AK7739_C2_211_MIXER2_SETTING                   */

  /* CODEC block */
  { 0x3000, 0x36 },  /* AK7739_C3_000_POWER_MANAGEMENT                 */
  { 0x3001, 0x20 },  /* AK7739_C3_001_MICBIAS_POWER_MANAGEMENT         */
  { 0x3002, 0x01 },  /* AK7739_C3_002_RESET_CONTROL                    */
  { 0x3003, 0x01 },  /* AK7739_C3_003_SYSTEM_CLOCK_SETTING             */
  { 0x3004, 0x11 },  /* AK7739_C3_004_MIC_AMP_GAIN                     */
  { 0x3005, 0x30 },  /* AK7739_C3_005_ADC1_LCH_DIGITAL_VOLUME          */
  { 0x3006, 0x30 },  /* AK7739_C3_006_ADC1_RCH_DIGITAL_VOLUME          */
  { 0x3007, 0x30 },  /* AK7739_C3_007_ADC2_LCH_DIGITAL_VOLUME          */
  { 0x3008, 0x30 },  /* AK7739_C3_008_ADC2_RCH_DIGITAL_VOLUME          */
  { 0x3009, 0x04 },  /* AK7739_C3_009_ADC_DIGITAL_FILTER_SETTING       */
  { 0x300A, 0x15 },  /* AK7739_C3_00A_ADC_ANALOG_INPUT_SETTING         */
  { 0x300B, 0x00 },  /* AK7739_C3_00B_ADC_MUTE&HPF_CONTROL             */
  { 0x300C, 0x17 },  /* AK7739_C3_00C_DAC1_LCH_DIGITAL_VOLUME          */
  { 0x300D, 0x1C },  /* AK7739_C3_00D_DAC1_RCH_DIGITAL_VOLUME          */
  { 0x300E, 0x65 },  /* AK7739_C3_00E_DAC2_LCH_DIGITAL_VOLUME          */
  { 0x300F, 0x63 },  /* AK7739_C3_00F_DAC2_RCH_DIGITAL_VOLUME          */
  { 0x3013, 0x15 },  /* AK7739_C3_013_DAC_De-EMPHASIS_SETTING          */
  { 0x3014, 0x02 },  /* AK7739_C3_014_DAC_MUTE&FILTER_SETTING          */
  { 0x3015, 0x00 },  /* AK7739_C3_015_DIGITAL_MIC_CONTROL              */

// VIRT Register for DSP Connection
  { 0x3016, 0x00 },  /* AK7739_VIRT_C3_016_DSP1OUT1_MIX              */
  { 0x3017, 0x00 },  /* AK7739_VIRT_C3_017_DSP1OUT2_MIX              */
  { 0x3018, 0x00 },  /* AK7739_VIRT_C3_018_DSP1OUT3_MIX              */
  { 0x3019, 0x00 },  /* AK7739_VIRT_C3_019_DSP1OUT4_MIX              */
  { 0x301A, 0x00 },  /* AK7739_VIRT_C3_01A_DSP1OUT5_MIX              */
  { 0x301B, 0x00 },  /* AK7739_VIRT_C3_01B_DSP1OUT6_MIX              */
  { 0x301C, 0x00 },  /* AK7739_VIRT_C3_01C_DSP2OUT1_MIX              */
  { 0x301D, 0x00 },  /* AK7739_VIRT_C3_01D_DSP2OUT2_MIX              */
  { 0x301E, 0x00 },  /* AK7739_VIRT_C3_01E_DSP2OUT3_MIX              */
  { 0x301F, 0x00 },  /* AK7739_VIRT_C3_01F_DSP2OUT4_MIX              */
  { 0x3020, 0x00 },  /* AK7739_VIRT_C3_020_DSP2OUT5_MIX              */
  { 0x3021, 0x00 },  /* AK7739_VIRT_C3_021_DSP2OUT6_MIX              */
};


/*-------------------Jazz------------------------*/
#define _AK77XX_DSP1_CRAM_BUF_SIZE	275
#define _AK77XX_DSP1_PRAM_BUF_SIZE	823

static unsigned char ak77dsp1PRAM_EQ[_AK77XX_DSP1_PRAM_BUF_SIZE] = {
	0xB8, 0x00, 0x00,		// command code and address
	0x0C, 0x94, 0xED, 0x1E, 0x74,
	0x04, 0xA9, 0x1C, 0x6D, 0x55,
	0x00, 0x44, 0x02, 0x11, 0x99,
	0x09, 0x58, 0x6F, 0x4D, 0xD0,
	0x01, 0x5A, 0x7E, 0xC9, 0x38,
	0x01, 0x35, 0xB3, 0x09, 0x8C,
	0x03, 0x4B, 0xFA, 0x24, 0x75,
	0x0E, 0xF8, 0x5B, 0xA8, 0x96,
	0x0D, 0x82, 0xD7, 0xDD, 0x00,
	0x01, 0x79, 0x77, 0x05, 0x9D,
	0x03, 0xAE, 0xA2, 0x45, 0x78,
	0x0E, 0x89, 0x21, 0xD8, 0x9C,
	0x00, 0xAB, 0x42, 0xE6, 0x41,
	0x00, 0xDA, 0xF8, 0xFB, 0x31,
	0x0B, 0x3A, 0x9E, 0xB8, 0x5B,
	0x02, 0x04, 0x2A, 0x09, 0xC2,
	0x01, 0xDD, 0xD7, 0x91, 0x42,
	0x04, 0xFD, 0x96, 0x5B, 0xF2,
	0x08, 0x66, 0x07, 0x1D, 0xC6,
	0x0E, 0xF4, 0x98, 0xEB, 0x6C,
	0x00, 0xF6, 0x80, 0xAD, 0xBB,
	0x05, 0xAF, 0xA9, 0x07, 0x4A,
	0x0B, 0xEE, 0xC7, 0x3A, 0x0F,
	0x0D, 0x44, 0x87, 0x76, 0x37,
	0x03, 0x43, 0xBC, 0xB9, 0xA0,
	0x07, 0xC5, 0xCD, 0x8E, 0x50,
	0x04, 0x79, 0xF4, 0xE2, 0xA4,
	0x01, 0xB5, 0x70, 0x37, 0x53,
	0x09, 0x0E, 0xD7, 0x13, 0x62,
	0x0C, 0xFF, 0xC3, 0x85, 0x69,
	0x0A, 0x6D, 0x73, 0x66, 0x64,
	0x0D, 0xCF, 0xD1, 0x8D, 0x2F,
	0x09, 0xF8, 0x10, 0xD9, 0xE3,
	0x0F, 0x4A, 0xBA, 0x76, 0x41,
	0x0E, 0x9C, 0xE6, 0x95, 0xAF,
	0x0D, 0x47, 0xB5, 0x9E, 0xBA,
	0x0B, 0x45, 0x26, 0xF7, 0x66,
	0x07, 0xB1, 0x9E, 0xC1, 0xEF,
	0x0A, 0xD1, 0xC0, 0x27, 0x68,
	0x02, 0x2E, 0x24, 0x48, 0xE3,
	0x0B, 0xAB, 0xA6, 0xA2, 0xA0,
	0x0D, 0xEE, 0xC9, 0xC3, 0x7A,
	0x0F, 0x0C, 0xCC, 0x53, 0xF6,
	0x08, 0xA6, 0x28, 0xAF, 0x92,
	0x0D, 0xAC, 0xFE, 0x59, 0x9B,
	0x02, 0xE5, 0x72, 0xC7, 0xDD,
	0x04, 0xFF, 0x2B, 0x9A, 0xBE,
	0x0A, 0xCC, 0xCA, 0xCB, 0xB8,
	0x0D, 0xA1, 0xF9, 0xF5, 0x12,
	0x08, 0x0A, 0x17, 0xCD, 0x0E,
	0x01, 0xB6, 0x47, 0x5D, 0xA7,
	0x03, 0xC0, 0xAD, 0xD1, 0xE7,
	0x0C, 0xC3, 0x57, 0x46, 0xD5,
	0x05, 0x0D, 0xA1, 0x27, 0x12,
	0x0F, 0x9C, 0x62, 0xF2, 0x96,
	0x0B, 0x5C, 0x66, 0xEC, 0x92,
	0x0D, 0xDA, 0x94, 0xBC, 0x18,
	0x0A, 0xFD, 0x5E, 0xA2, 0xC7,
	0x08, 0x2F, 0x4B, 0x3A, 0x0D,
	0x0C, 0x10, 0xCC, 0x7D, 0x54,
	0x0B, 0x1E, 0x53, 0xF2, 0x89,
	0x03, 0xB3, 0x09, 0x24, 0x33,
	0x04, 0xA4, 0x5C, 0x9D, 0xE1,
	0x0E, 0xCE, 0x5F, 0x28, 0x6E,
	0x03, 0x54, 0x47, 0xC9, 0xD1,
	0x04, 0x6A, 0x4C, 0xAB, 0x89,
	0x0A, 0xC9, 0x85, 0xBC, 0x9C,
	0x0B, 0xC4, 0x08, 0xB9, 0x14,
	0x0A, 0x07, 0x1D, 0xA5, 0xBF,
	0x07, 0x6E, 0x81, 0x31, 0x94,
	0x08, 0x36, 0x81, 0x0C, 0x02,
	0x0E, 0xD6, 0x97, 0x1A, 0xDB,
	0x01, 0x13, 0x3C, 0x28, 0x14,
	0x0E, 0xE7, 0x01, 0x75, 0xAE,
	0x04, 0x1D, 0xAC, 0x61, 0x41,
	0x0A, 0x7D, 0xFA, 0xC3, 0xFE,
	0x0D, 0x35, 0x9B, 0x44, 0x25,
	0x0B, 0x8E, 0x7E, 0x36, 0x2A,
	0x06, 0x53, 0x93, 0x93, 0x11,
	0x01, 0x4C, 0x80, 0x39, 0x56,
	0x0A, 0x9A, 0xB6, 0xAA, 0xE6,
	0x0E, 0x7B, 0xA8, 0x7E, 0x4D,
	0x0D, 0x89, 0xA5, 0xA8, 0xD2,
	0x0F, 0x42, 0xFC, 0x63, 0x94,
	0x0F, 0x21, 0xD1, 0xB1, 0x2A,
	0x04, 0xBC, 0x91, 0x29, 0x5E,
	0x0F, 0x89, 0x20, 0xC5, 0xEB,
	0x08, 0x59, 0x2F, 0x17, 0xB6,
	0x09, 0x3F, 0x43, 0x7C, 0x1E,
	0x09, 0x71, 0x71, 0x4A, 0x76,
	0x0E, 0x77, 0xA8, 0xD6, 0x3E,
	0x07, 0x67, 0xC5, 0x22, 0x01,
	0x09, 0x8B, 0xB4, 0x2C, 0x37,
	0x07, 0x2D, 0xB1, 0xAB, 0x35,
	0x05, 0x5F, 0x18, 0x9C, 0xD3,
	0x01, 0x8F, 0x90, 0xA5, 0xFD,
	0x04, 0x73, 0x0C, 0xB0, 0x7D,
	0x00, 0xA2, 0x8F, 0xC1, 0x6B,
	0x0B, 0xE8, 0x96, 0x3C, 0xBB,
	0x00, 0x06, 0x72, 0xD7, 0x51,
	0x00, 0xD5, 0xEB, 0xEE, 0x60,
	0x0E, 0xC6, 0x1B, 0x3D, 0xF1,
	0x02, 0x69, 0x52, 0x6D, 0x1E,
	0x0D, 0x11, 0x48, 0x1A, 0x67,
	0x0D, 0x85, 0xA0, 0x04, 0x5B,
	0x05, 0xAC, 0x99, 0x6F, 0x4D,
	0x0A, 0xE8, 0x9D, 0xF2, 0xC9,
	0x0D, 0x30, 0xD4, 0x33, 0x01,
	0x0D, 0xEB, 0x8F, 0x7A, 0x2C,
	0x04, 0x5E, 0x99, 0xFB, 0xA0,
	0x07, 0x94, 0x46, 0x55, 0x65,
	0x01, 0x6C, 0x9A, 0x77, 0x03,
	0x0C, 0x6E, 0x68, 0x22, 0x43,
	0x09, 0x97, 0x38, 0xA7, 0xD4,
	0x0E, 0x39, 0x9F, 0xE4, 0xAC,
	0x00, 0x60, 0x1B, 0x3C, 0xF9,
	0x03, 0x1B, 0xFF, 0x56, 0xB8,
	0x08, 0x41, 0xE5, 0x23, 0x09,
	0x0B, 0xF8, 0x1A, 0x1B, 0x91,
	0x03, 0x7C, 0x1C, 0x92, 0x5B,
	0x03, 0x23, 0xA2, 0x81, 0xA9,
	0x07, 0x5E, 0x17, 0x58, 0xEB,
	0x0D, 0xB8, 0x31, 0xC1, 0xAD,
	0x0A, 0xCC, 0x4E, 0xAC, 0x8B,
	0x0B, 0x3B, 0x0A, 0x01, 0x70,
	0x0E, 0x14, 0x85, 0x86, 0x70,
	0x0F, 0xFA, 0x84, 0x30, 0x3F,
	0x00, 0x3E, 0x24, 0xCC, 0x82,
	0x02, 0xFC, 0xBD, 0x73, 0x62,
	0x05, 0xB9, 0x54, 0x71, 0x31,
	0x01, 0x41, 0x82, 0x91, 0x25,
	0x00, 0x74, 0xD4, 0x22, 0x85,
	0x08, 0xB2, 0xE2, 0x37, 0xE4,
	0x05, 0x05, 0xE7, 0x34, 0x87,
	0x0E, 0x21, 0x55, 0xD0, 0x13,
	0x02, 0xA7, 0x42, 0x5B, 0x76,
	0x09, 0x16, 0x92, 0x8E, 0x95,
	0x05, 0x15, 0xEF, 0x74, 0x9E,
	0x0B, 0xC1, 0xC8, 0x61, 0x7B,
	0x07, 0x4F, 0x99, 0x7F, 0xC1,
	0x0E, 0xC2, 0x5D, 0x86, 0x17,
	0x09, 0x3A, 0x05, 0xC5, 0x48,
	0x02, 0x23, 0x24, 0xE2, 0x20,
	0x01, 0x45, 0xC6, 0x2C, 0xC9,
	0x0B, 0xA7, 0xA1, 0x0C, 0x99,
	0x07, 0x00, 0xAE, 0xC9, 0xA9,
	0x0A, 0x45, 0xA2, 0x96, 0x5F,
	0x01, 0xAA, 0x4D, 0xB3, 0xC7,
	0x0C, 0x4E, 0x72, 0x6C, 0x16,
	0x0F, 0x62, 0xE4, 0x2B, 0xCB,
	0x09, 0x55, 0x2D, 0xBF, 0xC5,
	0x03, 0xD0, 0x21, 0xF6, 0xCD,
	0x0F, 0xB9, 0x39, 0x03, 0xDF,
	0x06, 0xFB, 0xE8, 0x48, 0xDB,
	0x06, 0x04, 0x6E, 0x97, 0x8C,
	0x04, 0x8D, 0x05, 0x40, 0x21,
	0x08, 0x07, 0x95, 0x86, 0x84,
	0x0C, 0x43, 0xF5, 0xA7, 0xEC,
	0x0E, 0xE5, 0x93, 0x53, 0x26,
	0x08, 0xCA, 0x35, 0xB9, 0xA8,
	0x07, 0x59, 0x67, 0x09, 0xF0,
	0x0D, 0x6D, 0xD8, 0x29, 0x5D,
	0x04, 0x0A, 0x57, 0x93, 0x11,
	0x06, 0xD2, 0x7A, 0xEA, 0x24
};



static unsigned char ak77dsp1CRAM_JAZZ[_AK77XX_DSP1_CRAM_BUF_SIZE] = {
	0xB4, 0x00, 0x00,		// command code and address
	0x20, 0x00, 0x00, 0x00,
	0x7F, 0xFC, 0x96, 0x47,
	0x7F, 0xFC, 0x96, 0x47,
	0x3F, 0xBB, 0x4E, 0xA3,
	0x80, 0x36, 0x3E, 0x89,
	0xC0, 0x35, 0x26, 0x42,
	0x7F, 0xC9, 0xC1, 0x77,
	0x40, 0x0F, 0x8B, 0x1B,
	0x3F, 0x94, 0x5F, 0x5C,
	0x80, 0x62, 0x17, 0x0D,
	0xC0, 0x5F, 0x4A, 0x78,
	0x7F, 0x9D, 0xE8, 0xF3,
	0x40, 0x0C, 0x56, 0x2C,
	0x3F, 0x54, 0x1A, 0x83,
	0x80, 0x9F, 0x58, 0xA7,
	0xC0, 0x98, 0x31, 0x70,
	0x7F, 0x60, 0xA7, 0x59,
	0x40, 0x13, 0xB4, 0x0E,
	0x3E, 0xF4, 0x23, 0xC4,
	0x80, 0xFE, 0x93, 0x1F,
	0xC0, 0xED, 0x28, 0x43,
	0x7F, 0x01, 0x6C, 0xE1,
	0x40, 0x1E, 0xB3, 0xF9,
	0x3E, 0x55, 0x64, 0xDE,
	0x81, 0xA6, 0x18, 0xC7,
	0xC1, 0x79, 0xB4, 0xFF,
	0x7E, 0x59, 0xE7, 0x39,
	0x40, 0x30, 0xE6, 0x23,
	0x3D, 0x64, 0xF6, 0x85,
	0x82, 0xBB, 0xEE, 0xCA,
	0xC2, 0x4E, 0x94, 0x3E,
	0x7D, 0x44, 0x11, 0x36,
	0x40, 0x4C, 0x75, 0x3D,
	0x3B, 0xDF, 0xBE, 0xEC,
	0x84, 0x7C, 0x7B, 0x2A,
	0xC3, 0x6B, 0xA3, 0x35,
	0x7B, 0x83, 0x84, 0xD6,
	0x40, 0xB4, 0x9D, 0xDF,
	0x39, 0xAC, 0xF1, 0x53,
	0x88, 0xFB, 0xBC, 0x89,
	0xC6, 0x53, 0x0E, 0xAD,
	0x77, 0x04, 0x43, 0x77,
	0x40, 0x00, 0x00, 0x00,
	0x36, 0x56, 0x74, 0x5C,
	0x8E, 0xE4, 0xCA, 0x00,
	0xC8, 0x8E, 0x05, 0x05,
	0x71, 0x1B, 0x36, 0x00,
	0x41, 0x1B, 0x86, 0x9F,
	0x31, 0xB4, 0x18, 0x23,
	0x9D, 0x08, 0x00, 0x58,
	0xCD, 0xB8, 0x82, 0x3A,
	0x62, 0xF7, 0xFF, 0xA8,
	0x40, 0x93, 0x65, 0xA2,
	0x2B, 0x57, 0x66, 0xFD,
	0xB4, 0xBD, 0x18, 0xDD,
	0xD1, 0x20, 0x42, 0xF0,
	0x4B, 0x42, 0xE7, 0x23,
	0x43, 0x88, 0x56, 0x13,
	0x27, 0x1A, 0x45, 0xCA,
	0xE5, 0x50, 0xA8, 0xD9,
	0xD8, 0xE5, 0xBA, 0x36,
	0x1A, 0xAF, 0x57, 0x27,
	0x40, 0x00, 0x00, 0x00,
	0x29, 0x38, 0x26, 0xEB,
	0x34, 0x9C, 0x13, 0x75,
	0xD6, 0xC7, 0xD9, 0x15,
	0xCB, 0x63, 0xEC, 0x8B,
	0x40, 0x00, 0x00, 0x00
};

/*---------------------POP-------------------------*/
static unsigned char ak77dsp1CRAM_POP[_AK77XX_DSP1_CRAM_BUF_SIZE] = {
	0xB4, 0x00, 0x00,		// command code and address
	0x20, 0x00, 0x00, 0x00,
	0x7F, 0xFC, 0x96, 0x47,
	0x7F, 0xFC, 0x96, 0x47,
	0x3F, 0xBD, 0x1D, 0xED,
	0x80, 0x43, 0xFA, 0x3C,
	0xC0, 0x42, 0xE2, 0x13,
	0x7F, 0xBC, 0x05, 0xC4,
	0x40, 0x00, 0x00, 0x00,
	0x3F, 0x95, 0x1E, 0xAA,
	0x80, 0x6D, 0xAD, 0xAA,
	0xC0, 0x6A, 0xE1, 0x56,
	0x7F, 0x92, 0x52, 0x56,
	0x40, 0x00, 0x00, 0x00,
	0x3F, 0x55, 0x28, 0xD7,
	0x80, 0xAB, 0x1C, 0x5A,
	0xC0, 0xA3, 0xF5, 0xCC,
	0x7F, 0x54, 0xE3, 0xA6,
	0x40, 0x06, 0xE1, 0x5E,
	0x3E, 0xF5, 0xD6, 0xBB,
	0x81, 0x10, 0xD9, 0x80,
	0xC0, 0xFF, 0x71, 0x26,
	0x7E, 0xEF, 0x26, 0x80,
	0x40, 0x0A, 0xB8, 0x1F,
	0x3E, 0x4C, 0x60, 0x1A,
	0x81, 0x7D, 0x7F, 0xED,
	0xC1, 0x51, 0x0D, 0xE2,
	0x7E, 0x82, 0x80, 0x13,
	0x40, 0x62, 0x92, 0x03,
	0x3D, 0x5E, 0xC6, 0x69,
	0x82, 0x9B, 0x91, 0xBF,
	0xC2, 0x2E, 0x1A, 0xF3,
	0x7D, 0x64, 0x6E, 0x41,
	0x40, 0x73, 0x1E, 0xA4,
	0x3B, 0xDF, 0xBE, 0xEC,
	0x84, 0x7C, 0x7B, 0x2A,
	0xC3, 0x6B, 0xA3, 0x35,
	0x7B, 0x83, 0x84, 0xD6,
	0x40, 0xB4, 0x9D, 0xDF,
	0x39, 0x87, 0xC2, 0xB5,
	0x88, 0x0B, 0x15, 0x15,
	0xC5, 0x5D, 0x06, 0xE1,
	0x77, 0xF4, 0xEA, 0xEB,
	0x41, 0x1B, 0x36, 0x6B,
	0x36, 0x71, 0x77, 0xD6,
	0x8F, 0x7A, 0x63, 0x12,
	0xC9, 0x2C, 0x00, 0x6A,
	0x70, 0x85, 0x9C, 0xEE,
	0x40, 0x62, 0x87, 0xBF,
	0x31, 0x82, 0x20, 0xB0,
	0x9C, 0x42, 0xA8, 0x17,
	0xCC, 0xD4, 0xA2, 0x77,
	0x63, 0xBD, 0x57, 0xE9,
	0x41, 0xA9, 0x3C, 0xD9,
	0x2B, 0xAB, 0xFB, 0x3A,
	0xB5, 0x54, 0xA4, 0xA6,
	0xD1, 0xFF, 0x84, 0x5C,
	0x4A, 0xAB, 0x5B, 0x5A,
	0x42, 0x54, 0x80, 0x6A,
	0x26, 0xE3, 0xB7, 0x0C,
	0xE5, 0x1B, 0xC6, 0x50,
	0xD8, 0x19, 0x65, 0x53,
	0x1A, 0xE4, 0x39, 0xB0,
	0x41, 0x02, 0xE3, 0xA1,
	0x28, 0x9D, 0x98, 0x7D,
	0x35, 0xA5, 0xE3, 0x71,
	0xD4, 0xB4, 0x39, 0x1E,
	0xCA, 0x5A, 0x1C, 0x8F,
	0x42, 0xAE, 0x2E, 0x65
};


static unsigned char ak77dsp1CRAM_Rock[_AK77XX_DSP1_CRAM_BUF_SIZE] = {
	0xB4, 0x00, 0x00,		// command code and address
	0x20, 0x00, 0x00, 0x00,
	0x7F, 0xFC, 0x96, 0x47,
	0x7F, 0xFC, 0x96, 0x47,
	0x3F, 0xBC, 0xA8, 0x80,
	0x80, 0x3C, 0xB7, 0xAD,
	0xC0, 0x3B, 0x9F, 0x74,
	0x7F, 0xC3, 0x48, 0x53,
	0x40, 0x07, 0xB8, 0x0C,
	0x3F, 0x95, 0x04, 0xEC,
	0x80, 0x69, 0x78, 0x87,
	0xC0, 0x66, 0xAC, 0x1B,
	0x7F, 0x96, 0x87, 0x79,
	0x40, 0x04, 0x4E, 0xF9,
	0x3F, 0x50, 0x99, 0xDE,
	0x80, 0x8E, 0xDE, 0x1B,
	0xC0, 0x87, 0xB5, 0xF7,
	0x7F, 0x71, 0x21, 0xE5,
	0x40, 0x27, 0xB0, 0x2B,
	0x3E, 0xF5, 0xD6, 0xBB,
	0x81, 0x10, 0xD9, 0x80,
	0xC0, 0xFF, 0x71, 0x26,
	0x7E, 0xEF, 0x26, 0x80,
	0x40, 0x0A, 0xB8, 0x1F,
	0x3E, 0x4C, 0x60, 0x1A,
	0x81, 0x7D, 0x7F, 0xED,
	0xC1, 0x51, 0x0D, 0xE2,
	0x7E, 0x82, 0x80, 0x13,
	0x40, 0x62, 0x92, 0x03,
	0x3D, 0x5E, 0xC6, 0x69,
	0x82, 0x9B, 0x91, 0xBF,
	0xC2, 0x2E, 0x1A, 0xF3,
	0x7D, 0x64, 0x6E, 0x41,
	0x40, 0x73, 0x1E, 0xA4,
	0x3B, 0xC6, 0x49, 0x3A,
	0x84, 0x2D, 0x83, 0xD6,
	0xC3, 0x1B, 0xFD, 0x71,
	0x7B, 0xD2, 0x7C, 0x2A,
	0x41, 0x1D, 0xB9, 0x55,
	0x39, 0xA8, 0x75, 0xBB,
	0x88, 0xC0, 0x2A, 0x21,
	0xC6, 0x16, 0x27, 0x92,
	0x77, 0x3F, 0xD5, 0xDF,
	0x40, 0x41, 0x62, 0xB4,
	0x36, 0x12, 0xDC, 0x12,
	0x8E, 0x10, 0xCC, 0x30,
	0xC7, 0xAE, 0x25, 0xB4,
	0x71, 0xEF, 0x33, 0xD0,
	0x42, 0x3E, 0xFE, 0x3A,
	0x31, 0xC7, 0x1C, 0x72,
	0x9D, 0x77, 0x2E, 0x8D,
	0xCE, 0x38, 0xE3, 0x8E,
	0x62, 0x88, 0xD1, 0x73,
	0x40, 0x00, 0x00, 0x00,
	0x29, 0x79, 0x60, 0x61,
	0xB2, 0xB4, 0xF1, 0x41,
	0xCE, 0x21, 0xFA, 0x04,
	0x4D, 0x4B, 0x0E, 0xBF,
	0x48, 0x64, 0xA5, 0x9B,
	0x27, 0x1A, 0x45, 0xCA,
	0xE5, 0x50, 0xA8, 0xD9,
	0xD8, 0xE5, 0xBA, 0x36,
	0x1A, 0xAF, 0x57, 0x27,
	0x40, 0x00, 0x00, 0x00,
	0x28, 0x9D, 0x98, 0x7D,
	0x35, 0xA5, 0xE3, 0x71,
	0xD4, 0xB4, 0x39, 0x1E,
	0xCA, 0x5A, 0x1C, 0x8F,
	0x42, 0xAE, 0x2E, 0x65
};


static unsigned char ak77dsp1CRAM_Power[_AK77XX_DSP1_CRAM_BUF_SIZE] = {
	0xB4, 0x00, 0x00,		// command code and address
	0x20, 0x00, 0x00, 0x00,
	0x7F, 0xFC, 0x96, 0x47,
	0x7F, 0xFC, 0x96, 0x47,
	0x3F, 0xBC, 0xA8, 0x80,
	0x80, 0x3C, 0xB7, 0xAD,
	0xC0, 0x3B, 0x9F, 0x74,
	0x7F, 0xC3, 0x48, 0x53,
	0x40, 0x07, 0xB8, 0x0C,
	0x3F, 0x8E, 0x92, 0xB6,
	0x80, 0x4E, 0x89, 0xD3,
	0xC0, 0x4B, 0xBC, 0xD0,
	0x7F, 0xB1, 0x76, 0x2D,
	0x40, 0x25, 0xB0, 0x7A,
	0x3F, 0x50, 0x99, 0xDE,
	0x80, 0x8E, 0xDE, 0x1B,
	0xC0, 0x87, 0xB5, 0xF7,
	0x7F, 0x71, 0x21, 0xE5,
	0x40, 0x27, 0xB0, 0x2B,
	0x3E, 0xF4, 0x23, 0xC4,
	0x80, 0xFE, 0x93, 0x1F,
	0xC0, 0xED, 0x28, 0x43,
	0x7F, 0x01, 0x6C, 0xE1,
	0x40, 0x1E, 0xB3, 0xF9,
	0x3E, 0x58, 0xCD, 0x0D,
	0x81, 0xD3, 0x86, 0xC5,
	0xC1, 0xA7, 0x32, 0xF3,
	0x7E, 0x2C, 0x79, 0x3B,
	0x40, 0x00, 0x00, 0x00,
	0x3D, 0x68, 0x0F, 0xB2,
	0x83, 0x50, 0xE3, 0x23,
	0xC2, 0xE4, 0x0A, 0xA0,
	0x7C, 0xAF, 0x1C, 0xDD,
	0x3F, 0xB3, 0xE5, 0xAD,
	0x3B, 0xF4, 0xBD, 0xBC,
	0x85, 0x55, 0xC1, 0x92,
	0xC4, 0x46, 0xC9, 0x94,
	0x7A, 0xAA, 0x3E, 0x6E,
	0x3F, 0xC4, 0x78, 0xB1,
	0x39, 0xA5, 0xC3, 0xBC,
	0x88, 0xA7, 0x4A, 0x6E,
	0xC5, 0xFC, 0xB9, 0x9C,
	0x77, 0x58, 0xB5, 0x92,
	0x40, 0x5D, 0x82, 0xA8,
	0x36, 0x56, 0x74, 0x5C,
	0x8E, 0xE4, 0xCA, 0x00,
	0xC8, 0x8E, 0x05, 0x05,
	0x71, 0x1B, 0x36, 0x00,
	0x41, 0x1B, 0x86, 0x9F,
	0x31, 0x82, 0x20, 0xB0,
	0x9C, 0x42, 0xA8, 0x17,
	0xCC, 0xD4, 0xA2, 0x77,
	0x63, 0xBD, 0x57, 0xE9,
	0x41, 0xA9, 0x3C, 0xD9,
	0x2A, 0xF2, 0x36, 0x0E,
	0xB4, 0x2B, 0xCE, 0x89,
	0xD0, 0x4A, 0x38, 0xC2,
	0x4B, 0xD4, 0x31, 0x77,
	0x44, 0xC3, 0x91, 0x30,
	0x25, 0x5F, 0xE1, 0x0C,
	0xE4, 0x33, 0xFB, 0x0A,
	0xD4, 0x99, 0xCF, 0xFA,
	0x1B, 0xCC, 0x04, 0xF6,
	0x46, 0x06, 0x4E, 0xF9,
	0x28, 0xF3, 0xBE, 0xE7,
	0x35, 0x23, 0x82, 0x2D,
	0xD5, 0xB8, 0xFB, 0xA7,
	0xCA, 0xDC, 0x7D, 0xD3,
	0x41, 0x53, 0x45, 0x72
};

static unsigned char ak77dsp1CRAM_Vocal[_AK77XX_DSP1_CRAM_BUF_SIZE] = {
	0xB4, 0x00, 0x00,		// command code and address
	0x20, 0x00, 0x00, 0x00,
	0x7F, 0xFC, 0x96, 0x47,
	0x7F, 0xFC, 0x96, 0x47,
	0x3F, 0xBB, 0x5F, 0x4E,
	0x80, 0x55, 0x40, 0x0A,
	0xC0, 0x54, 0x28, 0x07,
	0x7F, 0xAA, 0xBF, 0xF6,
	0x3F, 0xF0, 0x78, 0xAB,
	0x3F, 0x94, 0x74, 0x17,
	0x80, 0x7A, 0xAB, 0xC0,
	0xC0, 0x77, 0xDF, 0xB5,
	0x7F, 0x85, 0x54, 0x40,
	0x3F, 0xF3, 0xAC, 0x34,
	0x3F, 0x55, 0x55, 0x4F,
	0x80, 0xB1, 0xD0, 0xDE,
	0xC0, 0xAA, 0xAA, 0xB1,
	0x7F, 0x4E, 0x2F, 0x22,
	0x40, 0x00, 0x00, 0x00,
	0x3E, 0xEE, 0x9A, 0xCF,
	0x80, 0xE4, 0xF6, 0xDA,
	0xC0, 0xD3, 0x88, 0x7B,
	0x7F, 0x1B, 0x09, 0x26,
	0x40, 0x3D, 0xDC, 0xB5,
	0x3E, 0x45, 0xB8, 0xBF,
	0x81, 0x6A, 0xDA, 0x9F,
	0xC1, 0x3E, 0x62, 0x06,
	0x7E, 0x95, 0x25, 0x61,
	0x40, 0x7B, 0xE5, 0x3B,
	0x3D, 0x4B, 0xBE, 0x45,
	0x82, 0x60, 0x00, 0x87,
	0xC1, 0xF2, 0x55, 0xBA,
	0x7D, 0x9F, 0xFF, 0x79,
	0x40, 0xC1, 0xEC, 0x01,
	0x3B, 0xF4, 0x84, 0xCF,
	0x85, 0x1A, 0xF5, 0x12,
	0xC4, 0x0B, 0x7B, 0x31,
	0x7A, 0xE5, 0x0A, 0xEE,
	0x40, 0x00, 0x00, 0x00,
	0x39, 0xAC, 0xF1, 0x53,
	0x88, 0xFB, 0xBC, 0x89,
	0xC6, 0x53, 0x0E, 0xAD,
	0x77, 0x04, 0x43, 0x77,
	0x40, 0x00, 0x00, 0x00,
	0x36, 0x56, 0x74, 0x5C,
	0x8E, 0xE4, 0xCA, 0x00,
	0xC8, 0x8E, 0x05, 0x05,
	0x71, 0x1B, 0x36, 0x00,
	0x41, 0x1B, 0x86, 0x9F,
	0x31, 0x0E, 0xBF, 0xE5,
	0x9B, 0x29, 0x40, 0x9F,
	0xCB, 0x8F, 0xB2, 0x74,
	0x64, 0xD6, 0xBF, 0x61,
	0x43, 0x61, 0x8D, 0xA7,
	0x2A, 0xF2, 0x36, 0x0E,
	0xB4, 0x2B, 0xCE, 0x89,
	0xD0, 0x4A, 0x38, 0xC2,
	0x4B, 0xD4, 0x31, 0x77,
	0x44, 0xC3, 0x91, 0x30,
	0x25, 0xED, 0x11, 0xE3,
	0xE4, 0x77, 0x33, 0xA3,
	0xD5, 0x9D, 0x88, 0xE4,
	0x1B, 0x88, 0xCC, 0x5D,
	0x44, 0x75, 0x65, 0x3A,
	0x28, 0xF3, 0xBE, 0xE7,
	0x35, 0x23, 0x82, 0x2D,
	0xD5, 0xB8, 0xFB, 0xA7,
	0xCA, 0xDC, 0x7D, 0xD3,
	0x41, 0x53, 0x45, 0x72
};

static unsigned char ak77dsp1CRAM_Standard[] = {
	0xB4, 0x00, 0x00,		// command code and address
	0x20, 0x00, 0x00, 0x00,
	0x7F, 0xFC, 0x96, 0x47,
	0x7F, 0xFC, 0x96, 0x47,
	0x3F, 0xBD, 0x1D, 0xED,
	0x80, 0x43, 0xFA, 0x3C,
	0xC0, 0x42, 0xE2, 0x13,
	0x7F, 0xBC, 0x05, 0xC4,
	0x40, 0x00, 0x00, 0x00,
	0x3F, 0x95, 0x1E, 0xAA,
	0x80, 0x6D, 0xAD, 0xAA,
	0xC0, 0x6A, 0xE1, 0x56,
	0x7F, 0x92, 0x52, 0x56,
	0x40, 0x00, 0x00, 0x00,
	0x3F, 0x55, 0x55, 0x4F,
	0x80, 0xB1, 0xD0, 0xDE,
	0xC0, 0xAA, 0xAA, 0xB1,
	0x7F, 0x4E, 0x2F, 0x22,
	0x40, 0x00, 0x00, 0x00,
	0x3E, 0xF6, 0x23, 0xD4,
	0x81, 0x1B, 0x43, 0x19,
	0xC1, 0x09, 0xDC, 0x2C,
	0x7E, 0xE4, 0xBC, 0xE7,
	0x40, 0x00, 0x00, 0x00,
	0x3E, 0x58, 0xCD, 0x0D,
	0x81, 0xD3, 0x86, 0xC5,
	0xC1, 0xA7, 0x32, 0xF3,
	0x7E, 0x2C, 0x79, 0x3B,
	0x40, 0x00, 0x00, 0x00,
	0x3D, 0x6A, 0xD0, 0x3D,
	0x83, 0x02, 0x4C, 0xE1,
	0xC2, 0x95, 0x2F, 0xC3,
	0x7C, 0xFD, 0xB3, 0x1F,
	0x40, 0x00, 0x00, 0x00,
	0x3B, 0xF4, 0x84, 0xCF,
	0x85, 0x1A, 0xF5, 0x12,
	0xC4, 0x0B, 0x7B, 0x31,
	0x7A, 0xE5, 0x0A, 0xEE,
	0x40, 0x00, 0x00, 0x00,
	0x39, 0xAC, 0xF1, 0x53,
	0x88, 0xFB, 0xBC, 0x89,
	0xC6, 0x53, 0x0E, 0xAD,
	0x77, 0x04, 0x43, 0x77,
	0x40, 0x00, 0x00, 0x00,
	0x36, 0x7A, 0xA2, 0x60,
	0x8F, 0xCF, 0x02, 0x30,
	0xC9, 0x85, 0x5D, 0xA0,
	0x70, 0x30, 0xFD, 0xD0,
	0x40, 0x00, 0x00, 0x00,
	0x31, 0xC7, 0x1C, 0x72,
	0x9D, 0x77, 0x2E, 0x8D,
	0xCE, 0x38, 0xE3, 0x8E,
	0x62, 0x88, 0xD1, 0x73,
	0x40, 0x00, 0x00, 0x00,
	0x2C, 0x25, 0x7B, 0x5C,
	0xB6, 0x97, 0x13, 0x03,
	0xD3, 0xDA, 0x84, 0xA4,
	0x49, 0x68, 0xEC, 0xFD,
	0x40, 0x00, 0x00, 0x00,
	0x27, 0x1A, 0x45, 0xCA,
	0xE5, 0x50, 0xA8, 0xD9,
	0xD8, 0xE5, 0xBA, 0x36,
	0x1A, 0xAF, 0x57, 0x27,
	0x40, 0x00, 0x00, 0x00,
	0x29, 0x38, 0x26, 0xEB,
	0x34, 0x9C, 0x13, 0x75,
	0xD6, 0xC7, 0xD9, 0x15,
	0xCB, 0x63, 0xEC, 0x8B,
	0x40, 0x00, 0x00, 0x00
};


static unsigned char ak77dsp2CRAM_LowPass[] = {
	0xB5, 0x00, 0x00,		// command code and address
	0x20, 0x00, 0x00, 0x00,
	0x7F, 0xFC, 0x96, 0x47,
	0x7F, 0xFC, 0x96, 0x47,
	0x00, 0x00, 0xB3, 0x15,
	0x00, 0x01, 0x66, 0x2A,
	0xC0, 0x6A, 0xE1, 0x56,
	0x7F, 0x92, 0x52, 0x56,
	0x00, 0x00, 0xB3, 0x15
};

static unsigned char ak77dsp2PRAM_LowPass[] = {
	0xB9, 0x00, 0x00,		// command code and address
	0x0C, 0x94, 0xED, 0x1E, 0x74,
	0x04, 0xA9, 0x1C, 0x6D, 0x55,
	0x00, 0x44, 0x02, 0x11, 0x99,
	0x09, 0x58, 0x6F, 0x4D, 0xD0,
	0x01, 0x5A, 0x7E, 0xC9, 0x38,
	0x01, 0x35, 0xB3, 0x09, 0x8C,
	0x03, 0x4B, 0xFA, 0x24, 0x75,
	0x0E, 0xF8, 0x5B, 0xA8, 0x96,
	0x0D, 0x82, 0xD7, 0xDD, 0x00,
	0x01, 0x79, 0x77, 0x05, 0x9D,
	0x03, 0xAE, 0xA2, 0x45, 0x78,
	0x0E, 0x89, 0x21, 0xD8, 0x9C,
	0x00, 0xAB, 0x42, 0xE6, 0x41,
	0x00, 0xDA, 0xF8, 0xFB, 0x31,
	0x0B, 0x3A, 0x9E, 0xB8, 0x5B,
	0x02, 0x04, 0x2A, 0x09, 0xC2,
	0x01, 0xDD, 0xD7, 0x91, 0x42,
	0x04, 0xFD, 0x96, 0x5B, 0xF2,
	0x08, 0x66, 0x07, 0x1D, 0xC6,
	0x0E, 0xF4, 0x98, 0xEB, 0x6C,
	0x00, 0xF6, 0x80, 0xAD, 0xBB,
	0x05, 0xAF, 0xA9, 0x07, 0x4A,
	0x0B, 0xEE, 0xC7, 0x3A, 0x0F,
	0x0D, 0x44, 0x87, 0x76, 0x37,
	0x03, 0x43, 0xBC, 0xB9, 0xA0,
	0x07, 0xC5, 0xCD, 0x82, 0x53,
	0x04, 0x79, 0xF4, 0xE2, 0xA4,
	0x01, 0xB5, 0x70, 0x37, 0x53,
	0x09, 0x0E, 0xD7, 0x13, 0x62,
	0x0C, 0xFF, 0xC3, 0x85, 0x69,
	0x0A, 0x6D, 0x73, 0x64, 0xD4,
	0x0D, 0xCF, 0xD1, 0x8D, 0x2F,
	0x09, 0xF8, 0x10, 0xD9, 0xE3,
	0x0F, 0x4A, 0xBA, 0x76, 0x41,
	0x0E, 0x9C, 0xE6, 0x95, 0xAF,
	0x0D, 0x47, 0xB5, 0x9E, 0xBA,
	0x09, 0x45, 0x21, 0x7B, 0x66,
	0x07, 0xB1, 0x9F, 0xC1, 0xEF,
	0x0B, 0x99, 0x04, 0xA7, 0x68,
	0x03, 0xE6, 0xC4, 0x4E, 0xE9,
	0x0A, 0xE2, 0x62, 0xA6, 0x5A,
	0x0C, 0x27, 0x2A, 0xC7, 0x5A,
	0x0E, 0x45, 0x0A, 0xD7, 0x97,
	0x06, 0x6F, 0xC9, 0xA9, 0x98
};

static unsigned char ak77dsp2CRAM_bypass[] = {
	0xB5, 0x00, 0x00,		// command code and address

};

static unsigned char ak77dsp2PRAM_bypass[] = {
	0xB9, 0x00, 0x00,		// command code and address
	0x0C, 0x94, 0xED, 0x1E, 0x74,
	0x04, 0xA9, 0x1C, 0x6D, 0x55,
	0x00, 0x44, 0x02, 0x11, 0x99,
	0x09, 0x58, 0x6F, 0x4D, 0xD0,
	0x01, 0x5A, 0x7E, 0xC9, 0x38,
	0x01, 0x35, 0xB3, 0x09, 0x8C,
	0x03, 0x4B, 0xFA, 0x24, 0x75,
	0x06, 0x78, 0xFB, 0xA4, 0xF6,
	0x05, 0x82, 0x57, 0xDD, 0x00,
	0x05, 0x79, 0x77, 0x0B, 0xDD,
	0x07, 0xAE, 0xA2, 0x47, 0x19,
	0x01, 0xD9, 0xA1, 0xDE, 0x1F
};

#endif
