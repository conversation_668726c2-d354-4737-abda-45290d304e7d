#ifndef _AK77XX_DSP1_ADDR_INCLUDED
#define _AK77XX_DSP1_ADDR_INCLUDED

// Component: Fader_LR01
#define 	DSP1_CRAM_ADDR_LEVEL_Fader_LR01	0x0000
#define 	DSP1_CRAM_ADDR_ATT_TIME_Fader_LR01	0x0001
#define 	DSP1_CRAM_ADDR_REL_TIME_Fader_LR01	0x0002

// Component: GEQ_LR_01
#define 	DSP1_CRAM_ADDR_BAND1_GEQ_LR_01	0x0003
#define 	DSP1_CRAM_ADDR_BAND2_GEQ_LR_01	0x0008
#define 	DSP1_CRAM_ADDR_BAND3_GEQ_LR_01	0x000D
#define 	DSP1_CRAM_ADDR_BAND4_GEQ_LR_01	0x0012
#define 	DSP1_CRAM_ADDR_BAND5_GEQ_LR_01	0x0017
#define 	DSP1_CRAM_ADDR_BAND6_GEQ_LR_01	0x001C
#define 	DSP1_CRAM_ADDR_BAND7_GEQ_LR_01	0x0021
#define 	DSP1_CRAM_ADDR_BAND8_GEQ_LR_01	0x0026
#define 	DSP1_CRAM_ADDR_BAND9_GEQ_LR_01	0x002B
#define 	DSP1_CRAM_ADDR_BAND10_GEQ_LR_01	0x0030
#define 	DSP1_CRAM_ADDR_BAND11_GEQ_LR_01	0x0035
#define 	DSP1_CRAM_ADDR_BAND12_GEQ_LR_01	0x003A
#define 	DSP1_CRAM_ADDR_BAND13_GEQ_LR_01	0x003F


/*
FREQ  INDEX
62.5   100   160  250  400  630  1000   1600   2500  4000  6300   10000  16000

GAIN -12 ~ 12  index 0 - 24
*/

static int gain_coefficient[13][25][5] = {
    {
        {  0x3facaf40, 0x80864580, 0xc0852dc0, 0x7f79ba80, 0x3fce2300 },
        {  0x3faf6640, 0x807ed980, 0xc07dc200, 0x7f812680, 0x3fd2d7c0 },
        {  0x3fb1d9c0, 0x8077d780, 0xc076bf80, 0x7f882880, 0x3fd766c0 },
        {  0x3fb40b40, 0x80713880, 0xc07020c0, 0x7f8ec780, 0x3fdbd400 },
        {  0x3fb5fcc0, 0x806af800, 0xc069e040, 0x7f950800, 0x3fe02300 },
        {  0x3fb7afc0, 0x80651080, 0xc063f8c0, 0x7f9aef80, 0x3fe45780 },
        {  0x3fb925c0, 0x805f7d00, 0xc05e6540, 0x7fa08300, 0x3fe87500 },
        {  0x3fba6000, 0x805a3900, 0xc0592100, 0x7fa5c700, 0x3fec7f00 },
        {  0x3fbb5f40, 0x80554000, 0xc0542800, 0x7faac000, 0x3ff078c0 },
        {  0x3fbc24c0, 0x80508e00, 0xc04f75c0, 0x7faf7200, 0x3ff46580 },
        {  0x3fbcb080, 0x804c1e80, 0xc04b0680, 0x7fb3e180, 0x3ff84900 },
        {  0x3fbd03c0, 0x8047ee80, 0xc046d680, 0x7fb81180, 0x3ffc2600 },
        {  0x3fbd1e00, 0x8043fa00, 0xc042e200, 0x7fbc0600, 0x40000000 },
        {  0x3fbcff80, 0x80403e80, 0xc03f2600, 0x7fbfc180, 0x4003da80 },
        {  0x3fbca880, 0x803cb780, 0xc03b9f80, 0x7fc34880, 0x4007b800 },
        {  0x3fbc1840, 0x80396380, 0xc0384b00, 0x7fc69c80, 0x400b9c80 },
        {  0x3fbb4ec0, 0x80363e80, 0xc0352640, 0x7fc9c180, 0x400f8b00 },
        {  0x3fba4ac0, 0x80334680, 0xc0322e40, 0x7fccb980, 0x40138700 },
        {  0x3fb90bc0, 0x80307900, 0xc02f60c0, 0x7fcf8700, 0x40179380 },
        {  0x3fb79080, 0x802dd380, 0xc02cbb40, 0x7fd22c80, 0x401bb480 },
        {  0x3fb5d7c0, 0x802b5400, 0xc02a3b80, 0x7fd4ac00, 0x401fed00 },
        {  0x3fb3e040, 0x8028f800, 0xc027df80, 0x7fd70800, 0x40244080 },
        {  0x3fb1a800, 0x8026bd80, 0xc025a500, 0x7fd94280, 0x4028b300 },
        {  0x3faf2d40, 0x8024a300, 0xc0238a80, 0x7fdb5d00, 0x402d4800 },
        {  0x3fac6e00, 0x8022a680, 0xc0218e00, 0x7fdd5980, 0x40320400 },
    },
    {
        {  0x3f7b0580, 0x80d75a80, 0xc0d49080, 0x7f28a580, 0x3fb06a40 },
        {  0x3f7f5680, 0x80cb8980, 0xc0c8bf00, 0x7f347680, 0x3fb7ea80 },
        {  0x3f833bc0, 0x80c05f80, 0xc0bd9500, 0x7f3fa080, 0x3fbf2f40 },
        {  0x3f86b880, 0x80b5d380, 0xc0b308c0, 0x7f4a2c80, 0x3fc63ec0 },
        {  0x3f89cf80, 0x80abdd00, 0xc0a91240, 0x7f542300, 0x3fcd1e40 },
        {  0x3f8c8300, 0x80a27400, 0xc09fa8c0, 0x7f5d8c00, 0x3fd3d440 },
        {  0x3f8ed540, 0x80999080, 0xc096c500, 0x7f666f80, 0x3fda65c0 },
        {  0x3f90c880, 0x80912b00, 0xc08e5f40, 0x7f6ed500, 0x3fe0d840 },
        {  0x3f925dc0, 0x80893c80, 0xc08670c0, 0x7f76c380, 0x3fe73140 },
        {  0x3f9396c0, 0x8081bf00, 0xc07ef300, 0x7f7e4100, 0x3fed7640 },
        {  0x3f947400, 0x807aab80, 0xc077dfc0, 0x7f855480, 0x3ff3ac40 },
        {  0x3f94f680, 0x8073fd00, 0xc0713100, 0x7f8c0300, 0x3ff9d880 },
        {  0x3f951ec0, 0x806dad80, 0xc06ae140, 0x7f925280, 0x40000000 },
        {  0x3f94ec40, 0x8067b800, 0xc064eb80, 0x7f984800, 0x40062800 },
        {  0x3f945f40, 0x80621700, 0xc05f4a80, 0x7f9de900, 0x400c5600 },
        {  0x3f937740, 0x805cc680, 0xc059f980, 0x7fa33980, 0x40128f00 },
        {  0x3f923340, 0x8057c180, 0xc054f480, 0x7fa83e80, 0x4018d880 },
        {  0x3f909240, 0x80530400, 0xc05036c0, 0x7facfc00, 0x401f3700 },
        {  0x3f8e92c0, 0x804e8a00, 0xc04bbcc0, 0x7fb17600, 0x4025b080 },
        {  0x3f8c3300, 0x804a4f80, 0xc0478280, 0x7fb5b080, 0x402c4a80 },
        {  0x3f897140, 0x80465200, 0xc04384c0, 0x7fb9ae00, 0x40330a00 },
        {  0x3f864ac0, 0x80428d00, 0xc03fbfc0, 0x7fbd7300, 0x4039f580 },
        {  0x3f82bd00, 0x803efe00, 0xc03c3080, 0x7fc10200, 0x40411280 },
        {  0x3f7ec500, 0x803ba180, 0xc038d400, 0x7fc45e80, 0x40486700 },
        {  0x3f7a5f40, 0x80387500, 0xc035a780, 0x7fc78b00, 0x404ff980 },
    },
    {
        {  0x3f2c1180, 0x8159e180, 0xc152c4c0, 0x7ea61e80, 0x3f8129c0 },
        {  0x3f32e740, 0x81471e80, 0xc1400100, 0x7eb8e180, 0x3f8d17c0 },
        {  0x3f3912c0, 0x81356380, 0xc12e4480, 0x7eca9c80, 0x3f98a8c0 },
        {  0x3f3e9880, 0x8124a180, 0xc11d81c0, 0x7edb5e80, 0x3fa3e5c0 },
        {  0x3f437d00, 0x8114cb80, 0xc10daac0, 0x7eeb3480, 0x3faed840 },
        {  0x3f47c400, 0x8105d480, 0xc0feb300, 0x7efa2b80, 0x3fb98900 },
        {  0x3f4b70c0, 0x80f7b080, 0xc0f08e80, 0x7f084f80, 0x3fc40100 },
        {  0x3f4e8600, 0x80ea5480, 0xc0e33180, 0x7f15ab80, 0x3fce4880 },
        {  0x3f510640, 0x80ddb500, 0xc0d69140, 0x7f224b00, 0x3fd86880 },
        {  0x3f52f3c0, 0x80d1c800, 0xc0caa380, 0x7f2e3800, 0x3fe268c0 },
        {  0x3f544f40, 0x80c68380, 0xc0bf5e80, 0x7f397c80, 0x3fec5200 },
        {  0x3f551a80, 0x80bbdf00, 0xc0b4b940, 0x7f442100, 0x3ff62c40 },
        {  0x3f555540, 0x80b1d100, 0xc0aaaac0, 0x7f4e2f00, 0x40000000 },
        {  0x3f550040, 0x80a85180, 0xc0a12ac0, 0x7f57ae80, 0x4009d500 },
        {  0x3f541a80, 0x809f5880, 0xc0983180, 0x7f60a780, 0x4013b400 },
        {  0x3f52a380, 0x8096df00, 0xc08fb780, 0x7f692100, 0x401da500 },
        {  0x3f5099c0, 0x808ede00, 0xc087b600, 0x7f712200, 0x4027b000 },
        {  0x3f4dfbc0, 0x80874e80, 0xc0802640, 0x7f78b180, 0x4031de00 },
        {  0x3f4ac6c0, 0x80802a80, 0xc07901c0, 0x7f7fd580, 0x403c3780 },
        {  0x3f46f840, 0x80796c80, 0xc0724300, 0x7f869380, 0x4046c500 },
        {  0x3f428cc0, 0x80730e00, 0xc06be400, 0x7f8cf200, 0x40518f00 },
        {  0x3f3d80c0, 0x806d0a00, 0xc065dfc0, 0x7f92f600, 0x405c9f80 },
        {  0x3f37cf80, 0x80675b80, 0xc0603140, 0x7f98a480, 0x4067ff80 },
        {  0x3f317480, 0x8061fe00, 0xc05ad380, 0x7f9e0200, 0x4073b800 },
        {  0x3f2a6a40, 0x805ced00, 0xc055c240, 0x7fa31300, 0x407fd380 },
    },
    {
        {  0x3eb6ce00, 0x821f7980, 0xc20e3600, 0x7de08680, 0x3f3afbc0 },
        {  0x3ec15200, 0x82028000, 0xc1f13900, 0x7dfd8000, 0x3f4d7500 },
        {  0x3ecad040, 0x81e71980, 0xc1d5ce80, 0x7e18e680, 0x3f5f6140 },
        {  0x3ed34fc0, 0x81cd3080, 0xc1bbe1c0, 0x7e32cf80, 0x3f70ce80 },
        {  0x3edad680, 0x81b4b080, 0xc1a35ec0, 0x7e4b4f80, 0x3f81cac0 },
        {  0x3ee16a80, 0x819d8700, 0xc18c31c0, 0x7e627900, 0x3f9263c0 },
        {  0x3ee71080, 0x8187a100, 0xc17648c0, 0x7e785f00, 0x3fa2a6c0 },
        {  0x3eebcc80, 0x8172ed80, 0xc16192c0, 0x7e8d1280, 0x3fb2a0c0 },
        {  0x3eefa200, 0x815f5c80, 0xc14dff00, 0x7ea0a380, 0x3fc25ec0 },
        {  0x3ef29400, 0x814cde80, 0xc13b7e40, 0x7eb32180, 0x3fd1ee00 },
        {  0x3ef4a400, 0x813b6400, 0xc12a0140, 0x7ec49c00, 0x3fe15ac0 },
        {  0x3ef5d3c0, 0x812adf00, 0xc1197a40, 0x7ed52100, 0x3ff0b1c0 },
        {  0x3ef623c0, 0x811b4300, 0xc109dc40, 0x7ee4bd00, 0x40000000 },
        {  0x3ef59400, 0x810c8300, 0xc0fb1a00, 0x7ef37d00, 0x400f5200 },
        {  0x3ef423c0, 0x80fe9300, 0xc0ed2840, 0x7f016d00, 0x401eb400 },
        {  0x3ef1d180, 0x80f16800, 0xc0dffb40, 0x7f0e9800, 0x402e3380 },
        {  0x3eee9ac0, 0x80e4f700, 0xc0d38880, 0x7f1b0900, 0x403ddc80 },
        {  0x3eea7d00, 0x80d93580, 0xc0c7c5c0, 0x7f26ca80, 0x404dbd80 },
        {  0x3ee57480, 0x80ce1b00, 0xc0bca940, 0x7f31e500, 0x405de280 },
        {  0x3edf7c80, 0x80c39d80, 0xc0b22a40, 0x7f3c6280, 0x406e5900 },
        {  0x3ed89000, 0x80b9b480, 0xc0a84000, 0x7f464b80, 0x407f3000 },
        {  0x3ed0a900, 0x80b05800, 0xc09ee240, 0x7f4fa800, 0x40907480 },
        {  0x3ec7c0c0, 0x80a78000, 0xc0960980, 0x7f588000, 0x40a23600 },
        {  0x3ebdcf40, 0x809f2600, 0xc08dae40, 0x7f60da00, 0x40b48280 },
        {  0x3eb2cc80, 0x80974280, 0xc085c980, 0x7f68bd80, 0x40c76a00 },
    },
    {
        {  0x3df674c0, 0x836d7100, 0xc341ad40, 0x7c928f00, 0x3ec7ddc0 },
        {  0x3e06d9c0, 0x833ffd80, 0xc3142a00, 0x7cc00280, 0x3ee4fc40 },
        {  0x3e15a780, 0x8314f680, 0xc2e913c0, 0x7ceb0980, 0x3f0144c0 },
        {  0x3e22e800, 0x82ec3c80, 0xc2c04b40, 0x7d13c380, 0x3f1cccc0 },
        {  0x3e2ea480, 0x82c5b100, 0xc299b240, 0x7d3a4f00, 0x3f37a940 },
        {  0x3e38e480, 0x82a13800, 0xc2752c40, 0x7d5ec800, 0x3f51ef40 },
        {  0x3e41af40, 0x827eb580, 0xc2529dc0, 0x7d814a80, 0x3f6bb300 },
        {  0x3e490a80, 0x825e1000, 0xc231ecc0, 0x7da1f000, 0x3f8508c0 },
        {  0x3e4efb00, 0x823f2e00, 0xc2130000, 0x7dc0d200, 0x3f9e0500 },
        {  0x3e538480, 0x8221f880, 0xc1f5c040, 0x7dde0780, 0x3fb6bb00 },
        {  0x3e56a9c0, 0x82065900, 0xc1da1700, 0x7df9a700, 0x3fcf3f00 },
        {  0x3e586c80, 0x81ec3a00, 0xc1bfeec0, 0x7e13c600, 0x3fe7a4c0 },
        {  0x3e58cd00, 0x81d38700, 0xc1a73300, 0x7e2c7900, 0x40000000 },
        {  0x3e57cb00, 0x81bc2c80, 0xc18fd080, 0x7e43d380, 0x40186480 },
        {  0x3e5564c0, 0x81a61900, 0xc179b500, 0x7e59e700, 0x4030e600 },
        {  0x3e5197c0, 0x81913a00, 0xc164cf00, 0x7e6ec600, 0x40499900 },
        {  0x3e4c6000, 0x817d8000, 0xc1510e00, 0x7e828000, 0x40629200 },
        {  0x3e45b8c0, 0x816ada80, 0xc13e6200, 0x7e952580, 0x407be500 },
        {  0x3e3d9bc0, 0x81593b80, 0xc12cbc80, 0x7ea6c480, 0x4095a800 },
        {  0x3e340180, 0x81489400, 0xc11c0f40, 0x7eb76c00, 0x40afef80 },
        {  0x3e28e1c0, 0x8138d700, 0xc10c4c80, 0x7ec72900, 0x40cad180 },
        {  0x3e1c3280, 0x8129f780, 0xc0fd6840, 0x7ed60880, 0x40e66500 },
        {  0x3e0de900, 0x811bea00, 0xc0ef55c0, 0x7ee41600, 0x4102c100 },
        {  0x3dfdf8c0, 0x810ea300, 0xc0e20a00, 0x7ef15d00, 0x411ffd00 },
        {  0x3dec53c0, 0x81021780, 0xc0d57a40, 0x7efde880, 0x413e3200 },
    },
    {
        {  0x3cd6f040, 0x85783c00, 0xc50d44c0, 0x7a87c400, 0x3e1bcb00 },
        {  0x3cefc140, 0x8532d400, 0xc4c7a040, 0x7acd2c00, 0x3e489e80 },
        {  0x3d062b80, 0x84f10580, 0xc4859880, 0x7b0efa80, 0x3e743c00 },
        {  0x3d1a3c80, 0x84b2a500, 0xc4470140, 0x7b4d5b00, 0x3e9ec240 },
        {  0x3d2bffc0, 0x84778700, 0xc40bb000, 0x7b887900, 0x3ec85040 },
        {  0x3d3b7fc0, 0x843f8400, 0xc3d37bc0, 0x7bc07c00, 0x3ef10480 },
        {  0x3d48c500, 0x840a7400, 0xc39e3dc0, 0x7bf58c00, 0x3f18fd40 },
        {  0x3d53d740, 0x83d83280, 0xc36bd000, 0x7c27cd80, 0x3f4058c0 },
        {  0x3d5cbc80, 0x83a89b00, 0xc33c0f00, 0x7c576500, 0x3f6734c0 },
        {  0x3d637900, 0x837b8b80, 0xc30ed800, 0x7c847480, 0x3f8daf00 },
        {  0x3d680fc0, 0x8350e300, 0xc2e40a80, 0x7caf1d00, 0x3fb3e5c0 },
        {  0x3d6a8240, 0x83288300, 0xc2bb8740, 0x7cd77d00, 0x3fd9f6c0 },
        {  0x3d6ad040, 0x83024d00, 0xc2952fc0, 0x7cfdb300, 0x40000000 },
        {  0x3d68f840, 0x82de2480, 0xc270e7c0, 0x7d21db80, 0x40262000 },
        {  0x3d64f680, 0x82bbef00, 0xc24e9440, 0x7d441100, 0x404c7500 },
        {  0x3d5ec680, 0x829b9180, 0xc22e1b00, 0x7d646e80, 0x40731e80 },
        {  0x3d566140, 0x827cf500, 0xc20f6340, 0x7d830b00, 0x409a3b80 },
        {  0x3d4bbe40, 0x82600080, 0xc1f255c0, 0x7d9fff80, 0x40c1ec00 },
        {  0x3d3ed3c0, 0x82449e80, 0xc1d6dc00, 0x7dbb6180, 0x40ea5080 },
        {  0x3d2f9540, 0x822aba00, 0xc1bce080, 0x7dd54600, 0x41138a00 },
        {  0x3d1df580, 0x82123e00, 0xc1a44f80, 0x7dedc200, 0x413dbb00 },
        {  0x3d09e440, 0x81fb1880, 0xc18d1540, 0x7e04e780, 0x41690680 },
        {  0x3cf35000, 0x81e53600, 0xc1772000, 0x7e1aca00, 0x41959000 },
        {  0x3cda24c0, 0x81d08680, 0xc1625e40, 0x7e2f7980, 0x41c37d00 },
        {  0x3cbe4cc0, 0x81bcf880, 0xc14ebf40, 0x7e430780, 0x41f2f400 },
    },
    {
        {  0x3b1ae5c0, 0x88da4200, 0xc7d30f40, 0x7725be00, 0x3d120b00 },
        {  0x3b3fdb80, 0x88719980, 0xc7697f80, 0x778e6680, 0x3d56a500 },
        {  0x3b614100, 0x880e2280, 0xc7052d00, 0x77f1dd80, 0x3d999200 },
        {  0x3b7f2540, 0x87afa380, 0xc6a5dd40, 0x78505c80, 0x3ddafd80 },
        {  0x3b999580, 0x8755e500, 0xc64b5840, 0x78aa1b00, 0x3e1b1240 },
        {  0x3bb09c80, 0x8700b000, 0xc5f56740, 0x78ff5000, 0x3e59fc00 },
        {  0x3bc44480, 0x86afd100, 0xc5a3d580, 0x79502f00, 0x3e97e600 },
        {  0x3bd494c0, 0x86631480, 0xc5566f40, 0x799ceb80, 0x3ed4fc00 },
        {  0x3be19300, 0x861a4980, 0xc50d0380, 0x79e5b680, 0x3f116940 },
        {  0x3beb4340, 0x85d54080, 0xc4c76280, 0x7a2abf80, 0x3f4d5a40 },
        {  0x3bf1a700, 0x8593cc80, 0xc4855dc0, 0x7a6c3380, 0x3f88fb40 },
        {  0x3bf4bdc0, 0x8555c180, 0xc446c980, 0x7aaa3e80, 0x3fc478c0 },
        {  0x3bf484c0, 0x851af500, 0xc40b7b40, 0x7ae50b00, 0x40000000 },
        {  0x3bf0f740, 0x84e33f00, 0xc3d349c0, 0x7b1cc100, 0x403bbf00 },
        {  0x3bea0e00, 0x84ae7800, 0xc39e0e40, 0x7b518800, 0x4077e400 },
        {  0x3bdfbf00, 0x847c7b00, 0xc36ba340, 0x7b838500, 0x40b49e00 },
        {  0x3bd1fe40, 0x844d2500, 0xc33be480, 0x7bb2db00, 0x40f21d80 },
        {  0x3bc0bd00, 0x84205380, 0xc30eb000, 0x7bdfac80, 0x41309300 },
        {  0x3babe9c0, 0x83f5e600, 0xc2e3e480, 0x7c0a1a00, 0x41703180 },
        {  0x3b937080, 0x83cdbd00, 0xc2bb6340, 0x7c324300, 0x41b12c80 },
        {  0x3b773a40, 0x83a7bb80, 0xc2950dc0, 0x7c584480, 0x41f3b800 },
        {  0x3b572d00, 0x8383c500, 0xc270c7c0, 0x7c7c3b00, 0x42380b80 },
        {  0x3b332bc0, 0x8361be80, 0xc24e75c0, 0x7c9e4180, 0x427e5e80 },
        {  0x3b0b1640, 0x83418e00, 0xc22dfe00, 0x7cbe7200, 0x42c6eb80 },
        {  0x3adec980, 0x83231b00, 0xc20f4800, 0x7cdce500, 0x4311ee80 },
    },
    {
        {  0x3879c680, 0x8e8fce80, 0xcc070800, 0x71703180, 0x3b7f3180 },
        {  0x38af2300, 0x8df65b00, 0xcb6a2700, 0x7209a500, 0x3be6b600 },
        {  0x38df5dc0, 0x8d63fe80, 0xcad48540, 0x729c0180, 0x3c4c1d40 },
        {  0x390a8140, 0x8cd87700, 0xca45e000, 0x73278900, 0x3caf9ec0 },
        {  0x393096c0, 0x8c538500, 0xc9bdf580, 0x73ac7b00, 0x3d1173c0 },
        {  0x3951a580, 0x8bd4e780, 0xc93c8400, 0x742b1880, 0x3d71d640 },
        {  0x396db300, 0x8b5c6000, 0xc8c14b40, 0x74a3a000, 0x3dd101c0 },
        {  0x3984c200, 0x8ae9b080, 0xc84c0b80, 0x75164f80, 0x3e2f3240 },
        {  0x3996d400, 0x8a7c9b00, 0xc7dc8680, 0x75836500, 0x3e8ca580 },
        {  0x39a3e740, 0x8a14e500, 0xc7727f00, 0x75eb1b00, 0x3ee99980 },
        {  0x39abf7c0, 0x89b25380, 0xc70db9c0, 0x764dac80, 0x3f464e80 },
        {  0x39aefe40, 0x8954ad80, 0xc6adfc80, 0x76ab5280, 0x3fa30540 },
        {  0x39acf140, 0x88fbbc80, 0xc6530ec0, 0x77044380, 0x40000000 },
        {  0x39a5c3c0, 0x88a74a80, 0xc5fcb980, 0x7758b580, 0x405d8280 },
        {  0x39996580, 0x88572380, 0xc5aac840, 0x77a8dc80, 0x40bbd280 },
        {  0x3987c2c0, 0x880b1500, 0xc55d0700, 0x77f4eb00, 0x411b3680 },
        {  0x3970c480, 0x87c2ef00, 0xc5134440, 0x783d1100, 0x417bf780 },
        {  0x39545000, 0x877e8280, 0xc4cd5040, 0x78817d80, 0x41de6000 },
        {  0x39324640, 0x873da200, 0xc48afcc0, 0x78c25e00, 0x4242bd00 },
        {  0x390a8480, 0x87002280, 0xc44c1d40, 0x78ffdd80, 0x42a95e00 },
        {  0x38dce3c0, 0x86c5da00, 0xc4108780, 0x793a2600, 0x43129500 },
        {  0x38a93840, 0x868ea080, 0xc3d81240, 0x79715f80, 0x437eb580 },
        {  0x386f5200, 0x865a4f80, 0xc3a29600, 0x79a5b080, 0x43ee1800 },
        {  0x382efc00, 0x8628c280, 0xc36fed80, 0x79d73d80, 0x44611680 },
        {  0x37e7fc40, 0x85f9d580, 0xc33ff480, 0x7a062a80, 0x44d80f00 },
    },
    {
        {  0x34ef52c0, 0x978aac00, 0xd1affa40, 0x68755400, 0x3960b300 },
        {  0x3536edc0, 0x96ba5b00, 0xd0d3fc80, 0x6945a500, 0x39f515c0 },
        {  0x35779ec0, 0x95f2b100, 0xd0012200, 0x6a0d4f00, 0x3a873f40 },
        {  0x35b15fc0, 0x95337680, 0xcf372fc0, 0x6acc8980, 0x3b177040 },
        {  0x35e42ac0, 0x947c7180, 0xce75e900, 0x6b838e80, 0x3ba5ec80 },
        {  0x360ff7c0, 0x93cd6600, 0xcdbd0e00, 0x6c329a00, 0x3c32fa40 },
        {  0x3634bd80, 0x93261700, 0xcd0c5e80, 0x6cd9e900, 0x3cbee400 },
        {  0x365270c0, 0x92864680, 0xcc639900, 0x6d79b980, 0x3d49f600 },
        {  0x36690440, 0x91edb580, 0xcbc27b40, 0x6e124a80, 0x3dd48080 },
        {  0x367867c0, 0x915c2500, 0xcb28c240, 0x6ea3db00, 0x3e5ed600 },
        {  0x36808800, 0x90d15600, 0xca962c00, 0x6f2eaa00, 0x3ee94c00 },
        {  0x36814ec0, 0x904d0a00, 0xca0a75c0, 0x6fb2f600, 0x3f743b80 },
        {  0x367aa280, 0x8fcf0200, 0xc9855d80, 0x7030fe00, 0x40000000 },
        {  0x366c64c0, 0x8f570100, 0xc906a2c0, 0x70a8ff00, 0x408cf880 },
        {  0x36567440, 0x8ee4ca00, 0xc88e0500, 0x711b3600, 0x411b8680 },
        {  0x3638aa80, 0x8e782100, 0xc81b4540, 0x7187df00, 0x41ac1080 },
        {  0x3612dc00, 0x8e10cc00, 0xc7ae25c0, 0x71ef3400, 0x423efe00 },
        {  0x35e4d8c0, 0x8dae9180, 0xc7466a00, 0x72516e80, 0x42d4bd00 },
        {  0x35ae6b00, 0x8d513a00, 0xc6e3d700, 0x72aec600, 0x436dbe00 },
        {  0x356f5780, 0x8cf88e80, 0xc6863380, 0x73077180, 0x440a7500 },
        {  0x35275c80, 0x8ca45b00, 0xc62d47c0, 0x735ba500, 0x44ab5b80 },
        {  0x34d63240, 0x8c546b80, 0xc5d8dd80, 0x73ab9480, 0x4550f000 },
        {  0x347b8a80, 0x8c088f00, 0xc588c0c0, 0x73f77100, 0x45fbb500 },
        {  0x34170f40, 0x8bc09580, 0xc53cbe40, 0x743f6a80, 0x46ac3280 },
        {  0x33a86380, 0x8b7c5080, 0xc4f4a5c0, 0x7483af80, 0x4762f700 },
    },
    {
        {  0x3003e0c0, 0xa746e700, 0xd98d2cc0, 0x58b91900, 0x366ef280 },
        {  0x305d7540, 0xa6466900, 0xd86500c0, 0x59b99700, 0x373d8a00 },
        {  0x30ae2c00, 0xa54ee480, 0xd7473180, 0x5ab11b80, 0x380aa280 },
        {  0x30f5d980, 0xa4604680, 0xd633a980, 0x5b9fb980, 0x38d67d00 },
        {  0x31345240, 0xa37a7600, 0xd52a4b80, 0x5c858a00, 0x39a16240 },
        {  0x31696b00, 0xa29d5480, 0xd42af4c0, 0x5d62ab80, 0x3a6ba040 },
        {  0x3194f740, 0xa1c8bf80, 0xd3357cc0, 0x5e374080, 0x3b358c40 },
        {  0x31b6c940, 0xa0fc8f00, 0xd249b580, 0x5f037100, 0x3bff8140 },
        {  0x31ceb1c0, 0xa0389780, 0xd1676d40, 0x5fc76880, 0x3cc9e100 },
        {  0x31dc7e80, 0x9f7cab80, 0xd08e6e80, 0x60835480, 0x3d951300 },
        {  0x31dffac0, 0x9ec89880, 0xcfbe8040, 0x61376780, 0x3e618500 },
        {  0x31d8ee40, 0x9e1c2b80, 0xcef76680, 0x61e3d480, 0x3f2fab40 },
        {  0x31c71c80, 0x9d772e80, 0xce38e380, 0x6288d180, 0x40000000 },
        {  0x31aa4480, 0x9cd96b00, 0xcd82b800, 0x63269500, 0x40d30380 },
        {  0x318220c0, 0x9c42a800, 0xccd4a280, 0x63bd5800, 0x41a93d00 },
        {  0x314e6500, 0x9bb2ad00, 0xcc2e6180, 0x644d5300, 0x42833980 },
        {  0x310ec000, 0x9b294080, 0xcb8fb280, 0x64d6bf80, 0x43618d80 },
        {  0x30c2d8c0, 0x9aa62880, 0xcaf852c0, 0x6559d780, 0x4444d480 },
        {  0x306a4f80, 0x9a292c00, 0xca680000, 0x65d6d400, 0x452db080 },
        {  0x3004bcc0, 0x99b21080, 0xc9de77c0, 0x664def80, 0x461ccb80 },
        {  0x2f91b040, 0x99409e80, 0xc95b78c0, 0x66bf6180, 0x4712d700 },
        {  0x2f10b0c0, 0x98d49d00, 0xc8dec1c0, 0x672b6300, 0x48108d80 },
        {  0x2e813bc0, 0x986dd500, 0xc8681340, 0x67922b00, 0x4916b100 },
        {  0x2de2c3c0, 0x980c1000, 0xc7f72e80, 0x67f3f000, 0x4a260e00 },
        {  0x2d34b140, 0x97af1980, 0xc78bd640, 0x6850e680, 0x4b3f7880 },
    },
    {
        {  0x2a884280, 0xc0683500, 0xe250d4c0, 0x3f97cb00, 0x3326e8c0 },
        {  0x2aeba9c0, 0xbf705880, 0xe0e3af00, 0x408fa780, 0x3430a700 },
        {  0x2b449180, 0xbe7f5200, 0xdf809c40, 0x4180ae00, 0x353ad240 },
        {  0x2b929480, 0xbd953d00, 0xde27c380, 0x426ac300, 0x3645a800 },
        {  0x2bd54fc0, 0xbcb22c80, 0xdcd94100, 0x434dd380, 0x37516f40 },
        {  0x2c0c5f40, 0xbbd62c80, 0xdb952740, 0x4429d380, 0x385e7940 },
        {  0x2c375f80, 0xbb014300, 0xda5b7ec0, 0x44febd00, 0x396d21c0 },
        {  0x2c55eb80, 0xba337000, 0xd92c46c0, 0x45cc9000, 0x3a7dcdc0 },
        {  0x2c679d00, 0xb96cac80, 0xd8077680, 0x46935380, 0x3b90ec80 },
        {  0x2c6c0b40, 0xb8acee00, 0xd6ecfcc0, 0x47531200, 0x3ca6f800 },
        {  0x2c62ca80, 0xb7f42380, 0xd5dcc180, 0x480bdc80, 0x3dc07400 },
        {  0x2c4b6c00, 0xb7423880, 0xd4d6a5c0, 0x48bdc780, 0x3eddee40 },
        {  0x2c257b40, 0xb6971300, 0xd3da84c0, 0x4968ed00, 0x40000000 },
        {  0x2bf08000, 0xb5f29700, 0xd2e833c0, 0x4a0d6900, 0x41274c00 },
        {  0x2babfb40, 0xb554a480, 0xd1ff8440, 0x4aab5b80, 0x42548080 },
        {  0x2b576700, 0xb4bd1900, 0xd1204300, 0x4b42e700, 0x43885600 },
        {  0x2af23600, 0xb42bce80, 0xd04a38c0, 0x4bd43180, 0x44c39100 },
        {  0x2a7bd280, 0xb3a09e80, 0xcf7d2c00, 0x4c5f6180, 0x46070180 },
        {  0x29f39d80, 0xb31b5f80, 0xceb8e000, 0x4ce4a080, 0x47538280 },
        {  0x2958ed40, 0xb29be700, 0xcdfd1680, 0x4d641900, 0x48a9fc00 },
        {  0x28ab0dc0, 0xb2220a00, 0xcd498f80, 0x4dddf600, 0x4a0b6300 },
        {  0x27e93e00, 0xb1ad9b80, 0xcc9e0940, 0x4e526480, 0x4b78b880 },
        {  0x2712b0c0, 0xb13e6f80, 0xcbfa4240, 0x4ec19080, 0x4cf30d00 },
        {  0x26268a40, 0xb0d45880, 0xcb5df780, 0x4f2ba780, 0x4e7b7e00 },
        {  0x2523e080, 0xb06f2880, 0xcac8e6c0, 0x4f90d780, 0x50133900 },  
    },
    {
        {  0x25f68540, 0xe9a4a3e0, 0xe99ea2c0, 0x165b5c20, 0x306ad800 },
        {  0x2659cdc0, 0xe93a9c40, 0xe804f800, 0x16c563c0, 0x31a13a40 },
        {  0x26b18000, 0xe8d2d7c0, 0xe6740a80, 0x172d2840, 0x32da7580 },
        {  0x26fd0100, 0xe86d6e80, 0xe4ec3800, 0x17929180, 0x3416c700 },
        {  0x273bb580, 0xe80a75c0, 0xe36dd260, 0x17f58a40, 0x35567840 },
        {  0x276d01c0, 0xe7a9ffc0, 0xe1f91fe0, 0x18560040, 0x3699de80 },
        {  0x27904940, 0xe74c1b80, 0xe08e5b20, 0x18b3e480, 0x37e15bc0 },
        {  0x27a4edc0, 0xe6f0d580, 0xdf2db380, 0x190f2a80, 0x392d5e80 },
        {  0x27aa4fc0, 0xe6983700, 0xddd74dc0, 0x1967c900, 0x3a7e62c0 },
        {  0x279fcb80, 0xe64246c0, 0xdc8b4380, 0x19bdb940, 0x3bd4f100 },
        {  0x2784bb40, 0xe5ef0900, 0xdb49a4c0, 0x1a10f700, 0x3d31a000 },
        {  0x27587400, 0xe59e7f40, 0xda1277c0, 0x1a6180c0, 0x3e951440 },
        {  0x271a45c0, 0xe550a8e0, 0xd8e5ba40, 0x1aaf5720, 0x40000000 },
        {  0x26c97ac0, 0xe5058300, 0xd7c36100, 0x1afa7d00, 0x41732480 },
        {  0x266555c0, 0xe4bd08e0, 0xd6ab5900, 0x1b42f720, 0x42ef5100 },
        {  0x25ed1200, 0xe47733a0, 0xd59d8900, 0x1b88cc60, 0x44756500 },
        {  0x255fe100, 0xe433fb00, 0xd499d000, 0x1bcc0500, 0x46064f00 },
        {  0x24bceb00, 0xe3f35520, 0xd3a00800, 0x1c0caae0, 0x47a30d00 },
        {  0x24034c80, 0xe3b536c0, 0xd2b005c0, 0x1c4ac940, 0x494cae00 },
        {  0x23321580, 0xe3799340, 0xd1c99900, 0x1c866cc0, 0x4b045180 },
        {  0x22484880, 0xe3405d60, 0xd0ec8d80, 0x1cbfa2a0, 0x4ccb2a00 },
        {  0x2144d9c0, 0xe3098680, 0xd018abc0, 0x1cf67980, 0x4ea27a80 },
        {  0x2026ac80, 0xe2d4ffc0, 0xcf4db940, 0x1d2b0040, 0x508b9a80 },
        {  0x1eec9340, 0xe2a2b920, 0xce8b78c0, 0x1d5d46e0, 0x5287f400 },
        {  0x1d954d80, 0xe272a260, 0xcdd1abc0, 0x1d8d5da0, 0x54990680 },
    },
    {
        {  0x27d7ee80, 0x2cb17200, 0xe69d1c20, 0xd34e8e00, 0x318af580 },
        {  0x283c2f00, 0x2d75d5c0, 0xe5145480, 0xd28a2a40, 0x32af7c80 },
        {  0x28954700, 0x2e358740, 0xe394f1c0, 0xd1ca78c0, 0x33d5c740 },
        {  0x28e2b200, 0x2ef06240, 0xe21f3b60, 0xd10f9dc0, 0x34fe1280 },
        {  0x2923ec80, 0x2fa64980, 0xe0b36d40, 0xd059b680, 0x3628a640 },
        {  0x29587300, 0x30572400, 0xdf51b7c0, 0xcfa8dc00, 0x3755d500 },
        {  0x297fc100, 0x3102df80, 0xddfa4100, 0xcefd2080, 0x3885fe00 },
        {  0x29995180, 0x31a96e40, 0xdcad2380, 0xce5691c0, 0x39b98b40 },
        {  0x29a49d00, 0x324ac800, 0xdb6a7040, 0xcdb53800, 0x3af0f2c0 },
        {  0x29a11a40, 0x32e6e8c0, 0xda322ec0, 0xcd191740, 0x3c2cb740 },
        {  0x298e3c40, 0x337dd180, 0xd9045d00, 0xcc822e80, 0x3d6d66c0 },
        {  0x296b7240, 0x340f8780, 0xd7e0f140, 0xcbf07880, 0x3eb39c80 },
        {  0x29382700, 0x349c1380, 0xd6c7d900, 0xcb63ec80, 0x40000000 },
        {  0x28f3bf00, 0x35238240, 0xd5b8fbc0, 0xcadc7dc0, 0x41534580 },
        {  0x289d9880, 0x35a5e380, 0xd4b43900, 0xca5a1c80, 0x42ae2e80 },
        {  0x28350a80, 0x36234a00, 0xd3b96bc0, 0xc9dcb600, 0x44118980 },
        {  0x27b96340, 0x369bcb80, 0xd2c868c0, 0xc9643480, 0x457e3400 },
        {  0x2729e7c0, 0x370f7fc0, 0xd1e10080, 0xc8f08040, 0x46f51800 },
        {  0x2685d280, 0x377e8080, 0xd102ff00, 0xc8817f80, 0x48772e80 },
        {  0x25cc5280, 0x37e8e980, 0xd02e2d40, 0xc8171680, 0x4a058000 },
        {  0x24fc8a80, 0x384ed780, 0xcf6250c0, 0xc7b12880, 0x4ba12480 },
        {  0x24158f80, 0x38b06980, 0xce9f2d00, 0xc74f9680, 0x4d4b4380 },
        {  0x23166780, 0x390dbe80, 0xcde48340, 0xc6f24180, 0x4f051580 },
        {  0x21fe08c0, 0x3966f680, 0xcd321300, 0xc6990980, 0x50cfe480 },
        {  0x20cb5840, 0x39bc3280, 0xcc879ac0, 0xc643cd80, 0x52ad0d00 },
    }
};

#endif //#end of _AK77XX_DSP1_ADDR_INCLUDED
