/*
 * max98090.h -- MAX98090 ALSA SoC Audio driver
 *
 * Copyright 2011-2012 Maxim Integrated Products
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 */

#ifndef _MAX98090_H
#define _MAX98090_H

/*
 * The default operating frequency for a DMIC attached to the codec.
 * This can be overridden by a device tree property.
 */
#define MAX98090_DEFAULT_DMIC_FREQ		2500000

/*
 * MAX98090 Register Definitions
 */

#define M98090_REG_SOFTWARE_RESET		0x00
#define M98090_REG_DEVICE_STATUS		0x01
#define M98090_REG_JACK_STATUS			0x02
#define M98090_REG_INTERRUPT_S			0x03
#define M98090_REG_QUICK_SYSTEM_CLOCK		0x04
#define M98090_REG_QUICK_SAMPLE_RATE		0x05
#define M98090_REG_DAI_INTERFACE		0x06
#define M98090_REG_DAC_PATH			0x07
#define M98090_REG_MIC_DIRECT_TO_ADC		0x08
#define M98090_REG_LINE_TO_ADC			0x09
#define M98090_REG_ANALOG_MIC_LOOP		0x0A
#define M98090_REG_ANALOG_LINE_LOOP		0x0B
#define M98090_REG_RESERVED			0x0C
#define M98090_REG_LINE_INPUT_CONFIG		0x0D
#define M98090_REG_LINE_INPUT_LEVEL		0x0E
#define M98090_REG_INPUT_MODE			0x0F
#define M98090_REG_MIC1_INPUT_LEVEL		0x10
#define M98090_REG_MIC2_INPUT_LEVEL		0x11
#define M98090_REG_MIC_BIAS_VOLTAGE		0x12
#define M98090_REG_DIGITAL_MIC_ENABLE		0x13
#define M98090_REG_DIGITAL_MIC_CONFIG		0x14
#define M98090_REG_LEFT_ADC_MIXER		0x15
#define M98090_REG_RIGHT_ADC_MIXER		0x16
#define M98090_REG_LEFT_ADC_LEVEL		0x17
#define M98090_REG_RIGHT_ADC_LEVEL		0x18
#define M98090_REG_ADC_BIQUAD_LEVEL		0x19
#define M98090_REG_ADC_SIDETONE			0x1A
#define M98090_REG_SYSTEM_CLOCK			0x1B
#define M98090_REG_CLOCK_MODE			0x1C
#define M98090_REG_CLOCK_RATIO_NI_MSB		0x1D
#define M98090_REG_CLOCK_RATIO_NI_LSB		0x1E
#define M98090_REG_CLOCK_RATIO_MI_MSB		0x1F
#define M98090_REG_CLOCK_RATIO_MI_LSB		0x20
#define M98090_REG_MASTER_MODE			0x21
#define M98090_REG_INTERFACE_FORMAT		0x22
#define M98090_REG_TDM_CONTROL			0x23
#define M98090_REG_TDM_FORMAT			0x24
#define M98090_REG_IO_CONFIGURATION		0x25
#define M98090_REG_FILTER_CONFIG		0x26
#define M98090_REG_DAI_PLAYBACK_LEVEL		0x27
#define M98090_REG_DAI_PLAYBACK_LEVEL_EQ	0x28
#define M98090_REG_LEFT_HP_MIXER		0x29
#define M98090_REG_RIGHT_HP_MIXER		0x2A
#define M98090_REG_HP_CONTROL			0x2B
#define M98090_REG_LEFT_HP_VOLUME		0x2C
#define M98090_REG_RIGHT_HP_VOLUME		0x2D
#define M98090_REG_LEFT_SPK_MIXER		0x2E
#define M98090_REG_RIGHT_SPK_MIXER		0x2F
#define M98090_REG_SPK_CONTROL			0x30
#define M98090_REG_LEFT_SPK_VOLUME		0x31
#define M98090_REG_RIGHT_SPK_VOLUME		0x32
#define M98090_REG_DRC_TIMING			0x33
#define M98090_REG_DRC_COMPRESSOR		0x34
#define M98090_REG_DRC_EXPANDER			0x35
#define M98090_REG_DRC_GAIN			0x36
#define M98090_REG_RCV_LOUTL_MIXER		0x37
#define M98090_REG_RCV_LOUTL_CONTROL		0x38
#define M98090_REG_RCV_LOUTL_VOLUME		0x39
#define M98090_REG_LOUTR_MIXER			0x3A
#define M98090_REG_LOUTR_CONTROL		0x3B
#define M98090_REG_LOUTR_VOLUME			0x3C
#define M98090_REG_JACK_DETECT			0x3D
#define M98090_REG_INPUT_ENABLE			0x3E
#define M98090_REG_OUTPUT_ENABLE		0x3F
#define M98090_REG_LEVEL_CONTROL		0x40
#define M98090_REG_DSP_FILTER_ENABLE		0x41
#define M98090_REG_BIAS_CONTROL			0x42
#define M98090_REG_DAC_CONTROL			0x43
#define M98090_REG_ADC_CONTROL			0x44
#define M98090_REG_DEVICE_SHUTDOWN		0x45
#define M98090_REG_EQUALIZER_BASE		0x46
#define M98090_REG_RECORD_BIQUAD_BASE		0xAF
#define M98090_REG_DMIC3_VOLUME			0xBE
#define M98090_REG_DMIC4_VOLUME			0xBF
#define M98090_REG_DMIC34_BQ_PREATTEN		0xC0
#define M98090_REG_RECORD_TDM_SLOT		0xC1
#define M98090_REG_SAMPLE_RATE			0xC2
#define M98090_REG_DMIC34_BIQUAD_BASE		0xC3
#define M98090_REG_REVISION_ID			0xFF

#define M98090_REG_CNT				(0xFF+1)
#define MAX98090_MAX_REGISTER			0xFF

/* MAX98090 Register Bit Fields */

/*
 * M98090_REG_SOFTWARE_RESET
 */
#define M98090_SWRESET_MASK		(1<<7)
#define M98090_SWRESET_SHIFT		7
#define M98090_SWRESET_WIDTH		1

/*
 * M98090_REG_DEVICE_STATUS
 */
#define M98090_CLD_MASK			(1<<7)
#define M98090_CLD_SHIFT		7
#define M98090_CLD_WIDTH		1
#define M98090_SLD_MASK			(1<<6)
#define M98090_SLD_SHIFT		6
#define M98090_SLD_WIDTH		1
#define M98090_ULK_MASK			(1<<5)
#define M98090_ULK_SHIFT		5
#define M98090_ULK_WIDTH		1
#define M98090_JDET_MASK		(1<<2)
#define M98090_JDET_SHIFT		2
#define M98090_JDET_WIDTH		1
#define M98090_DRCACT_MASK		(1<<1)
#define M98090_DRCACT_SHIFT		1
#define M98090_DRCACT_WIDTH		1
#define M98090_DRCCLP_MASK		(1<<0)
#define M98090_DRCCLP_SHIFT		0
#define M98090_DRCCLP_WIDTH		1

/*
 * M98090_REG_JACK_STATUS
 */
#define M98090_LSNS_MASK		(1<<2)
#define M98090_LSNS_SHIFT		2
#define M98090_LSNS_WIDTH		1
#define M98090_JKSNS_MASK		(1<<1)
#define M98090_JKSNS_SHIFT		1
#define M98090_JKSNS_WIDTH		1

/*
 * M98090_REG_INTERRUPT_S
 */
#define M98090_ICLD_MASK		(1<<7)
#define M98090_ICLD_SHIFT		7
#define M98090_ICLD_WIDTH		1
#define M98090_ISLD_MASK		(1<<6)
#define M98090_ISLD_SHIFT		6
#define M98090_ISLD_WIDTH		1
#define M98090_IULK_MASK		(1<<5)
#define M98090_IULK_SHIFT		5
#define M98090_IULK_WIDTH		1
#define M98090_IJDET_MASK		(1<<2)
#define M98090_IJDET_SHIFT		2
#define M98090_IJDET_WIDTH		1
#define M98090_IDRCACT_MASK		(1<<1)
#define M98090_IDRCACT_SHIFT		1
#define M98090_IDRCACT_WIDTH		1
#define M98090_IDRCCLP_MASK		(1<<0)
#define M98090_IDRCCLP_SHIFT		0
#define M98090_IDRCCLP_WIDTH		1

/*
 * M98090_REG_QUICK_SYSTEM_CLOCK
 */
#define M98090_26M_MASK			(1<<7)
#define M98090_26M_SHIFT		7
#define M98090_26M_WIDTH		1
#define M98090_19P2M_MASK		(1<<6)
#define M98090_19P2M_SHIFT		6
#define M98090_19P2M_WIDTH		1
#define M98090_13M_MASK			(1<<5)
#define M98090_13M_SHIFT		5
#define M98090_13M_WIDTH		1
#define M98090_12P288M_MASK		(1<<4)
#define M98090_12P288M_SHIFT		4
#define M98090_12P288M_WIDTH		1
#define M98090_12M_MASK			(1<<3)
#define M98090_12M_SHIFT		3
#define M98090_12M_WIDTH		1
#define M98090_11P2896M_MASK		(1<<2)
#define M98090_11P2896M_SHIFT		2
#define M98090_11P2896M_WIDTH		1
#define M98090_256FS_MASK		(1<<0)
#define M98090_256FS_SHIFT		0
#define M98090_256FS_WIDTH		1
#define M98090_CLK_ALL_SHIFT		0
#define M98090_CLK_ALL_WIDTH		8
#define M98090_CLK_ALL_NUM		(1<<M98090_CLK_ALL_WIDTH)

/*
 * M98090_REG_QUICK_SAMPLE_RATE
 */
#define M98090_SR_96K_MASK		(1<<5)
#define M98090_SR_96K_SHIFT		5
#define M98090_SR_96K_WIDTH		1
#define M98090_SR_32K_MASK		(1<<4)
#define M98090_SR_32K_SHIFT		4
#define M98090_SR_32K_WIDTH		1
#define M98090_SR_48K_MASK		(1<<3)
#define M98090_SR_48K_SHIFT		3
#define M98090_SR_48K_WIDTH		1
#define M98090_SR_44K1_MASK		(1<<2)
#define M98090_SR_44K1_SHIFT		2
#define M98090_SR_44K1_WIDTH		1
#define M98090_SR_16K_MASK		(1<<1)
#define M98090_SR_16K_SHIFT		1
#define M98090_SR_16K_WIDTH		1
#define M98090_SR_8K_MASK		(1<<0)
#define M98090_SR_8K_SHIFT		0
#define M98090_SR_8K_WIDTH		1
#define M98090_SR_MASK			0x3F
#define M98090_SR_ALL_SHIFT		0
#define M98090_SR_ALL_WIDTH		8
#define M98090_SR_ALL_NUM		(1<<M98090_SR_ALL_WIDTH)

/*
 * M98090_REG_DAI_INTERFACE
 */
#define M98090_RJ_M_MASK		(1<<5)
#define M98090_RJ_M_SHIFT		5
#define M98090_RJ_M_WIDTH		1
#define M98090_RJ_S_MASK		(1<<4)
#define M98090_RJ_S_SHIFT		4
#define M98090_RJ_S_WIDTH		1
#define M98090_LJ_M_MASK		(1<<3)
#define M98090_LJ_M_SHIFT		3
#define M98090_LJ_M_WIDTH		1
#define M98090_LJ_S_MASK		(1<<2)
#define M98090_LJ_S_SHIFT		2
#define M98090_LJ_S_WIDTH		1
#define M98090_I2S_M_MASK		(1<<1)
#define M98090_I2S_M_SHIFT		1
#define M98090_I2S_M_WIDTH		1
#define M98090_I2S_S_MASK		(1<<0)
#define M98090_I2S_S_SHIFT		0
#define M98090_I2S_S_WIDTH		1
#define M98090_DAI_ALL_SHIFT		0
#define M98090_DAI_ALL_WIDTH		8
#define M98090_DAI_ALL_NUM		(1<<M98090_DAI_ALL_WIDTH)

/*
 * M98090_REG_DAC_PATH
 */
#define M98090_DIG2_HP_MASK		(1<<7)
#define M98090_DIG2_HP_SHIFT		7
#define M98090_DIG2_HP_WIDTH		1
#define M98090_DIG2_EAR_MASK		(1<<6)
#define M98090_DIG2_EAR_SHIFT		6
#define M98090_DIG2_EAR_WIDTH		1
#define M98090_DIG2_SPK_MASK		(1<<5)
#define M98090_DIG2_SPK_SHIFT		5
#define M98090_DIG2_SPK_WIDTH		1
#define M98090_DIG2_LOUT_MASK		(1<<4)
#define M98090_DIG2_LOUT_SHIFT		4
#define M98090_DIG2_LOUT_WIDTH		1
#define M98090_DIG2_ALL_SHIFT		0
#define M98090_DIG2_ALL_WIDTH		8
#define M98090_DIG2_ALL_NUM		(1<<M98090_DIG2_ALL_WIDTH)

/*
 * M98090_REG_MIC_DIRECT_TO_ADC
 */
#define M98090_IN12_MIC1_MASK		(1<<7)
#define M98090_IN12_MIC1_SHIFT		7
#define M98090_IN12_MIC1_WIDTH		1
#define M98090_IN34_MIC2_MASK		(1<<6)
#define M98090_IN34_MIC2_SHIFT		6
#define M98090_IN34_MIC2_WIDTH		1
#define M98090_IN56_MIC1_MASK		(1<<5)
#define M98090_IN56_MIC1_SHIFT		5
#define M98090_IN56_MIC1_WIDTH		1
#define M98090_IN56_MIC2_MASK		(1<<4)
#define M98090_IN56_MIC2_SHIFT		4
#define M98090_IN56_MIC2_WIDTH		1
#define M98090_IN12_DADC_MASK		(1<<3)
#define M98090_IN12_DADC_SHIFT		3
#define M98090_IN12_DADC_WIDTH		1
#define M98090_IN34_DADC_MASK		(1<<2)
#define M98090_IN34_DADC_SHIFT		2
#define M98090_IN34_DADC_WIDTH		1
#define M98090_IN56_DADC_MASK		(1<<1)
#define M98090_IN56_DADC_SHIFT		1
#define M98090_IN56_DADC_WIDTH		1
#define M98090_MIC_ALL_SHIFT		0
#define M98090_MIC_ALL_WIDTH		8
#define M98090_MIC_ALL_NUM		(1<<M98090_MIC_ALL_WIDTH)

/*
 * M98090_REG_LINE_TO_ADC
 */
#define M98090_IN12S_AB_MASK		(1<<7)
#define M98090_IN12S_AB_SHIFT		7
#define M98090_IN12S_AB_WIDTH		1
#define M98090_IN34S_AB_MASK		(1<<6)
#define M98090_IN34S_AB_SHIFT		6
#define M98090_IN34S_AB_WIDTH		1
#define M98090_IN56S_AB_MASK		(1<<5)
#define M98090_IN56S_AB_SHIFT		5
#define M98090_IN56S_AB_WIDTH		1
#define M98090_IN34D_A_MASK		(1<<4)
#define M98090_IN34D_A_SHIFT		4
#define M98090_IN34D_A_WIDTH		1
#define M98090_IN56D_B_MASK		(1<<3)
#define M98090_IN56D_B_SHIFT		3
#define M98090_IN56D_B_WIDTH		1
#define M98090_LINE_ALL_SHIFT		0
#define M98090_LINE_ALL_WIDTH		8
#define M98090_LINE_ALL_NUM		(1<<M98090_LINE_ALL_WIDTH)

/*
 * M98090_REG_ANALOG_MIC_LOOP
 */
#define M98090_IN12_M1HPL_MASK		(1<<7)
#define M98090_IN12_M1HPL_SHIFT		7
#define M98090_IN12_M1HPL_WIDTH		1
#define M98090_IN12_M1SPKL_MASK		(1<<6)
#define M98090_IN12_M1SPKL_SHIFT	6
#define M98090_IN12_M1SPKL_WIDTH	1
#define M98090_IN12_M1EAR_MASK		(1<<5)
#define M98090_IN12_M1EAR_SHIFT		5
#define M98090_IN12_M1EAR_WIDTH		1
#define M98090_IN12_M1LOUTL_MASK	(1<<4)
#define M98090_IN12_M1LOUTL_SHIFT	4
#define M98090_IN12_M1LOUTL_WIDTH	1
#define M98090_IN34_M2HPR_MASK		(1<<3)
#define M98090_IN34_M2HPR_SHIFT		3
#define M98090_IN34_M2HPR_WIDTH		1
#define M98090_IN34_M2SPKR_MASK		(1<<2)
#define M98090_IN34_M2SPKR_SHIFT	2
#define M98090_IN34_M2SPKR_WIDTH	1
#define M98090_IN34_M2EAR_MASK		(1<<1)
#define M98090_IN34_M2EAR_SHIFT		1
#define M98090_IN34_M2EAR_WIDTH		1
#define M98090_IN34_M2LOUTR_MASK	(1<<0)
#define M98090_IN34_M2LOUTR_SHIFT	0
#define M98090_IN34_M2LOUTR_WIDTH	1
#define M98090_AMIC_ALL_SHIFT		0
#define M98090_AMIC_ALL_WIDTH		8
#define M98090_AMIC_ALL_NUM		(1<<M98090_AMIC_ALL_WIDTH)

/*
 * M98090_REG_ANALOG_LINE_LOOP
 */
#define M98090_IN12S_ABHP_MASK		(1<<7)
#define M98090_IN12S_ABHP_SHIFT		7
#define M98090_IN12S_ABHP_WIDTH		1
#define M98090_IN34D_ASPKL_MASK		(1<<6)
#define M98090_IN34D_ASPKL_SHIFT	6
#define M98090_IN34D_ASPKL_WIDTH	1
#define M98090_IN34D_AEAR_MASK		(1<<5)
#define M98090_IN34D_AEAR_SHIFT		5
#define M98090_IN34D_AEAR_WIDTH		1
#define M98090_IN12S_ABLOUT_MASK	(1<<4)
#define M98090_IN12S_ABLOUT_SHIFT	4
#define M98090_IN12S_ABLOUT_WIDTH	1
#define M98090_IN34S_ABHP_MASK		(1<<3)
#define M98090_IN34S_ABHP_SHIFT		3
#define M98090_IN34S_ABHP_WIDTH		1
#define M98090_IN56D_BSPKR_MASK		(1<<2)
#define M98090_IN56D_BSPKR_SHIFT	2
#define M98090_IN56D_BSPKR_WIDTH	1
#define M98090_IN56D_BEAR_MASK		(1<<1)
#define M98090_IN56D_BEAR_SHIFT		1
#define M98090_IN56D_BEAR_WIDTH		1
#define M98090_IN34S_ABLOUT_MASK	(1<<0)
#define M98090_IN34S_ABLOUT_SHIFT	0
#define M98090_IN34S_ABLOUT_WIDTH	1
#define M98090_ALIN_ALL_SHIFT		0
#define M98090_ALIN_ALL_WIDTH		8
#define M98090_ALIN_ALL_NUM		(1<<M98090_ALIN_ALL_WIDTH)

/*
 * M98090_REG_RESERVED
 */

/*
 * M98090_REG_LINE_INPUT_CONFIG
 */
#define M98090_IN34DIFF_MASK		(1<<7)
#define M98090_IN34DIFF_SHIFT		7
#define M98090_IN34DIFF_WIDTH		1
#define M98090_IN56DIFF_MASK		(1<<6)
#define M98090_IN56DIFF_SHIFT		6
#define M98090_IN56DIFF_WIDTH		1
#define M98090_IN1SEEN_MASK		(1<<5)
#define M98090_IN1SEEN_SHIFT		5
#define M98090_IN1SEEN_WIDTH		1
#define M98090_IN2SEEN_MASK		(1<<4)
#define M98090_IN2SEEN_SHIFT		4
#define M98090_IN2SEEN_WIDTH		1
#define M98090_IN3SEEN_MASK		(1<<3)
#define M98090_IN3SEEN_SHIFT		3
#define M98090_IN3SEEN_WIDTH		1
#define M98090_IN4SEEN_MASK		(1<<2)
#define M98090_IN4SEEN_SHIFT		2
#define M98090_IN4SEEN_WIDTH		1
#define M98090_IN5SEEN_MASK		(1<<1)
#define M98090_IN5SEEN_SHIFT		1
#define M98090_IN5SEEN_WIDTH		1
#define M98090_IN6SEEN_MASK		(1<<0)
#define M98090_IN6SEEN_SHIFT		0
#define M98090_IN6SEEN_WIDTH		1

/*
 * M98090_REG_LINE_INPUT_LEVEL
 */
#define M98090_MIXG135_MASK		(1<<7)
#define M98090_MIXG135_SHIFT		7
#define M98090_MIXG135_WIDTH		1
#define M98090_MIXG135_NUM		(1<<M98090_MIXG135_WIDTH)
#define M98090_MIXG246_MASK		(1<<6)
#define M98090_MIXG246_SHIFT		6
#define M98090_MIXG246_WIDTH		1
#define M98090_MIXG246_NUM		(1<<M98090_MIXG246_WIDTH)
#define M98090_LINAPGA_MASK		(7<<3)
#define M98090_LINAPGA_SHIFT		3
#define M98090_LINAPGA_WIDTH		3
#define M98090_LINAPGA_NUM		6
#define M98090_LINBPGA_MASK		(7<<0)
#define M98090_LINBPGA_SHIFT		0
#define M98090_LINBPGA_WIDTH		3
#define M98090_LINBPGA_NUM		6

/*
 * M98090_REG_INPUT_MODE
 */
#define M98090_EXTBUFA_MASK		(1<<7)
#define M98090_EXTBUFA_SHIFT		7
#define M98090_EXTBUFA_WIDTH		1
#define M98090_EXTBUFA_NUM		(1<<M98090_EXTBUFA_WIDTH)
#define M98090_EXTBUFB_MASK		(1<<6)
#define M98090_EXTBUFB_SHIFT		6
#define M98090_EXTBUFB_WIDTH		1
#define M98090_EXTBUFB_NUM		(1<<M98090_EXTBUFB_WIDTH)
#define M98090_EXTMIC_MASK		(3<<0)
#define M98090_EXTMIC_SHIFT		0
#define M98090_EXTMIC1_SHIFT		0
#define M98090_EXTMIC2_SHIFT		1
#define M98090_EXTMIC_WIDTH		2
#define M98090_EXTMIC_NONE		(0<<0)
#define M98090_EXTMIC_MIC1		(1<<0)
#define M98090_EXTMIC_MIC2		(2<<0)

/*
 * M98090_REG_MIC1_INPUT_LEVEL
 */
#define M98090_MIC_PA1EN_MASK		(3<<5)
#define M98090_MIC_PA1EN_SHIFT		5
#define M98090_MIC_PA1EN_WIDTH		2
#define M98090_MIC_PA1EN_NUM		3
#define M98090_MIC_PGAM1_MASK		(31<<0)
#define M98090_MIC_PGAM1_SHIFT		0
#define M98090_MIC_PGAM1_WIDTH		5
#define M98090_MIC_PGAM1_NUM		21

/*
 * M98090_REG_MIC2_INPUT_LEVEL
 */
#define M98090_MIC_PA2EN_MASK		(3<<5)
#define M98090_MIC_PA2EN_SHIFT		5
#define M98090_MIC_PA2EN_WIDTH		2
#define M98090_MIC_PA2EN_NUM		3
#define M98090_MIC_PGAM2_MASK		(31<<0)
#define M98090_MIC_PGAM2_SHIFT		0
#define M98090_MIC_PGAM2_WIDTH		5
#define M98090_MIC_PGAM2_NUM		21

/*
 * M98090_REG_MIC_BIAS_VOLTAGE
 */
#define M98090_MBVSEL_MASK		(3<<0)
#define M98090_MBVSEL_SHIFT		0
#define M98090_MBVSEL_WIDTH		2
#define M98090_MBVSEL_2V8		(3<<0)
#define M98090_MBVSEL_2V55		(2<<0)
#define M98090_MBVSEL_2V4		(1<<0)
#define M98090_MBVSEL_2V2		(0<<0)

/*
 * M98090_REG_DIGITAL_MIC_ENABLE
 */
#define M98090_MICCLK_MASK		(7<<4)
#define M98090_MICCLK_SHIFT		4
#define M98090_MICCLK_WIDTH		3
#define M98090_DIGMIC4_MASK		(1<<3)
#define M98090_DIGMIC4_SHIFT		3
#define M98090_DIGMIC4_WIDTH		1
#define M98090_DIGMIC4_NUM		(1<<M98090_DIGMIC4_WIDTH)
#define M98090_DIGMIC3_MASK		(1<<2)
#define M98090_DIGMIC3_SHIFT		2
#define M98090_DIGMIC3_WIDTH		1
#define M98090_DIGMIC3_NUM		(1<<M98090_DIGMIC3_WIDTH)
#define M98090_DIGMICR_MASK		(1<<1)
#define M98090_DIGMICR_SHIFT		1
#define M98090_DIGMICR_WIDTH		1
#define M98090_DIGMICR_NUM		(1<<M98090_DIGMICR_WIDTH)
#define M98090_DIGMICL_MASK		(1<<0)
#define M98090_DIGMICL_SHIFT		0
#define M98090_DIGMICL_WIDTH		1
#define M98090_DIGMICL_NUM		(1<<M98090_DIGMICL_WIDTH)

/*
 * M98090_REG_DIGITAL_MIC_CONFIG
 */
#define M98090_DMIC_COMP_MASK		(15<<4)
#define M98090_DMIC_COMP_SHIFT		4
#define M98090_DMIC_COMP_WIDTH		4
#define M98090_DMIC_COMP_NUM		(1<<M98090_DMIC_COMP_WIDTH)
#define M98090_DMIC_FREQ_MASK		(3<<0)
#define M98090_DMIC_FREQ_SHIFT		0
#define M98090_DMIC_FREQ_WIDTH		2

/*
 * M98090_REG_LEFT_ADC_MIXER
 */
#define M98090_MIXADL_MIC2_MASK		(1<<6)
#define M98090_MIXADL_MIC2_SHIFT	6
#define M98090_MIXADL_MIC2_WIDTH	1
#define M98090_MIXADL_MIC1_MASK		(1<<5)
#define M98090_MIXADL_MIC1_SHIFT	5
#define M98090_MIXADL_MIC1_WIDTH	1
#define M98090_MIXADL_LINEB_MASK	(1<<4)
#define M98090_MIXADL_LINEB_SHIFT	4
#define M98090_MIXADL_LINEB_WIDTH	1
#define M98090_MIXADL_LINEA_MASK	(1<<3)
#define M98090_MIXADL_LINEA_SHIFT	3
#define M98090_MIXADL_LINEA_WIDTH	1
#define M98090_MIXADL_IN65DIFF_MASK	(1<<2)
#define M98090_MIXADL_IN65DIFF_SHIFT	2
#define M98090_MIXADL_IN65DIFF_WIDTH	1
#define M98090_MIXADL_IN34DIFF_MASK	(1<<1)
#define M98090_MIXADL_IN34DIFF_SHIFT	1
#define M98090_MIXADL_IN34DIFF_WIDTH	1
#define M98090_MIXADL_IN12DIFF_MASK	(1<<0)
#define M98090_MIXADL_IN12DIFF_SHIFT	0
#define M98090_MIXADL_IN12DIFF_WIDTH	1
#define M98090_MIXADL_MASK		(255<<0)
#define M98090_MIXADL_SHIFT		0
#define M98090_MIXADL_WIDTH		8

/*
 * M98090_REG_RIGHT_ADC_MIXER
 */
#define M98090_MIXADR_MIC2_MASK		(1<<6)
#define M98090_MIXADR_MIC2_SHIFT	6
#define M98090_MIXADR_MIC2_WIDTH	1
#define M98090_MIXADR_MIC1_MASK		(1<<5)
#define M98090_MIXADR_MIC1_SHIFT	5
#define M98090_MIXADR_MIC1_WIDTH	1
#define M98090_MIXADR_LINEB_MASK	(1<<4)
#define M98090_MIXADR_LINEB_SHIFT	4
#define M98090_MIXADR_LINEB_WIDTH	1
#define M98090_MIXADR_LINEA_MASK	(1<<3)
#define M98090_MIXADR_LINEA_SHIFT	3
#define M98090_MIXADR_LINEA_WIDTH	1
#define M98090_MIXADR_IN65DIFF_MASK	(1<<2)
#define M98090_MIXADR_IN65DIFF_SHIFT	2
#define M98090_MIXADR_IN65DIFF_WIDTH	1
#define M98090_MIXADR_IN34DIFF_MASK	(1<<1)
#define M98090_MIXADR_IN34DIFF_SHIFT	1
#define M98090_MIXADR_IN34DIFF_WIDTH	1
#define M98090_MIXADR_IN12DIFF_MASK	(1<<0)
#define M98090_MIXADR_IN12DIFF_SHIFT	0
#define M98090_MIXADR_IN12DIFF_WIDTH	1
#define M98090_MIXADR_MASK		(255<<0)
#define M98090_MIXADR_SHIFT		0
#define M98090_MIXADR_WIDTH		8

/*
 * M98090_REG_LEFT_ADC_LEVEL
 */
#define M98090_AVLG_MASK		(7<<4)
#define M98090_AVLG_SHIFT		4
#define M98090_AVLG_WIDTH		3
#define M98090_AVLG_NUM			(1<<M98090_AVLG_WIDTH)
#define M98090_AVL_MASK			(15<<0)
#define M98090_AVL_SHIFT		0
#define M98090_AVL_WIDTH		4
#define M98090_AVL_NUM			(1<<M98090_AVL_WIDTH)

/*
 * M98090_REG_RIGHT_ADC_LEVEL
 */
#define M98090_AVRG_MASK		(7<<4)
#define M98090_AVRG_SHIFT		4
#define M98090_AVRG_WIDTH		3
#define M98090_AVRG_NUM			(1<<M98090_AVRG_WIDTH)
#define M98090_AVR_MASK			(15<<0)
#define M98090_AVR_SHIFT		0
#define M98090_AVR_WIDTH		4
#define M98090_AVR_NUM			(1<<M98090_AVR_WIDTH)

/*
 * M98090_REG_ADC_BIQUAD_LEVEL
 */
#define M98090_AVBQ_MASK		(15<<0)
#define M98090_AVBQ_SHIFT		0
#define M98090_AVBQ_WIDTH		4
#define M98090_AVBQ_NUM			(1<<M98090_AVBQ_WIDTH)

/*
 * M98090_REG_ADC_SIDETONE
 */
#define M98090_DSTSR_MASK		(1<<7)
#define M98090_DSTSR_SHIFT		7
#define M98090_DSTSR_WIDTH		1
#define M98090_DSTSL_MASK		(1<<6)
#define M98090_DSTSL_SHIFT		6
#define M98090_DSTSL_WIDTH		1
#define M98090_DVST_MASK		(31<<0)
#define M98090_DVST_SHIFT		0
#define M98090_DVST_WIDTH		5
#define M98090_DVST_NUM			31

/*
 * M98090_REG_SYSTEM_CLOCK
 */
#define M98090_PSCLK_MASK		(3<<4)
#define M98090_PSCLK_SHIFT		4
#define M98090_PSCLK_WIDTH		2
#define M98090_PSCLK_DISABLED		(0<<4)
#define M98090_PSCLK_DIV1		(1<<4)
#define M98090_PSCLK_DIV2		(2<<4)
#define M98090_PSCLK_DIV4		(3<<4)

/*
 * M98090_REG_CLOCK_MODE
 */
#define M98090_FREQ_MASK		(15<<4)
#define M98090_FREQ_SHIFT		4
#define M98090_FREQ_WIDTH		4
#define M98090_USE_M1_MASK		(1<<0)
#define M98090_USE_M1_SHIFT		0
#define M98090_USE_M1_WIDTH		1
#define M98090_USE_M1_NUM		(1<<M98090_USE_M1_WIDTH)

/*
 * M98090_REG_CLOCK_RATIO_NI_MSB
 */
#define M98090_NI_HI_MASK		(127<<0)
#define M98090_NI_HI_SHIFT		0
#define M98090_NI_HI_WIDTH		7
#define M98090_NI_HI_NUM		(1<<M98090_NI_HI_WIDTH)

/*
 * M98090_REG_CLOCK_RATIO_NI_LSB
 */
#define M98090_NI_LO_MASK		(255<<0)
#define M98090_NI_LO_SHIFT		0
#define M98090_NI_LO_WIDTH		8
#define M98090_NI_LO_NUM		(1<<M98090_NI_LO_WIDTH)

/*
 * M98090_REG_CLOCK_RATIO_MI_MSB
 */
#define M98090_MI_HI_MASK		(255<<0)
#define M98090_MI_HI_SHIFT		0
#define M98090_MI_HI_WIDTH		8
#define M98090_MI_HI_NUM		(1<<M98090_MI_HI_WIDTH)

/*
 * M98090_REG_CLOCK_RATIO_MI_LSB
 */
#define M98090_MI_LO_MASK		(255<<0)
#define M98090_MI_LO_SHIFT		0
#define M98090_MI_LO_WIDTH		8
#define M98090_MI_LO_NUM		(1<<M98090_MI_LO_WIDTH)

/*
 * M98090_REG_MASTER_MODE
 */
#define M98090_MAS_MASK			(1<<7)
#define M98090_MAS_SHIFT		7
#define M98090_MAS_WIDTH		1
#define M98090_BSEL_MASK		(1<<0)
#define M98090_BSEL_SHIFT		0
#define M98090_BSEL_WIDTH		1
#define M98090_BSEL_32			(1<<0)
#define M98090_BSEL_48			(2<<0)
#define M98090_BSEL_64			(3<<0)

/*
 * M98090_REG_INTERFACE_FORMAT
 */
#define M98090_RJ_MASK			(1<<5)
#define M98090_RJ_SHIFT			5
#define M98090_RJ_WIDTH			1
#define M98090_WCI_MASK			(1<<4)
#define M98090_WCI_SHIFT		4
#define M98090_WCI_WIDTH		1
#define M98090_BCI_MASK			(1<<3)
#define M98090_BCI_SHIFT		3
#define M98090_BCI_WIDTH		1
#define M98090_DLY_MASK			(1<<2)
#define M98090_DLY_SHIFT		2
#define M98090_DLY_WIDTH		1
#define M98090_WS_MASK			(3<<0)
#define M98090_WS_SHIFT			0
#define M98090_WS_WIDTH			2
#define M98090_WS_NUM			(1<<M98090_WS_WIDTH)

/*
 * M98090_REG_TDM_CONTROL
 */
#define M98090_FSW_MASK			(1<<1)
#define M98090_FSW_SHIFT		1
#define M98090_FSW_WIDTH		1
#define M98090_TDM_MASK			(1<<0)
#define M98090_TDM_SHIFT		0
#define M98090_TDM_WIDTH		1
#define M98090_TDM_NUM			(1<<M98090_TDM_WIDTH)

/*
 * M98090_REG_TDM_FORMAT
 */
#define M98090_TDM_SLOTL_MASK		(3<<6)
#define M98090_TDM_SLOTL_SHIFT		6
#define M98090_TDM_SLOTL_WIDTH		2
#define M98090_TDM_SLOTL_NUM		(1<<M98090_TDM_SLOTL_WIDTH)
#define M98090_TDM_SLOTR_MASK		(3<<4)
#define M98090_TDM_SLOTR_SHIFT		4
#define M98090_TDM_SLOTR_WIDTH		2
#define M98090_TDM_SLOTR_NUM		(1<<M98090_TDM_SLOTR_WIDTH)
#define M98090_TDM_SLOTDLY_MASK		(15<<0)
#define M98090_TDM_SLOTDLY_SHIFT	0
#define M98090_TDM_SLOTDLY_WIDTH	4
#define M98090_TDM_SLOTDLY_NUM		(1<<M98090_TDM_SLOTDLY_WIDTH)

/*
 * M98090_REG_IO_CONFIGURATION
 */
#define M98090_LTEN_MASK		(1<<5)
#define M98090_LTEN_SHIFT		5
#define M98090_LTEN_WIDTH		1
#define M98090_LTEN_NUM			(1<<M98090_LTEN_WIDTH)
#define M98090_LBEN_MASK		(1<<4)
#define M98090_LBEN_SHIFT		4
#define M98090_LBEN_WIDTH		1
#define M98090_LBEN_NUM			(1<<M98090_LBEN_WIDTH)
#define M98090_DMONO_MASK		(1<<3)
#define M98090_DMONO_SHIFT		3
#define M98090_DMONO_WIDTH		1
#define M98090_DMONO_NUM		(1<<M98090_DMONO_WIDTH)
#define M98090_HIZOFF_MASK		(1<<2)
#define M98090_HIZOFF_SHIFT		2
#define M98090_HIZOFF_WIDTH		1
#define M98090_HIZOFF_NUM		(1<<M98090_HIZOFF_WIDTH)
#define M98090_SDOEN_MASK		(1<<1)
#define M98090_SDOEN_SHIFT		1
#define M98090_SDOEN_WIDTH		1
#define M98090_SDOEN_NUM		(1<<M98090_SDOEN_WIDTH)
#define M98090_SDIEN_MASK		(1<<0)
#define M98090_SDIEN_SHIFT		0
#define M98090_SDIEN_WIDTH		1
#define M98090_SDIEN_NUM		(1<<M98090_SDIEN_WIDTH)

/*
 * M98090_REG_FILTER_CONFIG
 */
#define M98090_MODE_MASK		(1<<7)
#define M98090_MODE_SHIFT		7
#define M98090_MODE_WIDTH		1
#define M98090_AHPF_MASK		(1<<6)
#define M98090_AHPF_SHIFT		6
#define M98090_AHPF_WIDTH		1
#define M98090_AHPF_NUM			(1<<M98090_AHPF_WIDTH)
#define M98090_DHPF_MASK		(1<<5)
#define M98090_DHPF_SHIFT		5
#define M98090_DHPF_WIDTH		1
#define M98090_DHPF_NUM			(1<<M98090_DHPF_WIDTH)
#define M98090_DHF_MASK			(1<<4)
#define M98090_DHF_SHIFT		4
#define M98090_DHF_WIDTH		1
#define M98090_FLT_DMIC34MODE_MASK	(1<<3)
#define M98090_FLT_DMIC34MODE_SHIFT	3
#define M98090_FLT_DMIC34MODE_WIDTH	1
#define M98090_FLT_DMIC34HPF_MASK	(1<<2)
#define M98090_FLT_DMIC34HPF_SHIFT	2
#define M98090_FLT_DMIC34HPF_WIDTH	1
#define M98090_FLT_DMIC34HPF_NUM	(1<<M98090_FLT_DMIC34HPF_WIDTH)

/*
 * M98090_REG_DAI_PLAYBACK_LEVEL
 */
#define M98090_DVM_MASK			(1<<7)
#define M98090_DVM_SHIFT		7
#define M98090_DVM_WIDTH		1
#define M98090_DVG_MASK			(3<<4)
#define M98090_DVG_SHIFT		4
#define M98090_DVG_WIDTH		2
#define M98090_DVG_NUM			(1<<M98090_DVG_WIDTH)
#define M98090_DV_MASK			(15<<0)
#define M98090_DV_SHIFT			0
#define M98090_DV_WIDTH			4
#define M98090_DV_NUM			(1<<M98090_DV_WIDTH)

/*
 * M98090_REG_DAI_PLAYBACK_LEVEL_EQ
 */
#define M98090_EQCLPN_MASK		(1<<4)
#define M98090_EQCLPN_SHIFT		4
#define M98090_EQCLPN_WIDTH		1
#define M98090_EQCLPN_NUM		(1<<M98090_EQCLPN_WIDTH)
#define M98090_DVEQ_MASK		(15<<0)
#define M98090_DVEQ_SHIFT		0
#define M98090_DVEQ_WIDTH		4
#define M98090_DVEQ_NUM			(1<<M98090_DVEQ_WIDTH)

/*
 * M98090_REG_LEFT_HP_MIXER
 */
#define M98090_MIXHPL_MIC2_MASK		(1<<5)
#define M98090_MIXHPL_MIC2_SHIFT	5
#define M98090_MIXHPL_MIC2_WIDTH	1
#define M98090_MIXHPL_MIC1_MASK		(1<<4)
#define M98090_MIXHPL_MIC1_SHIFT	4
#define M98090_MIXHPL_MIC1_WIDTH	1
#define M98090_MIXHPL_LINEB_MASK	(1<<3)
#define M98090_MIXHPL_LINEB_SHIFT	3
#define M98090_MIXHPL_LINEB_WIDTH	1
#define M98090_MIXHPL_LINEA_MASK	(1<<2)
#define M98090_MIXHPL_LINEA_SHIFT	2
#define M98090_MIXHPL_LINEA_WIDTH	1
#define M98090_MIXHPL_DACR_MASK		(1<<1)
#define M98090_MIXHPL_DACR_SHIFT	1
#define M98090_MIXHPL_DACR_WIDTH	1
#define M98090_MIXHPL_DACL_MASK		(1<<0)
#define M98090_MIXHPL_DACL_SHIFT	0
#define M98090_MIXHPL_DACL_WIDTH	1
#define M98090_MIXHPL_MASK		(63<<0)
#define M98090_MIXHPL_SHIFT		0
#define M98090_MIXHPL_WIDTH		6

/*
 * M98090_REG_RIGHT_HP_MIXER
 */
#define M98090_MIXHPR_MIC2_MASK		(1<<5)
#define M98090_MIXHPR_MIC2_SHIFT	5
#define M98090_MIXHPR_MIC2_WIDTH	1
#define M98090_MIXHPR_MIC1_MASK		(1<<4)
#define M98090_MIXHPR_MIC1_SHIFT	4
#define M98090_MIXHPR_MIC1_WIDTH	1
#define M98090_MIXHPR_LINEB_MASK	(1<<3)
#define M98090_MIXHPR_LINEB_SHIFT	3
#define M98090_MIXHPR_LINEB_WIDTH	1
#define M98090_MIXHPR_LINEA_MASK	(1<<2)
#define M98090_MIXHPR_LINEA_SHIFT	2
#define M98090_MIXHPR_LINEA_WIDTH	1
#define M98090_MIXHPR_DACR_MASK		(1<<1)
#define M98090_MIXHPR_DACR_SHIFT	1
#define M98090_MIXHPR_DACR_WIDTH	1
#define M98090_MIXHPR_DACL_MASK		(1<<0)
#define M98090_MIXHPR_DACL_SHIFT	0
#define M98090_MIXHPR_DACL_WIDTH	1
#define M98090_MIXHPR_MASK		(63<<0)
#define M98090_MIXHPR_SHIFT		0
#define M98090_MIXHPR_WIDTH		6

/*
 * M98090_REG_HP_CONTROL
 */
#define M98090_MIXHPRSEL_MASK		(1<<5)
#define M98090_MIXHPRSEL_SHIFT		5
#define M98090_MIXHPRSEL_WIDTH		1
#define M98090_MIXHPLSEL_MASK		(1<<4)
#define M98090_MIXHPLSEL_SHIFT		4
#define M98090_MIXHPLSEL_WIDTH		1
#define M98090_MIXHPRG_MASK		(3<<2)
#define M98090_MIXHPRG_SHIFT		2
#define M98090_MIXHPRG_WIDTH		2
#define M98090_MIXHPRG_NUM		(1<<M98090_MIXHPRG_WIDTH)
#define M98090_MIXHPLG_MASK		(3<<0)
#define M98090_MIXHPLG_SHIFT		0
#define M98090_MIXHPLG_WIDTH		2
#define M98090_MIXHPLG_NUM		(1<<M98090_MIXHPLG_WIDTH)

/*
 * M98090_REG_LEFT_HP_VOLUME
 */
#define M98090_HPLM_MASK		(1<<7)
#define M98090_HPLM_SHIFT		7
#define M98090_HPLM_WIDTH		1
#define M98090_HPVOLL_MASK		(31<<0)
#define M98090_HPVOLL_SHIFT		0
#define M98090_HPVOLL_WIDTH		5
#define M98090_HPVOLL_NUM		(1<<M98090_HPVOLL_WIDTH)

/*
 * M98090_REG_RIGHT_HP_VOLUME
 */
#define M98090_HPRM_MASK		(1<<7)
#define M98090_HPRM_SHIFT		7
#define M98090_HPRM_WIDTH		1
#define M98090_HPVOLR_MASK		(31<<0)
#define M98090_HPVOLR_SHIFT		0
#define M98090_HPVOLR_WIDTH		5
#define M98090_HPVOLR_NUM		(1<<M98090_HPVOLR_WIDTH)

/*
 * M98090_REG_LEFT_SPK_MIXER
 */
#define M98090_MIXSPL_MIC2_MASK		(1<<5)
#define M98090_MIXSPL_MIC2_SHIFT	5
#define M98090_MIXSPL_MIC2_WIDTH	1
#define M98090_MIXSPL_MIC1_MASK		(1<<4)
#define M98090_MIXSPL_MIC1_SHIFT	4
#define M98090_MIXSPL_MIC1_WIDTH	1
#define M98090_MIXSPL_LINEB_MASK	(1<<3)
#define M98090_MIXSPL_LINEB_SHIFT	3
#define M98090_MIXSPL_LINEB_WIDTH	1
#define M98090_MIXSPL_LINEA_MASK	(1<<2)
#define M98090_MIXSPL_LINEA_SHIFT	2
#define M98090_MIXSPL_LINEA_WIDTH	1
#define M98090_MIXSPL_DACR_MASK		(1<<1)
#define M98090_MIXSPL_DACR_SHIFT	1
#define M98090_MIXSPL_DACR_WIDTH	1
#define M98090_MIXSPL_DACL_MASK		(1<<0)
#define M98090_MIXSPL_DACL_SHIFT	0
#define M98090_MIXSPL_DACL_WIDTH	1
#define M98090_MIXSPL_MASK		(63<<0)
#define M98090_MIXSPL_SHIFT		0
#define M98090_MIXSPL_WIDTH		6
#define M98090_MIXSPR_DACR_MASK		(1<<1)
#define M98090_MIXSPR_DACR_SHIFT	1
#define M98090_MIXSPR_DACR_WIDTH	1


/*
 * M98090_REG_RIGHT_SPK_MIXER
 */
#define M98090_SPK_SLAVE_MASK		(1<<6)
#define M98090_SPK_SLAVE_SHIFT		6
#define M98090_SPK_SLAVE_WIDTH		1
#define M98090_MIXSPR_MIC2_MASK		(1<<5)
#define M98090_MIXSPR_MIC2_SHIFT	5
#define M98090_MIXSPR_MIC2_WIDTH	1
#define M98090_MIXSPR_MIC1_MASK		(1<<4)
#define M98090_MIXSPR_MIC1_SHIFT	4
#define M98090_MIXSPR_MIC1_WIDTH	1
#define M98090_MIXSPR_LINEB_MASK	(1<<3)
#define M98090_MIXSPR_LINEB_SHIFT	3
#define M98090_MIXSPR_LINEB_WIDTH	1
#define M98090_MIXSPR_LINEA_MASK	(1<<2)
#define M98090_MIXSPR_LINEA_SHIFT	2
#define M98090_MIXSPR_LINEA_WIDTH	1
#define M98090_MIXSPR_DACR_MASK		(1<<1)
#define M98090_MIXSPR_DACR_SHIFT	1
#define M98090_MIXSPR_DACR_WIDTH	1
#define M98090_MIXSPR_DACL_MASK		(1<<0)
#define M98090_MIXSPR_DACL_SHIFT	0
#define M98090_MIXSPR_DACL_WIDTH	1
#define M98090_MIXSPR_MASK		(63<<0)
#define M98090_MIXSPR_SHIFT		0
#define M98090_MIXSPR_WIDTH		6

/*
 * M98090_REG_SPK_CONTROL
 */
#define M98090_MIXSPRG_MASK		(3<<2)
#define M98090_MIXSPRG_SHIFT		2
#define M98090_MIXSPRG_WIDTH		2
#define M98090_MIXSPRG_NUM		(1<<M98090_MIXSPRG_WIDTH)
#define M98090_MIXSPLG_MASK		(3<<0)
#define M98090_MIXSPLG_SHIFT		0
#define M98090_MIXSPLG_WIDTH		2
#define M98090_MIXSPLG_NUM		(1<<M98090_MIXSPLG_WIDTH)

/*
 * M98090_REG_LEFT_SPK_VOLUME
 */
#define M98090_SPLM_MASK		(1<<7)
#define M98090_SPLM_SHIFT		7
#define M98090_SPLM_WIDTH		1
#define M98090_SPVOLL_MASK		(63<<0)
#define M98090_SPVOLL_SHIFT		0
#define M98090_SPVOLL_WIDTH		6
#define M98090_SPVOLL_NUM		40

/*
 * M98090_REG_RIGHT_SPK_VOLUME
 */
#define M98090_SPRM_MASK		(1<<7)
#define M98090_SPRM_SHIFT		7
#define M98090_SPRM_WIDTH		1
#define M98090_SPVOLR_MASK		(63<<0)
#define M98090_SPVOLR_SHIFT		0
#define M98090_SPVOLR_WIDTH		6
#define M98090_SPVOLR_NUM		40

/*
 * M98090_REG_DRC_TIMING
 */
#define M98090_DRCEN_MASK		(1<<7)
#define M98090_DRCEN_SHIFT		7
#define M98090_DRCEN_WIDTH		1
#define M98090_DRCEN_NUM		(1<<M98090_DRCEN_WIDTH)
#define M98090_DRCRLS_MASK		(7<<4)
#define M98090_DRCRLS_SHIFT		4
#define M98090_DRCRLS_WIDTH		3
#define M98090_DRCATK_MASK		(7<<0)
#define M98090_DRCATK_SHIFT		0
#define M98090_DRCATK_WIDTH		3

/*
 * M98090_REG_DRC_COMPRESSOR
 */
#define M98090_DRCCMP_MASK		(7<<5)
#define M98090_DRCCMP_SHIFT		5
#define M98090_DRCCMP_WIDTH		3
#define M98090_DRCTHC_MASK		(31<<0)
#define M98090_DRCTHC_SHIFT		0
#define M98090_DRCTHC_WIDTH		5
#define M98090_DRCTHC_NUM		(1<<M98090_DRCTHC_WIDTH)

/*
 * M98090_REG_DRC_EXPANDER
 */
#define M98090_DRCEXP_MASK		(7<<5)
#define M98090_DRCEXP_SHIFT		5
#define M98090_DRCEXP_WIDTH		3
#define M98090_DRCTHE_MASK		(31<<0)
#define M98090_DRCTHE_SHIFT		0
#define M98090_DRCTHE_WIDTH		5
#define M98090_DRCTHE_NUM		(1<<M98090_DRCTHE_WIDTH)

/*
 * M98090_REG_DRC_GAIN
 */
#define M98090_DRCG_MASK		(31<<0)
#define M98090_DRCG_SHIFT		0
#define M98090_DRCG_WIDTH		5
#define M98090_DRCG_NUM			13

/*
 * M98090_REG_RCV_LOUTL_MIXER
 */
#define M98090_MIXRCVL_MIC2_MASK	(1<<5)
#define M98090_MIXRCVL_MIC2_SHIFT	5
#define M98090_MIXRCVL_MIC2_WIDTH	1
#define M98090_MIXRCVL_MIC1_MASK	(1<<4)
#define M98090_MIXRCVL_MIC1_SHIFT	4
#define M98090_MIXRCVL_MIC1_WIDTH	1
#define M98090_MIXRCVL_LINEB_MASK	(1<<3)
#define M98090_MIXRCVL_LINEB_SHIFT	3
#define M98090_MIXRCVL_LINEB_WIDTH	1
#define M98090_MIXRCVL_LINEA_MASK	(1<<2)
#define M98090_MIXRCVL_LINEA_SHIFT	2
#define M98090_MIXRCVL_LINEA_WIDTH	1
#define M98090_MIXRCVL_DACR_MASK	(1<<1)
#define M98090_MIXRCVL_DACR_SHIFT	1
#define M98090_MIXRCVL_DACR_WIDTH	1
#define M98090_MIXRCVL_DACL_MASK	(1<<0)
#define M98090_MIXRCVL_DACL_SHIFT	0
#define M98090_MIXRCVL_DACL_WIDTH	1
#define M98090_MIXRCVL_MASK		(63<<0)
#define M98090_MIXRCVL_SHIFT		0
#define M98090_MIXRCVL_WIDTH		6

/*
 * M98090_REG_RCV_LOUTL_CONTROL
 */
#define M98090_MIXRCVLG_MASK		(3<<0)
#define M98090_MIXRCVLG_SHIFT		0
#define M98090_MIXRCVLG_WIDTH		2
#define M98090_MIXRCVLG_NUM		(1<<M98090_MIXRCVLG_WIDTH)

/*
 * M98090_REG_RCV_LOUTL_VOLUME
 */
#define M98090_RCVLM_MASK		(1<<7)
#define M98090_RCVLM_SHIFT		7
#define M98090_RCVLM_WIDTH		1
#define M98090_RCVLVOL_MASK		(31<<0)
#define M98090_RCVLVOL_SHIFT		0
#define M98090_RCVLVOL_WIDTH		5
#define M98090_RCVLVOL_NUM		(1<<M98090_RCVLVOL_WIDTH)

/*
 * M98090_REG_LOUTR_MIXER
 */
#define M98090_LINMOD_MASK		(1<<7)
#define M98090_LINMOD_SHIFT		7
#define M98090_LINMOD_WIDTH		1
#define M98090_MIXRCVR_MIC2_MASK	(1<<5)
#define M98090_MIXRCVR_MIC2_SHIFT	5
#define M98090_MIXRCVR_MIC2_WIDTH	1
#define M98090_MIXRCVR_MIC1_MASK	(1<<4)
#define M98090_MIXRCVR_MIC1_SHIFT	4
#define M98090_MIXRCVR_MIC1_WIDTH	1
#define M98090_MIXRCVR_LINEB_MASK	(1<<3)
#define M98090_MIXRCVR_LINEB_SHIFT	3
#define M98090_MIXRCVR_LINEB_WIDTH	1
#define M98090_MIXRCVR_LINEA_MASK	(1<<2)
#define M98090_MIXRCVR_LINEA_SHIFT	2
#define M98090_MIXRCVR_LINEA_WIDTH	1
#define M98090_MIXRCVR_DACR_MASK	(1<<1)
#define M98090_MIXRCVR_DACR_SHIFT	1
#define M98090_MIXRCVR_DACR_WIDTH	1
#define M98090_MIXRCVR_DACL_MASK	(1<<0)
#define M98090_MIXRCVR_DACL_SHIFT	0
#define M98090_MIXRCVR_DACL_WIDTH	1
#define M98090_MIXRCVR_MASK		(63<<0)
#define M98090_MIXRCVR_SHIFT		0
#define M98090_MIXRCVR_WIDTH		6

/*
 * M98090_REG_LOUTR_CONTROL
 */
#define M98090_MIXRCVRG_MASK		(3<<0)
#define M98090_MIXRCVRG_SHIFT		0
#define M98090_MIXRCVRG_WIDTH		2
#define M98090_MIXRCVRG_NUM		(1<<M98090_MIXRCVRG_WIDTH)

/*
 * M98090_REG_LOUTR_VOLUME
 */
#define M98090_RCVRM_MASK		(1<<7)
#define M98090_RCVRM_SHIFT		7
#define M98090_RCVRM_WIDTH		1
#define M98090_RCVRVOL_MASK		(31<<0)
#define M98090_RCVRVOL_SHIFT		0
#define M98090_RCVRVOL_WIDTH		5
#define M98090_RCVRVOL_NUM		(1<<M98090_RCVRVOL_WIDTH)

/*
 * M98090_REG_JACK_DETECT
 */
#define M98090_JDETEN_MASK		(1<<7)
#define M98090_JDETEN_SHIFT		7
#define M98090_JDETEN_WIDTH		1
#define M98090_JDWK_MASK		(1<<6)
#define M98090_JDWK_SHIFT		6
#define M98090_JDWK_WIDTH		1
#define M98090_JDEB_MASK		(3<<0)
#define M98090_JDEB_SHIFT		0
#define M98090_JDEB_WIDTH		2
#define M98090_JDEB_25MS		(0<<0)
#define M98090_JDEB_50MS		(1<<0)
#define M98090_JDEB_100MS		(2<<0)
#define M98090_JDEB_200MS		(3<<0)

/*
 * M98090_REG_INPUT_ENABLE
 */
#define M98090_MBEN_MASK		(1<<4)
#define M98090_MBEN_SHIFT		4
#define M98090_MBEN_WIDTH		1
#define M98090_LINEAEN_MASK		(1<<3)
#define M98090_LINEAEN_SHIFT		3
#define M98090_LINEAEN_WIDTH		1
#define M98090_LINEBEN_MASK		(1<<2)
#define M98090_LINEBEN_SHIFT		2
#define M98090_LINEBEN_WIDTH		1
#define M98090_ADREN_MASK		(1<<1)
#define M98090_ADREN_SHIFT		1
#define M98090_ADREN_WIDTH		1
#define M98090_ADLEN_MASK		(1<<0)
#define M98090_ADLEN_SHIFT		0
#define M98090_ADLEN_WIDTH		1

/*
 * M98090_REG_OUTPUT_ENABLE
 */
#define M98090_HPREN_MASK		(1<<7)
#define M98090_HPREN_SHIFT		7
#define M98090_HPREN_WIDTH		1
#define M98090_HPLEN_MASK		(1<<6)
#define M98090_HPLEN_SHIFT		6
#define M98090_HPLEN_WIDTH		1
#define M98090_SPREN_MASK		(1<<5)
#define M98090_SPREN_SHIFT		5
#define M98090_SPREN_WIDTH		1
#define M98090_SPLEN_MASK		(1<<4)
#define M98090_SPLEN_SHIFT		4
#define M98090_SPLEN_WIDTH		1
#define M98090_RCVLEN_MASK		(1<<3)
#define M98090_RCVLEN_SHIFT		3
#define M98090_RCVLEN_WIDTH		1
#define M98090_RCVREN_MASK		(1<<2)
#define M98090_RCVREN_SHIFT		2
#define M98090_RCVREN_WIDTH		1
#define M98090_DAREN_MASK		(1<<1)
#define M98090_DAREN_SHIFT		1
#define M98090_DAREN_WIDTH		1
#define M98090_DALEN_MASK		(1<<0)
#define M98090_DALEN_SHIFT		0
#define M98090_DALEN_WIDTH		1

/*
 * M98090_REG_LEVEL_CONTROL
 */
#define M98090_ZDENN_MASK		(1<<2)
#define M98090_ZDENN_SHIFT		2
#define M98090_ZDENN_WIDTH		1
#define M98090_ZDENN_NUM		(1<<M98090_ZDENN_WIDTH)
#define M98090_VS2ENN_MASK		(1<<1)
#define M98090_VS2ENN_SHIFT		1
#define M98090_VS2ENN_WIDTH		1
#define M98090_VS2ENN_NUM		(1<<M98090_VS2ENN_WIDTH)
#define M98090_VSENN_MASK		(1<<0)
#define M98090_VSENN_SHIFT		0
#define M98090_VSENN_WIDTH		1
#define M98090_VSENN_NUM		(1<<M98090_VSENN_WIDTH)

/*
 * M98090_REG_DSP_FILTER_ENABLE
 */
#define M98090_DMIC34BQEN_MASK		(1<<4)
#define M98090_DMIC34BQEN_SHIFT		4
#define M98090_DMIC34BQEN_WIDTH		1
#define M98090_DMIC34BQEN_NUM		(1<<M98090_DMIC34BQEN_WIDTH)
#define M98090_ADCBQEN_MASK		(1<<3)
#define M98090_ADCBQEN_SHIFT		3
#define M98090_ADCBQEN_WIDTH		1
#define M98090_ADCBQEN_NUM		(1<<M98090_ADCBQEN_WIDTH)
#define M98090_EQ3BANDEN_MASK		(1<<2)
#define M98090_EQ3BANDEN_SHIFT		2
#define M98090_EQ3BANDEN_WIDTH		1
#define M98090_EQ3BANDEN_NUM		(1<<M98090_EQ3BANDEN_WIDTH)
#define M98090_EQ5BANDEN_MASK		(1<<1)
#define M98090_EQ5BANDEN_SHIFT		1
#define M98090_EQ5BANDEN_WIDTH		1
#define M98090_EQ5BANDEN_NUM		(1<<M98090_EQ5BANDEN_WIDTH)
#define M98090_EQ7BANDEN_MASK		(1<<0)
#define M98090_EQ7BANDEN_SHIFT		0
#define M98090_EQ7BANDEN_WIDTH		1
#define M98090_EQ7BANDEN_NUM		(1<<M98090_EQ7BANDEN_WIDTH)

/*
 * M98090_REG_BIAS_CONTROL
 */
#define M98090_VCM_MODE_MASK		(1<<0)
#define M98090_VCM_MODE_SHIFT		0
#define M98090_VCM_MODE_WIDTH		1
#define M98090_VCM_MODE_NUM		(1<<M98090_VCM_MODE_WIDTH)

/*
 * M98090_REG_DAC_CONTROL
 */
#define M98090_PERFMODE_MASK		(1<<1)
#define M98090_PERFMODE_SHIFT		1
#define M98090_PERFMODE_WIDTH		1
#define M98090_PERFMODE_NUM		(1<<M98090_PERFMODE_WIDTH)
#define M98090_DACHP_MASK		(1<<0)
#define M98090_DACHP_SHIFT		0
#define M98090_DACHP_WIDTH		1
#define M98090_DACHP_NUM		(1<<M98090_DACHP_WIDTH)

/*
 * M98090_REG_ADC_CONTROL
 */
#define M98090_OSR128_MASK		(1<<2)
#define M98090_OSR128_SHIFT		2
#define M98090_OSR128_WIDTH		1
#define M98090_ADCDITHER_MASK		(1<<1)
#define M98090_ADCDITHER_SHIFT		1
#define M98090_ADCDITHER_WIDTH		1
#define M98090_ADCDITHER_NUM		(1<<M98090_ADCDITHER_WIDTH)
#define M98090_ADCHP_MASK		(1<<0)
#define M98090_ADCHP_SHIFT		0
#define M98090_ADCHP_WIDTH		1
#define M98090_ADCHP_NUM		(1<<M98090_ADCHP_WIDTH)

/*
 * M98090_REG_DEVICE_SHUTDOWN
 */
#define M98090_SHDNN_MASK		(1<<7)
#define M98090_SHDNN_SHIFT		7
#define M98090_SHDNN_WIDTH		1

/*
 * M98090_REG_EQUALIZER_BASE
 */
#define M98090_B0_1_HI_MASK		(255<<0)
#define M98090_B0_1_HI_SHIFT		0
#define M98090_B0_1_HI_WIDTH		8
#define M98090_B0_1_MID_MASK		(255<<0)
#define M98090_B0_1_MID_SHIFT		0
#define M98090_B0_1_MID_WIDTH		8
#define M98090_B0_1_LO_MASK		(255<<0)
#define M98090_B0_1_LO_SHIFT		0
#define M98090_B0_1_LO_WIDTH		8
#define M98090_B1_1_HI_MASK		(255<<0)
#define M98090_B1_1_HI_SHIFT		0
#define M98090_B1_1_HI_WIDTH		8
#define M98090_B1_1_MID_MASK		(255<<0)
#define M98090_B1_1_MID_SHIFT		0
#define M98090_B1_1_MID_WIDTH		8
#define M98090_B1_1_LO_MASK		(255<<0)
#define M98090_B1_1_LO_SHIFT		0
#define M98090_B1_1_LO_WIDTH		8
#define M98090_B2_1_HI_MASK		(255<<0)
#define M98090_B2_1_HI_SHIFT		0
#define M98090_B2_1_HI_WIDTH		8
#define M98090_B2_1_MID_MASK		(255<<0)
#define M98090_B2_1_MID_SHIFT		0
#define M98090_B2_1_MID_WIDTH		8
#define M98090_B2_1_LO_MASK		(255<<0)
#define M98090_B2_1_LO_SHIFT		0
#define M98090_B2_1_LO_WIDTH		8
#define M98090_A1_1_HI_MASK		(255<<0)
#define M98090_A1_1_HI_SHIFT		0
#define M98090_A1_1_HI_WIDTH		8
#define M98090_A1_1_MID_MASK		(255<<0)
#define M98090_A1_1_MID_SHIFT		0
#define M98090_A1_1_MID_WIDTH		8
#define M98090_A1_1_LO_MASK		(255<<0)
#define M98090_A1_1_LO_SHIFT		0
#define M98090_A1_1_LO_WIDTH		8
#define M98090_A2_1_HI_MASK		(255<<0)
#define M98090_A2_1_HI_SHIFT		0
#define M98090_A2_1_HI_WIDTH		8
#define M98090_A2_1_MID_MASK		(255<<0)
#define M98090_A2_1_MID_SHIFT		0
#define M98090_A2_1_MID_WIDTH		8
#define M98090_A2_1_LO_MASK		(255<<0)
#define M98090_A2_1_LO_SHIFT		0
#define M98090_A2_1_LO_WIDTH		8

#define M98090_COEFS_PER_BAND		5
#define M98090_COEFS_BLK_SZ		(M98090_COEFS_PER_BAND * 3)
#define M98090_COEFS_MAX_SZ		(M98090_COEFS_BLK_SZ * 7)

/*
 * M98090_REG_RECORD_BIQUAD_BASE
 */
#define M98090_REC_B0_HI_MASK		(255<<0)
#define M98090_REC_B0_HI_SHIFT		0
#define M98090_REC_B0_HI_WIDTH		8
#define M98090_REC_B0_MID_MASK		(255<<0)
#define M98090_REC_B0_MID_SHIFT		0
#define M98090_REC_B0_MID_WIDTH		8
#define M98090_REC_B0_LO_MASK		(255<<0)
#define M98090_REC_B0_LO_SHIFT		0
#define M98090_REC_B0_LO_WIDTH		8
#define M98090_REC_B1_HI_MASK		(255<<0)
#define M98090_REC_B1_HI_SHIFT		0
#define M98090_REC_B1_HI_WIDTH		8
#define M98090_REC_B1_MID_MASK		(255<<0)
#define M98090_REC_B1_MID_SHIFT		0
#define M98090_REC_B1_MID_WIDTH		8
#define M98090_REC_B1_LO_MASK		(255<<0)
#define M98090_REC_B1_LO_SHIFT		0
#define M98090_REC_B1_LO_WIDTH		8
#define M98090_REC_B2_HI_MASK		(255<<0)
#define M98090_REC_B2_HI_SHIFT		0
#define M98090_REC_B2_HI_WIDTH		8
#define M98090_REC_B2_MID_MASK		(255<<0)
#define M98090_REC_B2_MID_SHIFT		0
#define M98090_REC_B2_MID_WIDTH		8
#define M98090_REC_B2_LO_MASK		(255<<0)
#define M98090_REC_B2_LO_SHIFT		0
#define M98090_REC_B2_LO_WIDTH		8
#define M98090_REC_A1_HI_MASK		(255<<0)
#define M98090_REC_A1_HI_SHIFT		0
#define M98090_REC_A1_HI_WIDTH		8
#define M98090_REC_A1_MID_MASK		(255<<0)
#define M98090_REC_A1_MID_SHIFT		0
#define M98090_REC_A1_MID_WIDTH		8
#define M98090_REC_A1_LO_MASK		(255<<0)
#define M98090_REC_A1_LO_SHIFT		0
#define M98090_REC_A1_LO_WIDTH		8
#define M98090_REC_A2_HI_MASK		(255<<0)
#define M98090_REC_A2_HI_SHIFT		0
#define M98090_REC_A2_HI_WIDTH		8
#define M98090_REC_A2_MID_MASK		(255<<0)
#define M98090_REC_A2_MID_SHIFT		0
#define M98090_REC_A2_MID_WIDTH		8
#define M98090_REC_A2_LO_MASK		(255<<0)
#define M98090_REC_A2_LO_SHIFT		0
#define M98090_REC_A2_LO_WIDTH		8

/*
 * M98090_REG_DMIC3_VOLUME
 */
#define M98090_DMIC_AV3G_MASK		(7<<4)
#define M98090_DMIC_AV3G_SHIFT		4
#define M98090_DMIC_AV3G_WIDTH		3
#define M98090_DMIC_AV3G_NUM		(1<<M98090_DMIC_AV3G_WIDTH)
#define M98090_DMIC_AV3_MASK		(15<<0)
#define M98090_DMIC_AV3_SHIFT		0
#define M98090_DMIC_AV3_WIDTH		4
#define M98090_DMIC_AV3_NUM		(1<<M98090_DMIC_AV3_WIDTH)

/*
 * M98090_REG_DMIC4_VOLUME
 */
#define M98090_DMIC_AV4G_MASK		(7<<4)
#define M98090_DMIC_AV4G_SHIFT		4
#define M98090_DMIC_AV4G_WIDTH		3
#define M98090_DMIC_AV4G_NUM		(1<<M98090_DMIC_AV4G_WIDTH)
#define M98090_DMIC_AV4_MASK		(15<<0)
#define M98090_DMIC_AV4_SHIFT		0
#define M98090_DMIC_AV4_WIDTH		4
#define M98090_DMIC_AV4_NUM		(1<<M98090_DMIC_AV4_WIDTH)

/*
 * M98090_REG_DMIC34_BQ_PREATTEN
 */
#define M98090_AV34BQ_MASK		(15<<0)
#define M98090_AV34BQ_SHIFT		0
#define M98090_AV34BQ_WIDTH		4
#define M98090_AV34BQ_NUM		(1<<M98090_AV34BQ_WIDTH)

/*
 * M98090_REG_RECORD_TDM_SLOT
 */
#define M98090_TDM_SLOTADCL_MASK	(3<<6)
#define M98090_TDM_SLOTADCL_SHIFT	6
#define M98090_TDM_SLOTADCL_WIDTH	2
#define M98090_TDM_SLOTADCL_NUM		(1<<M98090_TDM_SLOTADCL_WIDTH)
#define M98090_TDM_SLOTADCR_MASK	(3<<4)
#define M98090_TDM_SLOTADCR_SHIFT	4
#define M98090_TDM_SLOTADCR_WIDTH	2
#define M98090_TDM_SLOTADCR_NUM		(1<<M98090_TDM_SLOTADCR_WIDTH)
#define M98090_TDM_SLOTDMIC3_MASK	(3<<2)
#define M98090_TDM_SLOTDMIC3_SHIFT	2
#define M98090_TDM_SLOTDMIC3_WIDTH	2
#define M98090_TDM_SLOTDMIC3_NUM	(1<<M98090_TDM_SLOTDMIC3_WIDTH)
#define M98090_TDM_SLOTDMIC4_MASK	(3<<0)
#define M98090_TDM_SLOTDMIC4_SHIFT	0
#define M98090_TDM_SLOTDMIC4_WIDTH	2
#define M98090_TDM_SLOTDMIC4_NUM	(1<<M98090_TDM_SLOTDMIC4_WIDTH)

/*
 * M98090_REG_SAMPLE_RATE
 */
#define M98090_DMIC34_ZEROPAD_MASK	(1<<4)
#define M98090_DMIC34_ZEROPAD_SHIFT	4
#define M98090_DMIC34_ZEROPAD_WIDTH	1
#define M98090_DMIC34_ZEROPAD_NUM	(1<<M98090_DIGMIC4_WIDTH)
#define M98090_DMIC34_SRDIV_MASK	(7<<0)
#define M98090_DMIC34_SRDIV_SHIFT	0
#define M98090_DMIC34_SRDIV_WIDTH	3

/*
 * M98090_REG_DMIC34_BIQUAD_BASE
 */
#define M98090_DMIC34_B0_HI_MASK	(255<<0)
#define M98090_DMIC34_B0_HI_SHIFT	0
#define M98090_DMIC34_B0_HI_WIDTH	8
#define M98090_DMIC34_B0_MID_MASK	(255<<0)
#define M98090_DMIC34_B0_MID_SHIFT	0
#define M98090_DMIC34_B0_MID_WIDTH	8
#define M98090_DMIC34_B0_LO_MASK	(255<<0)
#define M98090_DMIC34_B0_LO_SHIFT	0
#define M98090_DMIC34_B0_LO_WIDTH	8
#define M98090_DMIC34_B1_HI_MASK	(255<<0)
#define M98090_DMIC34_B1_HI_SHIFT	0
#define M98090_DMIC34_B1_HI_WIDTH	8
#define M98090_DMIC34_B1_MID_MASK	(255<<0)
#define M98090_DMIC34_B1_MID_SHIFT	0
#define M98090_DMIC34_B1_MID_WIDTH	8
#define M98090_DMIC34_B1_LO_MASK	(255<<0)
#define M98090_DMIC34_B1_LO_SHIFT	0
#define M98090_DMIC34_B1_LO_WIDTH	8
#define M98090_DMIC34_B2_HI_MASK	(255<<0)
#define M98090_DMIC34_B2_HI_SHIFT	0
#define M98090_DMIC34_B2_HI_WIDTH	8
#define M98090_DMIC34_B2_MID_MASK	(255<<0)
#define M98090_DMIC34_B2_MID_SHIFT	0
#define M98090_DMIC34_B2_MID_WIDTH	8
#define M98090_DMIC34_B2_LO_MASK	(255<<0)
#define M98090_DMIC34_B2_LO_SHIFT	0
#define M98090_DMIC34_B2_LO_WIDTH	8
#define M98090_DMIC34_A1_HI_MASK	(255<<0)
#define M98090_DMIC34_A1_HI_SHIFT	0
#define M98090_DMIC34_A1_HI_WIDTH	8
#define M98090_DMIC34_A1_MID_MASK	(255<<0)
#define M98090_DMIC34_A1_MID_SHIFT	0
#define M98090_DMIC34_A1_MID_WIDTH	8
#define M98090_DMIC34_A1_LO_MASK	(255<<0)
#define M98090_DMIC34_A1_LO_SHIFT	0
#define M98090_DMIC34_A1_LO_WIDTH	8
#define M98090_DMIC34_A2_HI_MASK	(255<<0)
#define M98090_DMIC34_A2_HI_SHIFT	0
#define M98090_DMIC34_A2_HI_WIDTH	8
#define M98090_DMIC34_A2_MID_MASK	(255<<0)
#define M98090_DMIC34_A2_MID_SHIFT	0
#define M98090_DMIC34_A2_MID_WIDTH	8
#define M98090_DMIC34_A2_LO_MASK	(255<<0)
#define M98090_DMIC34_A2_LO_SHIFT	0
#define M98090_DMIC34_A2_LO_WIDTH	8

#define M98090_JACK_STATE_NO_HEADSET	0
#define M98090_JACK_STATE_NO_HEADSET_2	1
#define M98090_JACK_STATE_HEADPHONE	2
#define M98090_JACK_STATE_HEADSET	3

/*
 * M98090_REG_REVISION_ID
 */
#define M98090_REVID_MASK		(255<<0)
#define M98090_REVID_SHIFT		0
#define M98090_REVID_WIDTH		8
#define M98090_REVID_NUM		(1<<M98090_REVID_WIDTH)

/* Silicon revision number */
#define M98090_REVA			0x40
#define M98091_REVA			0x50

enum max98090_type {
	MAX98090,
	MAX98091,
};

struct max98090_cdata {
	unsigned int rate;
	unsigned int fmt;
};

struct max98090_priv {
	struct regmap *regmap;
	struct snd_soc_codec *codec;
	enum max98090_type devtype;
	struct max98090_pdata *pdata;
	struct clk *mclk;
	unsigned int sysclk;
	unsigned int pclk;
	unsigned int bclk;
	unsigned int lrclk;
	u32 dmic_freq;
	struct max98090_cdata dai[1];
	int jack_state;
	struct delayed_work jack_work;
	struct delayed_work pll_det_enable_work;
	struct work_struct pll_det_disable_work;
	struct work_struct pll_work;
	struct snd_soc_jack *jack;
	unsigned int dai_fmt;
	int tdm_slots;
	int tdm_width;
	u8 lin_state;
	unsigned int pa1en;
	unsigned int pa2en;
	unsigned int sidetone;
	bool master;
	bool shdn_pending;
};

int max98090_mic_detect(struct snd_soc_codec *codec,
	struct snd_soc_jack *jack);

#endif
