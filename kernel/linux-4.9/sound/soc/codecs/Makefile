snd-soc-88pm860x-objs := 88pm860x-codec.o
snd-soc-ab8500-codec-objs := ab8500-codec.o
snd-soc-ac97-objs := ac97.o
snd-soc-ad1836-objs := ad1836.o
snd-soc-ad193x-objs := ad193x.o
snd-soc-ad193x-spi-objs := ad193x-spi.o
snd-soc-ad193x-i2c-objs := ad193x-i2c.o
snd-soc-ad1980-objs := ad1980.o
snd-soc-ad73311-objs := ad73311.o
snd-soc-adau-utils-objs := adau-utils.o
snd-soc-adau1373-objs := adau1373.o
snd-soc-adau1701-objs := adau1701.o
snd-soc-adau17x1-objs := adau17x1.o
snd-soc-adau1761-objs := adau1761.o
snd-soc-adau1761-i2c-objs := adau1761-i2c.o
snd-soc-adau1761-spi-objs := adau1761-spi.o
snd-soc-adau1781-objs := adau1781.o
snd-soc-adau1781-i2c-objs := adau1781-i2c.o
snd-soc-adau1781-spi-objs := adau1781-spi.o
snd-soc-adau1977-objs := adau1977.o
snd-soc-adau1977-spi-objs := adau1977-spi.o
snd-soc-adau1977-i2c-objs := adau1977-i2c.o
snd-soc-adau7002-objs := adau7002.o
snd-soc-adav80x-objs := adav80x.o
snd-soc-adav801-objs := adav801.o
snd-soc-adav803-objs := adav803.o
snd-soc-ads117x-objs := ads117x.o
snd-soc-ak4104-objs := ak4104.o
snd-soc-ak4535-objs := ak4535.o
snd-soc-ak4554-objs := ak4554.o
snd-soc-ak7739-objs := ak7739.o
snd-soc-ak4613-objs := ak4613.o
snd-soc-ak4641-objs := ak4641.o
snd-soc-ak4642-objs := ak4642.o
snd-soc-ak4671-objs := ak4671.o
snd-soc-ak5386-objs := ak5386.o
snd-soc-arizona-objs := arizona.o
snd-soc-bt-sco-objs := bt-sco.o
snd-soc-bu32-objs := bu32.o
snd-soc-cq93vc-objs := cq93vc.o
snd-soc-cs35l32-objs := cs35l32.o
snd-soc-cs35l33-objs := cs35l33.o
snd-soc-cs42l51-objs := cs42l51.o
snd-soc-cs42l51-i2c-objs := cs42l51-i2c.o
snd-soc-cs42l52-objs := cs42l52.o
snd-soc-cs42l56-objs := cs42l56.o
snd-soc-cs42l73-objs := cs42l73.o
snd-soc-cs4265-objs := cs4265.o
snd-soc-cs4270-objs := cs4270.o
snd-soc-cs4271-objs := cs4271.o
snd-soc-cs4271-i2c-objs := cs4271-i2c.o
snd-soc-cs4271-spi-objs := cs4271-spi.o
snd-soc-cs42xx8-objs := cs42xx8.o
snd-soc-cs42xx8-i2c-objs := cs42xx8-i2c.o
snd-soc-cs4349-objs := cs4349.o
snd-soc-cs47l24-objs := cs47l24.o
snd-soc-cs53l30-objs := cs53l30.o
snd-soc-cx20442-objs := cx20442.o
snd-soc-da7210-objs := da7210.o
snd-soc-da7213-objs := da7213.o
snd-soc-da7218-objs := da7218.o
snd-soc-da7219-objs := da7219.o da7219-aad.o
snd-soc-da732x-objs := da732x.o
snd-soc-da9055-objs := da9055.o
snd-soc-dmic-objs := dmic.o
snd-soc-es7210-objs := es7210.o
snd-soc-es8328-objs := es8328.o
snd-soc-es8328-i2c-objs := es8328-i2c.o
snd-soc-es8328-spi-objs := es8328-spi.o
snd-soc-gtm601-objs := gtm601.o
snd-soc-hdac-hdmi-objs := hdac_hdmi.o
snd-soc-ics43432-objs := ics43432.o
snd-soc-inno-rk3036-objs := inno_rk3036.o
snd-soc-isabelle-objs := isabelle.o
snd-soc-jz4740-codec-objs := jz4740.o
snd-soc-l3-objs := l3.o
snd-soc-lm4857-objs := lm4857.o
snd-soc-lm49453-objs := lm49453.o
snd-soc-max9768-objs := max9768.o
snd-soc-max98088-objs := max98088.o
snd-soc-max98090-objs := max98090.o
snd-soc-max98095-objs := max98095.o
snd-soc-max98357a-objs := max98357a.o
snd-soc-max98371-objs := max98371.o
snd-soc-max9867-objs := max9867.o
snd-soc-max98925-objs := max98925.o
snd-soc-max98926-objs := max98926.o
snd-soc-max9850-objs := max9850.o
snd-soc-max9860-objs := max9860.o
snd-soc-mc13783-objs := mc13783.o
snd-soc-ml26124-objs := ml26124.o
snd-soc-nau8810-objs := nau8810.o
snd-soc-nau8825-objs := nau8825.o
snd-soc-hdmi-codec-objs := hdmi-codec.o
snd-soc-pcm1681-objs := pcm1681.o
snd-soc-pcm179x-codec-objs := pcm179x.o
snd-soc-pcm179x-i2c-objs := pcm179x-i2c.o
snd-soc-pcm179x-spi-objs := pcm179x-spi.o
snd-soc-pcm3008-objs := pcm3008.o
snd-soc-pcm3168a-objs := pcm3168a.o
snd-soc-pcm3168a-i2c-objs := pcm3168a-i2c.o
snd-soc-pcm3168a-spi-objs := pcm3168a-spi.o
snd-soc-pcm5102a-objs := pcm5102a.o
snd-soc-pcm512x-objs := pcm512x.o
snd-soc-pcm512x-i2c-objs := pcm512x-i2c.o
snd-soc-pcm512x-spi-objs := pcm512x-spi.o
snd-soc-rl6231-objs := rl6231.o
snd-soc-rl6347a-objs := rl6347a.o
snd-soc-rt286-objs := rt286.o
snd-soc-rt298-objs := rt298.o
snd-soc-rt5514-objs := rt5514.o
snd-soc-rt5514-spi-objs := rt5514-spi.o
snd-soc-rt5616-objs := rt5616.o
snd-soc-rt5631-objs := rt5631.o
snd-soc-rt5640-objs := rt5640.o
snd-soc-rt5645-objs := rt5645.o
snd-soc-rt5651-objs := rt5651.o
snd-soc-rt5659-objs := rt5659.o
snd-soc-rt5660-objs := rt5660.o
snd-soc-rt5663-objs := rt5663.o
snd-soc-rt5670-objs := rt5670.o
snd-soc-rt5677-objs := rt5677.o
snd-soc-rt5677-spi-objs := rt5677-spi.o
snd-soc-sgtl5000-objs := sgtl5000.o
snd-soc-alc5623-objs := alc5623.o
snd-soc-alc5632-objs := alc5632.o
snd-soc-sigmadsp-objs := sigmadsp.o
snd-soc-sigmadsp-i2c-objs := sigmadsp-i2c.o
snd-soc-sigmadsp-regmap-objs := sigmadsp-regmap.o
snd-soc-si476x-objs := si476x.o
snd-soc-sirf-audio-codec-objs := sirf-audio-codec.o
snd-soc-sn95031-objs := sn95031.o
snd-soc-spdif-tx-objs := spdif_transmitter.o
snd-soc-spdif-rx-objs := spdif_receiver.o
snd-soc-ssm2518-objs := ssm2518.o
snd-soc-ssm2602-objs := ssm2602.o
snd-soc-ssm2602-spi-objs := ssm2602-spi.o
snd-soc-ssm2602-i2c-objs := ssm2602-i2c.o
snd-soc-ssm4567-objs := ssm4567.o
snd-soc-sta32x-objs := sta32x.o
snd-soc-sta350-objs := sta350.o
snd-soc-sta529-objs := sta529.o
snd-soc-stac9766-objs := stac9766.o
snd-soc-sti-sas-objs := sti-sas.o
snd-soc-tas5086-objs := tas5086.o
snd-soc-tas571x-objs := tas571x.o
snd-soc-tas5720-objs := tas5720.o
snd-soc-tfa9879-objs := tfa9879.o
snd-soc-tlv320aic23-objs := tlv320aic23.o
snd-soc-tlv320aic23-i2c-objs := tlv320aic23-i2c.o
snd-soc-tlv320aic23-spi-objs := tlv320aic23-spi.o
snd-soc-tlv320aic26-objs := tlv320aic26.o
snd-soc-tlv320aic31xx-objs := tlv320aic31xx.o
snd-soc-tlv320aic32x4-objs := tlv320aic32x4.o
snd-soc-tlv320aic32x4-i2c-objs := tlv320aic32x4-i2c.o
snd-soc-tlv320aic32x4-spi-objs := tlv320aic32x4-spi.o
snd-soc-tlv320aic3x-objs := tlv320aic3x.o
snd-soc-tlv320dac33-objs := tlv320dac33.o
snd-soc-ts3a227e-objs := ts3a227e.o
snd-soc-twl4030-objs := twl4030.o
snd-soc-twl6040-objs := twl6040.o
snd-soc-uda134x-objs := uda134x.o
snd-soc-uda1380-objs := uda1380.o
snd-soc-wl1273-objs := wl1273.o
snd-soc-wm-adsp-objs := wm_adsp.o
snd-soc-wm0010-objs := wm0010.o
snd-soc-wm1250-ev1-objs := wm1250-ev1.o
snd-soc-wm2000-objs := wm2000.o
snd-soc-wm2200-objs := wm2200.o
snd-soc-wm5100-objs := wm5100.o wm5100-tables.o
snd-soc-wm5102-objs := wm5102.o
snd-soc-wm5110-objs := wm5110.o
snd-soc-wm8350-objs := wm8350.o
snd-soc-wm8400-objs := wm8400.o
snd-soc-wm8510-objs := wm8510.o
snd-soc-wm8523-objs := wm8523.o
snd-soc-wm8580-objs := wm8580.o
snd-soc-wm8711-objs := wm8711.o
snd-soc-wm8727-objs := wm8727.o
snd-soc-wm8728-objs := wm8728.o
snd-soc-wm8731-objs := wm8731.o
snd-soc-wm8737-objs := wm8737.o
snd-soc-wm8741-objs := wm8741.o
snd-soc-wm8750-objs := wm8750.o
snd-soc-wm8753-objs := wm8753.o
snd-soc-wm8770-objs := wm8770.o
snd-soc-wm8776-objs := wm8776.o
snd-soc-wm8782-objs := wm8782.o
snd-soc-wm8804-objs := wm8804.o
snd-soc-wm8804-i2c-objs := wm8804-i2c.o
snd-soc-wm8804-spi-objs := wm8804-spi.o
snd-soc-wm8900-objs := wm8900.o
snd-soc-wm8903-objs := wm8903.o
snd-soc-wm8904-objs := wm8904.o
snd-soc-wm8996-objs := wm8996.o
snd-soc-wm8940-objs := wm8940.o
snd-soc-wm8955-objs := wm8955.o
snd-soc-wm8960-objs := wm8960.o
snd-soc-wm8961-objs := wm8961.o
snd-soc-wm8962-objs := wm8962.o
snd-soc-wm8971-objs := wm8971.o
snd-soc-wm8974-objs := wm8974.o
snd-soc-wm8978-objs := wm8978.o
snd-soc-wm8983-objs := wm8983.o
snd-soc-wm8985-objs := wm8985.o
snd-soc-wm8988-objs := wm8988.o
snd-soc-wm8990-objs := wm8990.o
snd-soc-wm8991-objs := wm8991.o
snd-soc-wm8993-objs := wm8993.o
snd-soc-wm8994-objs := wm8994.o wm8958-dsp2.o
snd-soc-wm8995-objs := wm8995.o
snd-soc-wm8997-objs := wm8997.o
snd-soc-wm8998-objs := wm8998.o
snd-soc-wm9081-objs := wm9081.o
snd-soc-wm9090-objs := wm9090.o
snd-soc-wm9705-objs := wm9705.o
snd-soc-wm9712-objs := wm9712.o
snd-soc-wm9713-objs := wm9713.o
snd-soc-wm-hubs-objs := wm_hubs.o

# Amp
snd-soc-max9877-objs := max9877.o
snd-soc-max98504-objs := max98504.o
snd-soc-tpa6130a2-objs := tpa6130a2.o
snd-soc-tas2552-objs := tas2552.o
#Wolfgang add acx00
snd-soc-acx00-objs := acx00.o

#Sunxi add td100
snd-soc-td100-objs := td100.o
#Sunxi add ac107
snd-soc-ac107-objs := ac107.o
#Sunxi add ac108
snd-soc-ac108-objs := ac108.o

#tyw add bt936b
snd-soc-bt936b-objs := bt936b.o

obj-$(CONFIG_SND_SOC_88PM860X)	+= snd-soc-88pm860x.o
obj-$(CONFIG_SND_SOC_AB8500_CODEC)	+= snd-soc-ab8500-codec.o
obj-$(CONFIG_SND_SOC_AC97_CODEC)	+= snd-soc-ac97.o
obj-$(CONFIG_SND_SOC_AD1836)	+= snd-soc-ad1836.o
obj-$(CONFIG_SND_SOC_AD193X)	+= snd-soc-ad193x.o
obj-$(CONFIG_SND_SOC_AD193X_SPI)	+= snd-soc-ad193x-spi.o
obj-$(CONFIG_SND_SOC_AD193X_I2C)	+= snd-soc-ad193x-i2c.o
obj-$(CONFIG_SND_SOC_AD1980)	+= snd-soc-ad1980.o
obj-$(CONFIG_SND_SOC_AD73311) += snd-soc-ad73311.o
obj-$(CONFIG_SND_SOC_ADAU_UTILS)	+= snd-soc-adau-utils.o
obj-$(CONFIG_SND_SOC_ADAU1373)	+= snd-soc-adau1373.o
obj-$(CONFIG_SND_SOC_ADAU1701)		+= snd-soc-adau1701.o
obj-$(CONFIG_SND_SOC_ADAU17X1)		+= snd-soc-adau17x1.o
obj-$(CONFIG_SND_SOC_ADAU1761)		+= snd-soc-adau1761.o
obj-$(CONFIG_SND_SOC_ADAU1761_I2C)	+= snd-soc-adau1761-i2c.o
obj-$(CONFIG_SND_SOC_ADAU1761_SPI)	+= snd-soc-adau1761-spi.o
obj-$(CONFIG_SND_SOC_ADAU1781)		+= snd-soc-adau1781.o
obj-$(CONFIG_SND_SOC_ADAU1781_I2C)	+= snd-soc-adau1781-i2c.o
obj-$(CONFIG_SND_SOC_ADAU1781_SPI)	+= snd-soc-adau1781-spi.o
obj-$(CONFIG_SND_SOC_ADAU1977)		+= snd-soc-adau1977.o
obj-$(CONFIG_SND_SOC_ADAU1977_SPI)	+= snd-soc-adau1977-spi.o
obj-$(CONFIG_SND_SOC_ADAU1977_I2C)	+= snd-soc-adau1977-i2c.o
obj-$(CONFIG_SND_SOC_ADAU7002)	+= snd-soc-adau7002.o
obj-$(CONFIG_SND_SOC_ADAV80X)  += snd-soc-adav80x.o
obj-$(CONFIG_SND_SOC_ADAV801)  += snd-soc-adav801.o
obj-$(CONFIG_SND_SOC_ADAV803)  += snd-soc-adav803.o
obj-$(CONFIG_SND_SOC_ADS117X)	+= snd-soc-ads117x.o
obj-$(CONFIG_SND_SOC_AK4104)	+= snd-soc-ak4104.o
obj-$(CONFIG_SND_SOC_AK4535)	+= snd-soc-ak4535.o
obj-$(CONFIG_SND_SOC_AK4554)	+= snd-soc-ak4554.o
obj-$(CONFIG_SND_SOC_AK7739)	+= snd-soc-ak7739.o
obj-$(CONFIG_SND_SOC_AK4613)	+= snd-soc-ak4613.o
obj-$(CONFIG_SND_SOC_AK4641)	+= snd-soc-ak4641.o
obj-$(CONFIG_SND_SOC_AK4642)	+= snd-soc-ak4642.o
obj-$(CONFIG_SND_SOC_AK4671)	+= snd-soc-ak4671.o
obj-$(CONFIG_SND_SOC_AK5386)	+= snd-soc-ak5386.o
obj-$(CONFIG_SND_SOC_ALC5623)    += snd-soc-alc5623.o
obj-$(CONFIG_SND_SOC_ALC5632)	+= snd-soc-alc5632.o
obj-$(CONFIG_SND_SOC_ARIZONA)	+= snd-soc-arizona.o
obj-$(CONFIG_SND_SOC_BT_SCO)	+= snd-soc-bt-sco.o
obj-$(CONFIG_SND_SOC_BU32)		+= snd-soc-bu32.o
obj-$(CONFIG_SND_SOC_CQ0093VC) += snd-soc-cq93vc.o
obj-$(CONFIG_SND_SOC_CS35L32)	+= snd-soc-cs35l32.o
obj-$(CONFIG_SND_SOC_CS35L33)	+= snd-soc-cs35l33.o
obj-$(CONFIG_SND_SOC_CS42L51)	+= snd-soc-cs42l51.o
obj-$(CONFIG_SND_SOC_CS42L51_I2C)	+= snd-soc-cs42l51-i2c.o
obj-$(CONFIG_SND_SOC_CS42L52)	+= snd-soc-cs42l52.o
obj-$(CONFIG_SND_SOC_CS42L56)	+= snd-soc-cs42l56.o
obj-$(CONFIG_SND_SOC_CS42L73)	+= snd-soc-cs42l73.o
obj-$(CONFIG_SND_SOC_CS4265)	+= snd-soc-cs4265.o
obj-$(CONFIG_SND_SOC_CS4270)	+= snd-soc-cs4270.o
obj-$(CONFIG_SND_SOC_CS4271)	+= snd-soc-cs4271.o
obj-$(CONFIG_SND_SOC_CS4271_I2C)	+= snd-soc-cs4271-i2c.o
obj-$(CONFIG_SND_SOC_CS4271_SPI)	+= snd-soc-cs4271-spi.o
obj-$(CONFIG_SND_SOC_CS42XX8)	+= snd-soc-cs42xx8.o
obj-$(CONFIG_SND_SOC_CS42XX8_I2C) += snd-soc-cs42xx8-i2c.o
obj-$(CONFIG_SND_SOC_CS4349)	+= snd-soc-cs4349.o
obj-$(CONFIG_SND_SOC_CS47L24)	+= snd-soc-cs47l24.o
obj-$(CONFIG_SND_SOC_CS53L30)	+= snd-soc-cs53l30.o
obj-$(CONFIG_SND_SOC_CX20442)	+= snd-soc-cx20442.o
obj-$(CONFIG_SND_SOC_DA7210)	+= snd-soc-da7210.o
obj-$(CONFIG_SND_SOC_DA7213)	+= snd-soc-da7213.o
obj-$(CONFIG_SND_SOC_DA7218)	+= snd-soc-da7218.o
obj-$(CONFIG_SND_SOC_DA7219)	+= snd-soc-da7219.o
obj-$(CONFIG_SND_SOC_DA732X)	+= snd-soc-da732x.o
obj-$(CONFIG_SND_SOC_DA9055)	+= snd-soc-da9055.o
obj-$(CONFIG_SND_SOC_DMIC)	+= snd-soc-dmic.o
obj-$(CONFIG_SND_SOC_ES7210)	+= snd-soc-es7210.o
obj-$(CONFIG_SND_SOC_ES8328)	+= snd-soc-es8328.o
obj-$(CONFIG_SND_SOC_ES8328_I2C)+= snd-soc-es8328-i2c.o
obj-$(CONFIG_SND_SOC_ES8328_SPI)+= snd-soc-es8328-spi.o
obj-$(CONFIG_SND_SOC_GTM601)    += snd-soc-gtm601.o
obj-$(CONFIG_SND_SOC_HDAC_HDMI) += snd-soc-hdac-hdmi.o
obj-$(CONFIG_SND_SOC_ICS43432)	+= snd-soc-ics43432.o
obj-$(CONFIG_SND_SOC_INNO_RK3036)	+= snd-soc-inno-rk3036.o
obj-$(CONFIG_SND_SOC_ISABELLE)	+= snd-soc-isabelle.o
obj-$(CONFIG_SND_SOC_JZ4740_CODEC)	+= snd-soc-jz4740-codec.o
obj-$(CONFIG_SND_SOC_L3)	+= snd-soc-l3.o
obj-$(CONFIG_SND_SOC_LM4857)	+= snd-soc-lm4857.o
obj-$(CONFIG_SND_SOC_LM49453)   += snd-soc-lm49453.o
obj-$(CONFIG_SND_SOC_MAX9768)	+= snd-soc-max9768.o
obj-$(CONFIG_SND_SOC_MAX98088)	+= snd-soc-max98088.o
obj-$(CONFIG_SND_SOC_MAX98090)	+= snd-soc-max98090.o
obj-$(CONFIG_SND_SOC_MAX98095)	+= snd-soc-max98095.o
obj-$(CONFIG_SND_SOC_MAX98357A)	+= snd-soc-max98357a.o
obj-$(CONFIG_SND_SOC_MAX9867)	+= snd-soc-max9867.o
obj-$(CONFIG_SND_SOC_MAX98925)	+= snd-soc-max98925.o
obj-$(CONFIG_SND_SOC_MAX98926)	+= snd-soc-max98926.o
obj-$(CONFIG_SND_SOC_MAX9850)	+= snd-soc-max9850.o
obj-$(CONFIG_SND_SOC_MAX9860)	+= snd-soc-max9860.o
obj-$(CONFIG_SND_SOC_MC13783)	+= snd-soc-mc13783.o
obj-$(CONFIG_SND_SOC_ML26124)	+= snd-soc-ml26124.o
obj-$(CONFIG_SND_SOC_NAU8810)   += snd-soc-nau8810.o
obj-$(CONFIG_SND_SOC_NAU8825)   += snd-soc-nau8825.o
obj-$(CONFIG_SND_SOC_HDMI_CODEC)	+= snd-soc-hdmi-codec.o
obj-$(CONFIG_SND_SOC_PCM1681)	+= snd-soc-pcm1681.o
obj-$(CONFIG_SND_SOC_PCM179X)	+= snd-soc-pcm179x-codec.o
obj-$(CONFIG_SND_SOC_PCM179X_I2C)	+= snd-soc-pcm179x-i2c.o
obj-$(CONFIG_SND_SOC_PCM179X_SPI)	+= snd-soc-pcm179x-spi.o
obj-$(CONFIG_SND_SOC_PCM3008)	+= snd-soc-pcm3008.o
obj-$(CONFIG_SND_SOC_PCM3168A)	+= snd-soc-pcm3168a.o
obj-$(CONFIG_SND_SOC_PCM3168A_I2C)	+= snd-soc-pcm3168a-i2c.o
obj-$(CONFIG_SND_SOC_PCM3168A_SPI)	+= snd-soc-pcm3168a-spi.o
obj-$(CONFIG_SND_SOC_PCM5102A)	+= snd-soc-pcm5102a.o
obj-$(CONFIG_SND_SOC_PCM512x)	+= snd-soc-pcm512x.o
obj-$(CONFIG_SND_SOC_PCM512x_I2C)	+= snd-soc-pcm512x-i2c.o
obj-$(CONFIG_SND_SOC_PCM512x_SPI)	+= snd-soc-pcm512x-spi.o
obj-$(CONFIG_SND_SOC_RL6231)	+= snd-soc-rl6231.o
obj-$(CONFIG_SND_SOC_RL6347A)	+= snd-soc-rl6347a.o
obj-$(CONFIG_SND_SOC_RT286)	+= snd-soc-rt286.o
obj-$(CONFIG_SND_SOC_RT298)	+= snd-soc-rt298.o
obj-$(CONFIG_SND_SOC_RT5514)	+= snd-soc-rt5514.o
obj-$(CONFIG_SND_SOC_RT5514_SPI)	+= snd-soc-rt5514-spi.o
obj-$(CONFIG_SND_SOC_RT5616)	+= snd-soc-rt5616.o
obj-$(CONFIG_SND_SOC_RT5631)	+= snd-soc-rt5631.o
obj-$(CONFIG_SND_SOC_RT5640)	+= snd-soc-rt5640.o
obj-$(CONFIG_SND_SOC_RT5645)	+= snd-soc-rt5645.o
obj-$(CONFIG_SND_SOC_RT5651)	+= snd-soc-rt5651.o
obj-$(CONFIG_SND_SOC_RT5659)	+= snd-soc-rt5659.o
obj-$(CONFIG_SND_SOC_RT5660)	+= snd-soc-rt5660.o
obj-$(CONFIG_SND_SOC_RT5663)	+= snd-soc-rt5663.o
obj-$(CONFIG_SND_SOC_RT5670)	+= snd-soc-rt5670.o
obj-$(CONFIG_SND_SOC_RT5677)	+= snd-soc-rt5677.o
obj-$(CONFIG_SND_SOC_RT5677_SPI)	+= snd-soc-rt5677-spi.o
obj-$(CONFIG_SND_SOC_SGTL5000)  += snd-soc-sgtl5000.o
obj-$(CONFIG_SND_SOC_SIGMADSP)	+= snd-soc-sigmadsp.o
obj-$(CONFIG_SND_SOC_SIGMADSP_I2C)	+= snd-soc-sigmadsp-i2c.o
obj-$(CONFIG_SND_SOC_SIGMADSP_REGMAP)	+= snd-soc-sigmadsp-regmap.o
obj-$(CONFIG_SND_SOC_SI476X)	+= snd-soc-si476x.o
obj-$(CONFIG_SND_SOC_SN95031)	+=snd-soc-sn95031.o
obj-$(CONFIG_SND_SOC_SPDIF)	+= snd-soc-spdif-rx.o snd-soc-spdif-tx.o
obj-$(CONFIG_SND_SOC_SSM2518)	+= snd-soc-ssm2518.o
obj-$(CONFIG_SND_SOC_SSM2602)	+= snd-soc-ssm2602.o
obj-$(CONFIG_SND_SOC_SSM2602_SPI)	+= snd-soc-ssm2602-spi.o
obj-$(CONFIG_SND_SOC_SSM2602_I2C)	+= snd-soc-ssm2602-i2c.o
obj-$(CONFIG_SND_SOC_SSM4567)	+= snd-soc-ssm4567.o
obj-$(CONFIG_SND_SOC_STA32X)   += snd-soc-sta32x.o
obj-$(CONFIG_SND_SOC_STA350)   += snd-soc-sta350.o
obj-$(CONFIG_SND_SOC_STA529)   += snd-soc-sta529.o
obj-$(CONFIG_SND_SOC_STAC9766)	+= snd-soc-stac9766.o
obj-$(CONFIG_SND_SOC_STI_SAS)	+= snd-soc-sti-sas.o
obj-$(CONFIG_SND_SOC_TAS2552)	+= snd-soc-tas2552.o
obj-$(CONFIG_SND_SOC_TAS5086)	+= snd-soc-tas5086.o
obj-$(CONFIG_SND_SOC_TAS571X)	+= snd-soc-tas571x.o
obj-$(CONFIG_SND_SOC_TAS5720)	+= snd-soc-tas5720.o
obj-$(CONFIG_SND_SOC_TFA9879)	+= snd-soc-tfa9879.o
obj-$(CONFIG_SND_SOC_TLV320AIC23)	+= snd-soc-tlv320aic23.o
obj-$(CONFIG_SND_SOC_TLV320AIC23_I2C)	+= snd-soc-tlv320aic23-i2c.o
obj-$(CONFIG_SND_SOC_TLV320AIC23_SPI)	+= snd-soc-tlv320aic23-spi.o
obj-$(CONFIG_SND_SOC_TLV320AIC26)	+= snd-soc-tlv320aic26.o
obj-$(CONFIG_SND_SOC_TLV320AIC31XX)     += snd-soc-tlv320aic31xx.o
obj-$(CONFIG_SND_SOC_TLV320AIC32X4)     += snd-soc-tlv320aic32x4.o
obj-$(CONFIG_SND_SOC_TLV320AIC32X4_I2C)	+= snd-soc-tlv320aic32x4-i2c.o
obj-$(CONFIG_SND_SOC_TLV320AIC32X4_SPI)	+= snd-soc-tlv320aic32x4-spi.o
obj-$(CONFIG_SND_SOC_TLV320AIC3X)	+= snd-soc-tlv320aic3x.o
obj-$(CONFIG_SND_SOC_TLV320DAC33)	+= snd-soc-tlv320dac33.o
obj-$(CONFIG_SND_SOC_TS3A227E)	+= snd-soc-ts3a227e.o
obj-$(CONFIG_SND_SOC_TWL4030)	+= snd-soc-twl4030.o
obj-$(CONFIG_SND_SOC_TWL6040)	+= snd-soc-twl6040.o
obj-$(CONFIG_SND_SOC_UDA134X)	+= snd-soc-uda134x.o
obj-$(CONFIG_SND_SOC_UDA1380)	+= snd-soc-uda1380.o
obj-$(CONFIG_SND_SOC_WL1273)	+= snd-soc-wl1273.o
obj-$(CONFIG_SND_SOC_WM0010)	+= snd-soc-wm0010.o
obj-$(CONFIG_SND_SOC_WM1250_EV1) += snd-soc-wm1250-ev1.o
obj-$(CONFIG_SND_SOC_WM2000)	+= snd-soc-wm2000.o
obj-$(CONFIG_SND_SOC_WM2200)	+= snd-soc-wm2200.o
obj-$(CONFIG_SND_SOC_WM5100)	+= snd-soc-wm5100.o
obj-$(CONFIG_SND_SOC_WM5102)	+= snd-soc-wm5102.o
obj-$(CONFIG_SND_SOC_WM5110)	+= snd-soc-wm5110.o
obj-$(CONFIG_SND_SOC_WM8350)	+= snd-soc-wm8350.o
obj-$(CONFIG_SND_SOC_WM8400)	+= snd-soc-wm8400.o
obj-$(CONFIG_SND_SOC_WM8510)	+= snd-soc-wm8510.o
obj-$(CONFIG_SND_SOC_WM8523)	+= snd-soc-wm8523.o
obj-$(CONFIG_SND_SOC_WM8580)	+= snd-soc-wm8580.o
obj-$(CONFIG_SND_SOC_WM8711)	+= snd-soc-wm8711.o
obj-$(CONFIG_SND_SOC_WM8727)	+= snd-soc-wm8727.o
obj-$(CONFIG_SND_SOC_WM8728)	+= snd-soc-wm8728.o
obj-$(CONFIG_SND_SOC_WM8731)	+= snd-soc-wm8731.o
obj-$(CONFIG_SND_SOC_WM8737)	+= snd-soc-wm8737.o
obj-$(CONFIG_SND_SOC_WM8741)	+= snd-soc-wm8741.o
obj-$(CONFIG_SND_SOC_WM8750)	+= snd-soc-wm8750.o
obj-$(CONFIG_SND_SOC_WM8753)	+= snd-soc-wm8753.o
obj-$(CONFIG_SND_SOC_WM8770)	+= snd-soc-wm8770.o
obj-$(CONFIG_SND_SOC_WM8776)	+= snd-soc-wm8776.o
obj-$(CONFIG_SND_SOC_WM8782)	+= snd-soc-wm8782.o
obj-$(CONFIG_SND_SOC_WM8804)	+= snd-soc-wm8804.o
obj-$(CONFIG_SND_SOC_WM8804_I2C) += snd-soc-wm8804-i2c.o
obj-$(CONFIG_SND_SOC_WM8804_SPI) += snd-soc-wm8804-spi.o
obj-$(CONFIG_SND_SOC_WM8900)	+= snd-soc-wm8900.o
obj-$(CONFIG_SND_SOC_WM8903)	+= snd-soc-wm8903.o
obj-$(CONFIG_SND_SOC_WM8904)	+= snd-soc-wm8904.o
obj-$(CONFIG_SND_SOC_WM8996)	+= snd-soc-wm8996.o
obj-$(CONFIG_SND_SOC_WM8940)	+= snd-soc-wm8940.o
obj-$(CONFIG_SND_SOC_WM8955)	+= snd-soc-wm8955.o
obj-$(CONFIG_SND_SOC_WM8960)	+= snd-soc-wm8960.o
obj-$(CONFIG_SND_SOC_WM8961)	+= snd-soc-wm8961.o
obj-$(CONFIG_SND_SOC_WM8962)	+= snd-soc-wm8962.o
obj-$(CONFIG_SND_SOC_WM8971)	+= snd-soc-wm8971.o
obj-$(CONFIG_SND_SOC_WM8974)	+= snd-soc-wm8974.o
obj-$(CONFIG_SND_SOC_WM8978)	+= snd-soc-wm8978.o
obj-$(CONFIG_SND_SOC_WM8983)	+= snd-soc-wm8983.o
obj-$(CONFIG_SND_SOC_WM8985)	+= snd-soc-wm8985.o
obj-$(CONFIG_SND_SOC_WM8988)	+= snd-soc-wm8988.o
obj-$(CONFIG_SND_SOC_WM8990)	+= snd-soc-wm8990.o
obj-$(CONFIG_SND_SOC_WM8991)	+= snd-soc-wm8991.o
obj-$(CONFIG_SND_SOC_WM8993)	+= snd-soc-wm8993.o
obj-$(CONFIG_SND_SOC_WM8994)	+= snd-soc-wm8994.o
obj-$(CONFIG_SND_SOC_WM8995)	+= snd-soc-wm8995.o
obj-$(CONFIG_SND_SOC_WM8997)	+= snd-soc-wm8997.o
obj-$(CONFIG_SND_SOC_WM8998)	+= snd-soc-wm8998.o
obj-$(CONFIG_SND_SOC_WM9081)	+= snd-soc-wm9081.o
obj-$(CONFIG_SND_SOC_WM9090)	+= snd-soc-wm9090.o
obj-$(CONFIG_SND_SOC_WM9705)	+= snd-soc-wm9705.o
obj-$(CONFIG_SND_SOC_WM9712)	+= snd-soc-wm9712.o
obj-$(CONFIG_SND_SOC_WM9713)	+= snd-soc-wm9713.o
obj-$(CONFIG_SND_SOC_WM_ADSP)	+= snd-soc-wm-adsp.o
obj-$(CONFIG_SND_SOC_WM_HUBS)	+= snd-soc-wm-hubs.o

# Amp
obj-$(CONFIG_SND_SOC_MAX9877)	+= snd-soc-max9877.o
obj-$(CONFIG_SND_SOC_MAX98504)	+= snd-soc-max98504.o
obj-$(CONFIG_SND_SOC_TPA6130A2)	+= snd-soc-tpa6130a2.o
# Wolfgang add ACX00
obj-$(CONFIG_SND_SOC_ACX00)	+= snd-soc-acx00.o

# Sunxi add TD100
obj-$(CONFIG_SND_SOC_TD100)	+= snd-soc-td100.o
# Sunxi add AC107
obj-$(CONFIG_SND_SOC_AC107)	+= snd-soc-ac107.o
# Sunxi add AC108
obj-$(CONFIG_SND_SOC_AC108)	+= snd-soc-ac108.o

# tyw add bt936b
obj-$(CONFIG_SND_SOC_BT936B) += snd-soc-bt936b.o