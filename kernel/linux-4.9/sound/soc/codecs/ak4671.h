/*
 * ak4671.h  --  audio driver for AK4671
 *
 * Copyright (C) 2009 Samsung Electronics Co.Ltd
 * Author: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
 *
 *  This program is free software; you can redistribute  it and/or modify it
 *  under  the terms of  the GNU General  Public License as published by the
 *  Free Software Foundation;  either version 2 of the  License, or (at your
 *  option) any later version.
 *
 */

#ifndef _AK4671_H
#define _AK4671_H

#define AK4671_AD_DA_POWER_MANAGEMENT		0x00
#define AK4671_PLL_MODE_SELECT0			0x01
#define AK4671_PLL_MODE_SELECT1			0x02
#define AK4671_FORMAT_SELECT			0x03
#define AK4671_MIC_SIGNAL_SELECT		0x04
#define AK4671_MIC_AMP_GAIN			0x05
#define AK4671_MIXING_POWER_MANAGEMENT0		0x06
#define AK4671_MIXING_POWER_MANAGEMENT1		0x07
#define AK4671_OUTPUT_VOLUME_CONTROL		0x08
#define AK4671_LOUT1_SIGNAL_SELECT		0x09
#define AK4671_ROUT1_SIGNAL_SELECT		0x0a
#define AK4671_LOUT2_SIGNAL_SELECT		0x0b
#define AK4671_ROUT2_SIGNAL_SELECT		0x0c
#define AK4671_LOUT3_SIGNAL_SELECT		0x0d
#define AK4671_ROUT3_SIGNAL_SELECT		0x0e
#define AK4671_LOUT1_POWER_MANAGERMENT		0x0f
#define AK4671_LOUT2_POWER_MANAGERMENT		0x10
#define AK4671_LOUT3_POWER_MANAGERMENT		0x11
#define AK4671_LCH_INPUT_VOLUME_CONTROL		0x12
#define AK4671_RCH_INPUT_VOLUME_CONTROL		0x13
#define AK4671_ALC_REFERENCE_SELECT		0x14
#define AK4671_DIGITAL_MIXING_CONTROL		0x15
#define AK4671_ALC_TIMER_SELECT			0x16
#define AK4671_ALC_MODE_CONTROL			0x17
#define AK4671_MODE_CONTROL1			0x18
#define AK4671_MODE_CONTROL2			0x19
#define AK4671_LCH_OUTPUT_VOLUME_CONTROL	0x1a
#define AK4671_RCH_OUTPUT_VOLUME_CONTROL	0x1b
#define AK4671_SIDETONE_A_CONTROL		0x1c
#define AK4671_DIGITAL_FILTER_SELECT		0x1d
#define AK4671_FIL3_COEFFICIENT0		0x1e
#define AK4671_FIL3_COEFFICIENT1		0x1f
#define AK4671_FIL3_COEFFICIENT2		0x20
#define AK4671_FIL3_COEFFICIENT3		0x21
#define AK4671_EQ_COEFFICIENT0			0x22
#define AK4671_EQ_COEFFICIENT1			0x23
#define AK4671_EQ_COEFFICIENT2			0x24
#define AK4671_EQ_COEFFICIENT3			0x25
#define AK4671_EQ_COEFFICIENT4			0x26
#define AK4671_EQ_COEFFICIENT5			0x27
#define AK4671_FIL1_COEFFICIENT0		0x28
#define AK4671_FIL1_COEFFICIENT1		0x29
#define AK4671_FIL1_COEFFICIENT2		0x2a
#define AK4671_FIL1_COEFFICIENT3		0x2b
#define AK4671_FIL2_COEFFICIENT0		0x2c
#define AK4671_FIL2_COEFFICIENT1		0x2d
#define AK4671_FIL2_COEFFICIENT2		0x2e
#define AK4671_FIL2_COEFFICIENT3		0x2f
#define AK4671_DIGITAL_FILTER_SELECT2		0x30
#define AK4671_E1_COEFFICIENT0			0x32
#define AK4671_E1_COEFFICIENT1			0x33
#define AK4671_E1_COEFFICIENT2			0x34
#define AK4671_E1_COEFFICIENT3			0x35
#define AK4671_E1_COEFFICIENT4			0x36
#define AK4671_E1_COEFFICIENT5			0x37
#define AK4671_E2_COEFFICIENT0			0x38
#define AK4671_E2_COEFFICIENT1			0x39
#define AK4671_E2_COEFFICIENT2			0x3a
#define AK4671_E2_COEFFICIENT3			0x3b
#define AK4671_E2_COEFFICIENT4			0x3c
#define AK4671_E2_COEFFICIENT5			0x3d
#define AK4671_E3_COEFFICIENT0			0x3e
#define AK4671_E3_COEFFICIENT1			0x3f
#define AK4671_E3_COEFFICIENT2			0x40
#define AK4671_E3_COEFFICIENT3			0x41
#define AK4671_E3_COEFFICIENT4			0x42
#define AK4671_E3_COEFFICIENT5			0x43
#define AK4671_E4_COEFFICIENT0			0x44
#define AK4671_E4_COEFFICIENT1			0x45
#define AK4671_E4_COEFFICIENT2			0x46
#define AK4671_E4_COEFFICIENT3			0x47
#define AK4671_E4_COEFFICIENT4			0x48
#define AK4671_E4_COEFFICIENT5			0x49
#define AK4671_E5_COEFFICIENT0			0x4a
#define AK4671_E5_COEFFICIENT1			0x4b
#define AK4671_E5_COEFFICIENT2			0x4c
#define AK4671_E5_COEFFICIENT3			0x4d
#define AK4671_E5_COEFFICIENT4			0x4e
#define AK4671_E5_COEFFICIENT5			0x4f
#define AK4671_EQ_CONTROL_250HZ_100HZ		0x50
#define AK4671_EQ_CONTROL_3500HZ_1KHZ		0x51
#define AK4671_EQ_CONTRO_10KHZ			0x52
#define AK4671_PCM_IF_CONTROL0			0x53
#define AK4671_PCM_IF_CONTROL1			0x54
#define AK4671_PCM_IF_CONTROL2			0x55
#define AK4671_DIGITAL_VOLUME_B_CONTROL		0x56
#define AK4671_DIGITAL_VOLUME_C_CONTROL		0x57
#define AK4671_SIDETONE_VOLUME_CONTROL		0x58
#define AK4671_DIGITAL_MIXING_CONTROL2		0x59
#define AK4671_SAR_ADC_CONTROL			0x5a

/* Bitfield Definitions */

/* AK4671_AD_DA_POWER_MANAGEMENT (0x00) Fields */
#define AK4671_PMVCM				0x01

/* AK4671_PLL_MODE_SELECT0 (0x01) Fields */
#define AK4671_PLL				0x0f
#define AK4671_PLL_11_2896MHZ			(4 << 0)
#define AK4671_PLL_12_288MHZ			(5 << 0)
#define AK4671_PLL_12MHZ			(6 << 0)
#define AK4671_PLL_24MHZ			(7 << 0)
#define AK4671_PLL_19_2MHZ			(8 << 0)
#define AK4671_PLL_13_5MHZ			(12 << 0)
#define AK4671_PLL_27MHZ			(13 << 0)
#define AK4671_PLL_13MHZ			(14 << 0)
#define AK4671_PLL_26MHZ			(15 << 0)
#define AK4671_FS				0xf0
#define AK4671_FS_8KHZ				(0 << 4)
#define AK4671_FS_12KHZ				(1 << 4)
#define AK4671_FS_16KHZ				(2 << 4)
#define AK4671_FS_24KHZ				(3 << 4)
#define AK4671_FS_11_025KHZ			(5 << 4)
#define AK4671_FS_22_05KHZ			(7 << 4)
#define AK4671_FS_32KHZ				(10 << 4)
#define AK4671_FS_48KHZ				(11 << 4)
#define AK4671_FS_44_1KHZ			(15 << 4)

/* AK4671_PLL_MODE_SELECT1 (0x02) Fields */
#define AK4671_PMPLL				0x01
#define AK4671_M_S				0x02

/* AK4671_FORMAT_SELECT (0x03) Fields */
#define AK4671_DIF				0x03
#define AK4671_DIF_DSP_MODE			(0 << 0)
#define AK4671_DIF_MSB_MODE			(2 << 0)
#define AK4671_DIF_I2S_MODE			(3 << 0)
#define AK4671_BCKP				0x04
#define AK4671_MSBS				0x08
#define AK4671_SDOD				0x10

/* AK4671_LOUT2_POWER_MANAGEMENT (0x10) Fields */
#define AK4671_MUTEN				0x04

#endif
