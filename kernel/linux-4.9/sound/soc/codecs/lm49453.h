/*
 * lm49453.h  -  LM49453 ALSA Soc Audio drive
 *
 * Copyright (c) 2012  Texas Instruments, Inc
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; version 2 of the License.
 *
 */

#ifndef _LM49453_H
#define _LM49453_H

#include <linux/bitops.h>

/* LM49453_P0 register space for page0 */
#define LM49453_P0_PMC_SETUP_REG			0x00
#define LM49453_P0_PLL_CLK_SEL1_REG			0x01
#define LM49453_P0_PLL_CLK_SEL2_REG			0x02
#define LM49453_P0_PMC_CLK_DIV_REG			0x03
#define LM49453_P0_HSDET_CLK_DIV_REG			0x04
#define LM49453_P0_DMIC_CLK_DIV_REG			0x05
#define LM49453_P0_ADC_CLK_DIV_REG			0x06
#define LM49453_P0_DAC_OT_CLK_DIV_REG			0x07
#define LM49453_P0_PLL_HF_M_REG				0x08
#define LM49453_P0_PLL_LF_M_REG				0x09
#define LM49453_P0_PLL_NL_REG				0x0A
#define LM49453_P0_PLL_N_MODL_REG			0x0B
#define LM49453_P0_PLL_N_MODH_REG			0x0C
#define LM49453_P0_PLL_P1_REG				0x0D
#define LM49453_P0_PLL_P2_REG				0x0E
#define LM49453_P0_FLL_REF_FREQL_REG			0x0F
#define LM49453_P0_FLL_REF_FREQH_REG			0x10
#define LM49453_P0_VCO_TARGETLL_REG			0x11
#define LM49453_P0_VCO_TARGETLH_REG			0x12
#define LM49453_P0_VCO_TARGETHL_REG			0x13
#define LM49453_P0_VCO_TARGETHH_REG			0x14
#define LM49453_P0_PLL_CONFIG_REG			0x15
#define LM49453_P0_DAC_CLK_SEL_REG			0x16
#define LM49453_P0_DAC_HP_CLK_DIV_REG			0x17

/* Analog Mixer Input Stages */
#define LM49453_P0_MICL_REG				0x20
#define LM49453_P0_MICR_REG				0x21
#define LM49453_P0_EP_REG				0x24
#define LM49453_P0_DIS_PKVL_FB_REG			0x25

/* Analog Mixer Output Stages */
#define LM49453_P0_ANALOG_MIXER_ADC_REG			0x2E

/*ADC or DAC */
#define LM49453_P0_ADC_DSP_REG				0x30
#define LM49453_P0_DAC_DSP_REG				0x31

/* EFFECTS ENABLES */
#define LM49453_P0_ADC_FX_ENABLES_REG			0x33

/* GPIO */
#define LM49453_P0_GPIO1_REG				0x38
#define LM49453_P0_GPIO2_REG				0x39
#define LM49453_P0_GPIO3_REG				0x3A
#define LM49453_P0_HAP_CTL_REG				0x3B
#define LM49453_P0_HAP_FREQ_PROG_LEFTL_REG		0x3C
#define LM49453_P0_HAP_FREQ_PROG_LEFTH_REG		0x3D
#define LM49453_P0_HAP_FREQ_PROG_RIGHTL_REG		0x3E
#define LM49453_P0_HAP_FREQ_PROG_RIGHTH_REG		0x3F

/* DIGITAL MIXER */
#define LM49453_P0_DMIX_CLK_SEL_REG			0x40
#define LM49453_P0_PORT1_RX_LVL1_REG			0x41
#define LM49453_P0_PORT1_RX_LVL2_REG			0x42
#define LM49453_P0_PORT2_RX_LVL_REG			0x43
#define LM49453_P0_PORT1_TX1_REG			0x44
#define LM49453_P0_PORT1_TX2_REG			0x45
#define LM49453_P0_PORT1_TX3_REG			0x46
#define LM49453_P0_PORT1_TX4_REG			0x47
#define LM49453_P0_PORT1_TX5_REG			0x48
#define LM49453_P0_PORT1_TX6_REG			0x49
#define LM49453_P0_PORT1_TX7_REG			0x4A
#define LM49453_P0_PORT1_TX8_REG			0x4B
#define LM49453_P0_PORT2_TX1_REG			0x4C
#define LM49453_P0_PORT2_TX2_REG			0x4D
#define LM49453_P0_STN_SEL_REG				0x4F
#define LM49453_P0_DACHPL1_REG				0x50
#define LM49453_P0_DACHPL2_REG				0x51
#define LM49453_P0_DACHPR1_REG				0x52
#define LM49453_P0_DACHPR2_REG				0x53
#define LM49453_P0_DACLOL1_REG				0x54
#define LM49453_P0_DACLOL2_REG				0x55
#define LM49453_P0_DACLOR1_REG				0x56
#define LM49453_P0_DACLOR2_REG				0x57
#define LM49453_P0_DACLSL1_REG				0x58
#define LM49453_P0_DACLSL2_REG				0x59
#define LM49453_P0_DACLSR1_REG				0x5A
#define LM49453_P0_DACLSR2_REG				0x5B
#define LM49453_P0_DACHAL1_REG				0x5C
#define LM49453_P0_DACHAL2_REG				0x5D
#define LM49453_P0_DACHAR1_REG				0x5E
#define LM49453_P0_DACHAR2_REG				0x5F

/* AUDIO PORT 1 (TDM) */
#define LM49453_P0_AUDIO_PORT1_BASIC_REG		0x60
#define LM49453_P0_AUDIO_PORT1_CLK_GEN1_REG		0x61
#define LM49453_P0_AUDIO_PORT1_CLK_GEN2_REG		0x62
#define LM49453_P0_AUDIO_PORT1_CLK_GEN3_REG		0x63
#define LM49453_P0_AUDIO_PORT1_SYNC_RATE_REG		0x64
#define LM49453_P0_AUDIO_PORT1_SYNC_SDO_SETUP_REG	0x65
#define LM49453_P0_AUDIO_PORT1_DATA_WIDTH_REG		0x66
#define LM49453_P0_AUDIO_PORT1_RX_MSB_REG		0x67
#define LM49453_P0_AUDIO_PORT1_TX_MSB_REG		0x68
#define LM49453_P0_AUDIO_PORT1_TDM_CHANNELS_REG		0x69

/* AUDIO PORT 2 */
#define LM49453_P0_AUDIO_PORT2_BASIC_REG		0x6A
#define LM49453_P0_AUDIO_PORT2_CLK_GEN1_REG		0x6B
#define LM49453_P0_AUDIO_PORT2_CLK_GEN2_REG		0x6C
#define LM49453_P0_AUDIO_PORT2_SYNC_GEN_REG		0x6D
#define LM49453_P0_AUDIO_PORT2_DATA_WIDTH_REG		0x6E
#define LM49453_P0_AUDIO_PORT2_RX_MODE_REG		0x6F
#define LM49453_P0_AUDIO_PORT2_TX_MODE_REG		0x70

/* SAMPLE RATE */
#define LM49453_P0_PORT1_SR_LSB_REG			0x79
#define LM49453_P0_PORT1_SR_MSB_REG			0x7A
#define LM49453_P0_PORT2_SR_LSB_REG			0x7B
#define LM49453_P0_PORT2_SR_MSB_REG			0x7C

/* EFFECTS - HPFs */
#define LM49453_P0_HPF_REG				0x80

/* EFFECTS ADC ALC */
#define LM49453_P0_ADC_ALC1_REG				0x82
#define LM49453_P0_ADC_ALC2_REG				0x83
#define LM49453_P0_ADC_ALC3_REG				0x84
#define LM49453_P0_ADC_ALC4_REG				0x85
#define LM49453_P0_ADC_ALC5_REG				0x86
#define LM49453_P0_ADC_ALC6_REG				0x87
#define LM49453_P0_ADC_ALC7_REG				0x88
#define LM49453_P0_ADC_ALC8_REG				0x89
#define LM49453_P0_DMIC1_LEVELL_REG			0x8A
#define LM49453_P0_DMIC1_LEVELR_REG			0x8B
#define LM49453_P0_DMIC2_LEVELL_REG			0x8C
#define LM49453_P0_DMIC2_LEVELR_REG			0x8D
#define LM49453_P0_ADC_LEVELL_REG			0x8E
#define LM49453_P0_ADC_LEVELR_REG			0x8F
#define LM49453_P0_DAC_HP_LEVELL_REG			0x90
#define LM49453_P0_DAC_HP_LEVELR_REG			0x91
#define LM49453_P0_DAC_LO_LEVELL_REG			0x92
#define LM49453_P0_DAC_LO_LEVELR_REG			0x93
#define LM49453_P0_DAC_LS_LEVELL_REG			0x94
#define LM49453_P0_DAC_LS_LEVELR_REG			0x95
#define LM49453_P0_DAC_HA_LEVELL_REG			0x96
#define LM49453_P0_DAC_HA_LEVELR_REG			0x97
#define LM49453_P0_SOFT_MUTE_REG			0x98
#define LM49453_P0_DMIC_MUTE_CFG_REG			0x99
#define LM49453_P0_ADC_MUTE_CFG_REG			0x9A
#define LM49453_P0_DAC_MUTE_CFG_REG			0x9B

/*DIGITAL MIC1 */
#define LM49453_P0_DIGITAL_MIC1_CONFIG_REG		0xB0
#define LM49453_P0_DIGITAL_MIC1_DATA_DELAYL_REG		0xB1
#define LM49453_P0_DIGITAL_MIC1_DATA_DELAYR_REG		0xB2

/*DIGITAL MIC2 */
#define LM49453_P0_DIGITAL_MIC2_CONFIG_REG		0xB3
#define LM49453_P0_DIGITAL_MIC2_DATA_DELAYL_REG		0xB4
#define LM49453_P0_DIGITAL_MIC2_DATA_DELAYR_REG		0xB5

/* ADC DECIMATOR */
#define LM49453_P0_ADC_DECIMATOR_REG			0xB6

/* DAC CONFIGURE */
#define LM49453_P0_DAC_CONFIG_REG			0xB7

/* SIDETONE */
#define LM49453_P0_STN_VOL_ADCL_REG			0xB8
#define LM49453_P0_STN_VOL_ADCR_REG			0xB9
#define LM49453_P0_STN_VOL_DMIC1L_REG			0xBA
#define LM49453_P0_STN_VOL_DMIC1R_REG			0xBB
#define LM49453_P0_STN_VOL_DMIC2L_REG			0xBC
#define LM49453_P0_STN_VOL_DMIC2R_REG			0xBD

/* ADC/DAC CLIPPING MONITORS (Read Only/Write to Clear) */
#define LM49453_P0_ADC_DEC_CLIP_REG			0xC2
#define LM49453_P0_ADC_HPF_CLIP_REG			0xC3
#define LM49453_P0_ADC_LVL_CLIP_REG			0xC4
#define LM49453_P0_DAC_LVL_CLIP_REG			0xC5

/* ADC ALC EFFECT MONITORS (Read Only) */
#define LM49453_P0_ADC_LVLMONL_REG			0xC8
#define LM49453_P0_ADC_LVLMONR_REG			0xC9
#define LM49453_P0_ADC_ALCMONL_REG			0xCA
#define LM49453_P0_ADC_ALCMONR_REG			0xCB
#define LM49453_P0_ADC_MUTED_REG			0xCC
#define LM49453_P0_DAC_MUTED_REG			0xCD

/* HEADSET DETECT */
#define LM49453_P0_HSD_PPB_LONG_CNT_LIMITL_REG		0xD0
#define LM49453_P0_HSD_PPB_LONG_CNT_LIMITR_REG		0xD1
#define LM49453_P0_HSD_PIN3_4_EX_LOOP_CNT_LIMITL_REG	0xD2
#define LM49453_P0_HSD_PIN3_4_EX_LOOP_CNT_LIMITH_REG	0xD3
#define LM49453_P0_HSD_TIMEOUT1_REG			0xD4
#define LM49453_P0_HSD_TIMEOUT2_REG			0xD5
#define LM49453_P0_HSD_TIMEOUT3_REG			0xD6
#define LM49453_P0_HSD_PIN3_4_CFG_REG			0xD7
#define LM49453_P0_HSD_IRQ1_REG				0xD8
#define LM49453_P0_HSD_IRQ2_REG				0xD9
#define LM49453_P0_HSD_IRQ3_REG				0xDA
#define LM49453_P0_HSD_IRQ4_REG				0xDB
#define LM49453_P0_HSD_IRQ_MASK1_REG			0xDC
#define LM49453_P0_HSD_IRQ_MASK2_REG			0xDD
#define LM49453_P0_HSD_IRQ_MASK3_REG			0xDE
#define LM49453_P0_HSD_R_HPLL_REG			0xE0
#define LM49453_P0_HSD_R_HPLH_REG			0xE1
#define LM49453_P0_HSD_R_HPLU_REG			0xE2
#define LM49453_P0_HSD_R_HPRL_REG			0xE3
#define LM49453_P0_HSD_R_HPRH_REG			0xE4
#define LM49453_P0_HSD_R_HPRU_REG			0xE5
#define LM49453_P0_HSD_VEL_L_FINALL_REG			0xE6
#define LM49453_P0_HSD_VEL_L_FINALH_REG			0xE7
#define LM49453_P0_HSD_VEL_L_FINALU_REG			0xE8
#define LM49453_P0_HSD_RO_FINALL_REG			0xE9
#define LM49453_P0_HSD_RO_FINALH_REG			0xEA
#define LM49453_P0_HSD_RO_FINALU_REG			0xEB
#define LM49453_P0_HSD_VMIC_BIAS_FINALL_REG		0xEC
#define LM49453_P0_HSD_VMIC_BIAS_FINALH_REG		0xED
#define LM49453_P0_HSD_VMIC_BIAS_FINALU_REG		0xEE
#define LM49453_P0_HSD_PIN_CONFIG_REG			0xEF
#define LM49453_P0_HSD_PLUG_DETECT_BB_IRQ_STATUS1_REG	0xF1
#define LM49453_P0_HSD_PLUG_DETECT_BB_IRQ_STATUS2_REG	0xF2
#define LM49453_P0_HSD_PLUG_DETECT_BB_IRQ_STATUS3_REG	0xF3
#define LM49453_P0_HSD_PLUG_DETECT_BB_IRQ_STATEL_REG	0xF4
#define LM49453_P0_HSD_PLUG_DETECT_BB_IRQ_STATEH_REG	0xF5

/* I/O PULLDOWN CONFIG */
#define LM49453_P0_PULL_CONFIG1_REG			0xF8
#define LM49453_P0_PULL_CONFIG2_REG			0xF9
#define LM49453_P0_PULL_CONFIG3_REG			0xFA

/* RESET */
#define LM49453_P0_RESET_REG				0xFE

/* PAGE */
#define LM49453_PAGE_REG				0xFF

#define LM49453_MAX_REGISTER				(0xFF+1)

/* LM49453_P0_PMC_SETUP_REG (0x00h) */
#define LM49453_PMC_SETUP_CHIP_EN			(BIT(1)|BIT(0))
#define LM49453_PMC_SETUP_PLL_EN			BIT(2)
#define LM49453_PMC_SETUP_PLL_P2_EN			BIT(3)
#define LM49453_PMC_SETUP_PLL_FLL			BIT(4)
#define LM49453_PMC_SETUP_MCLK_OVER			BIT(5)
#define LM49453_PMC_SETUP_RTC_CLK_OVER			BIT(6)
#define LM49453_PMC_SETUP_CHIP_ACTIVE			BIT(7)

/* Chip Enable bits */
#define LM49453_CHIP_EN_SHUTDOWN			0x00
#define LM49453_CHIP_EN					0x01
#define LM49453_CHIP_EN_HSD_DETECT			0x02
#define LM49453_CHIP_EN_INVALID_HSD			0x03

/* LM49453_P0_PLL_CLK_SEL1_REG (0x01h) */
#define LM49453_CLK_SEL1_MCLK_SEL			0x11
#define LM49453_CLK_SEL1_RTC_SEL			0x11
#define LM49453_CLK_SEL1_PORT1_SEL			0x10
#define LM49453_CLK_SEL1_PORT2_SEL			0x11

/* LM49453_P0_PLL_CLK_SEL2_REG (0x02h) */
#define LM49453_CLK_SEL2_ADC_CLK_SEL			0x38

/* LM49453_P0_FLL_REF_FREQL_REG (0x0F) */
#define LM49453_FLL_REF_FREQ_VAL			0x8ca0001

/* LM49453_P0_VCO_TARGETLL_REG (0x11) */
#define LM49453_VCO_TARGET_VAL				0x8ca0001

/* LM49453_P0_ADC_DSP_REG (0x30h) */
#define LM49453_ADC_DSP_ADC_MUTEL			BIT(0)
#define LM49453_ADC_DSP_ADC_MUTER			BIT(1)
#define LM49453_ADC_DSP_DMIC1_MUTEL			BIT(2)
#define LM49453_ADC_DSP_DMIC1_MUTER			BIT(3)
#define LM49453_ADC_DSP_DMIC2_MUTEL			BIT(4)
#define LM49453_ADC_DSP_DMIC2_MUTER			BIT(5)
#define LM49453_ADC_DSP_MUTE_ALL			0x3F

/* LM49453_P0_DAC_DSP_REG (0x31h) */
#define LM49453_DAC_DSP_MUTE_ALL			0xFF

/* LM49453_P0_AUDIO_PORT1_BASIC_REG (0x60h) */
#define LM49453_AUDIO_PORT1_BASIC_FMT_MASK		(BIT(4)|BIT(3))
#define LM49453_AUDIO_PORT1_BASIC_CLK_MS		BIT(3)
#define LM49453_AUDIO_PORT1_BASIC_SYNC_MS		BIT(4)

/* LM49453_P0_RESET_REG (0xFEh) */
#define LM49453_RESET_REG_RST				BIT(0)

/* Page select register bits (0xFF) */
#define LM49453_PAGE0_SELECT				0x0
#define LM49453_PAGE1_SELECT				0x1

/* LM49453_P0_HSD_PIN3_4_CFG_REG (Jack Pin config - 0xD7) */
#define LM49453_JACK_DISABLE				0x00
#define LM49453_JACK_CONFIG1				0x01
#define LM49453_JACK_CONFIG2				0x02
#define LM49453_JACK_CONFIG3				0x03
#define LM49453_JACK_CONFIG4				0x04
#define LM49453_JACK_CONFIG5				0x05

/* Page 1 REGISTERS */

/* SIDETONE */
#define LM49453_P1_SIDETONE_SA0L_REG			0x80
#define LM49453_P1_SIDETONE_SA0H_REG			0x81
#define LM49453_P1_SIDETONE_SAB0U_REG			0x82
#define LM49453_P1_SIDETONE_SB0L_REG			0x83
#define LM49453_P1_SIDETONE_SB0H_REG			0x84
#define LM49453_P1_SIDETONE_SH0L_REG			0x85
#define LM49453_P1_SIDETONE_SH0H_REG			0x86
#define LM49453_P1_SIDETONE_SH0U_REG			0x87
#define LM49453_P1_SIDETONE_SA1L_REG			0x88
#define LM49453_P1_SIDETONE_SA1H_REG			0x89
#define LM49453_P1_SIDETONE_SAB1U_REG			0x8A
#define LM49453_P1_SIDETONE_SB1L_REG			0x8B
#define LM49453_P1_SIDETONE_SB1H_REG			0x8C
#define LM49453_P1_SIDETONE_SH1L_REG			0x8D
#define LM49453_P1_SIDETONE_SH1H_REG			0x8E
#define LM49453_P1_SIDETONE_SH1U_REG			0x8F
#define LM49453_P1_SIDETONE_SA2L_REG			0x90
#define LM49453_P1_SIDETONE_SA2H_REG			0x91
#define LM49453_P1_SIDETONE_SAB2U_REG			0x92
#define LM49453_P1_SIDETONE_SB2L_REG			0x93
#define LM49453_P1_SIDETONE_SB2H_REG			0x94
#define LM49453_P1_SIDETONE_SH2L_REG			0x95
#define LM49453_P1_SIDETONE_SH2H_REG			0x96
#define LM49453_P1_SIDETONE_SH2U_REG			0x97
#define LM49453_P1_SIDETONE_SA3L_REG			0x98
#define LM49453_P1_SIDETONE_SA3H_REG			0x99
#define LM49453_P1_SIDETONE_SAB3U_REG			0x9A
#define LM49453_P1_SIDETONE_SB3L_REG			0x9B
#define LM49453_P1_SIDETONE_SB3H_REG			0x9C
#define LM49453_P1_SIDETONE_SH3L_REG			0x9D
#define LM49453_P1_SIDETONE_SH3H_REG			0x9E
#define LM49453_P1_SIDETONE_SH3U_REG			0x9F
#define LM49453_P1_SIDETONE_SA4L_REG			0xA0
#define LM49453_P1_SIDETONE_SA4H_REG			0xA1
#define LM49453_P1_SIDETONE_SAB4U_REG			0xA2
#define LM49453_P1_SIDETONE_SB4L_REG			0xA3
#define LM49453_P1_SIDETONE_SB4H_REG			0xA4
#define LM49453_P1_SIDETONE_SH4L_REG			0xA5
#define LM49453_P1_SIDETONE_SH4H_REG			0xA6
#define LM49453_P1_SIDETONE_SH4U_REG			0xA7
#define LM49453_P1_SIDETONE_SA5L_REG			0xA8
#define LM49453_P1_SIDETONE_SA5H_REG			0xA9
#define LM49453_P1_SIDETONE_SAB5U_REG			0xAA
#define LM49453_P1_SIDETONE_SB5L_REG			0xAB
#define LM49453_P1_SIDETONE_SB5H_REG			0xAC
#define LM49453_P1_SIDETONE_SH5L_REG			0xAD
#define LM49453_P1_SIDETONE_SH5H_REG			0xAE
#define LM49453_P1_SIDETONE_SH5U_REG			0xAF

/* CHARGE PUMP CONFIG */
#define LM49453_P1_CP_CONFIG1_REG			0xB0
#define LM49453_P1_CP_CONFIG2_REG			0xB1
#define LM49453_P1_CP_CONFIG3_REG			0xB2
#define LM49453_P1_CP_CONFIG4_REG			0xB3
#define LM49453_P1_CP_LA_VTH1L_REG			0xB4
#define LM49453_P1_CP_LA_VTH1M_REG			0xB5
#define LM49453_P1_CP_LA_VTH2L_REG			0xB6
#define LM49453_P1_CP_LA_VTH2M_REG			0xB7
#define LM49453_P1_CP_LA_VTH3L_REG			0xB8
#define LM49453_P1_CP_LA_VTH3H_REG			0xB9
#define LM49453_P1_CP_CLK_DIV_REG			0xBA

/* DAC */
#define LM49453_P1_DAC_CHOP_REG				0xC0

#define	LM49453_CLK_SRC_MCLK				1
#endif
