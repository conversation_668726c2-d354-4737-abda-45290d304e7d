/*
 * rt286.h  --  RT286 ALSA SoC audio driver
 *
 * Copyright 2011 Realtek Microelectronics
 * Author: <PERSON> <johnny<PERSON><PERSON>@realtek.com>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 */

#ifndef __RT286_H__
#define __RT286_H__

#define VERB_CMD(V, N, D) ((N << 20) | (V << 8) | D)

#define RT286_AUDIO_FUNCTION_GROUP			0x01
#define RT286_DAC_OUT1					0x02
#define RT286_DAC_OUT2					0x03
#define RT286_ADC_IN1					0x09
#define RT286_ADC_IN2					0x08
#define RT286_MIXER_IN					0x0b
#define RT286_MIXER_OUT1				0x0c
#define RT286_MIXER_OUT2				0x0d
#define RT286_DMIC1					0x12
#define RT286_DMIC2					0x13
#define RT286_SPK_OUT					0x14
#define RT286_MIC1					0x18
#define RT286_LINE1					0x1a
#define RT286_BEEP					0x1d
#define RT286_SPDIF					0x1e
#define RT286_VENDOR_REGISTERS				0x20
#define RT286_HP_OUT					0x21
#define RT286_MIXER_IN1					0x22
#define RT286_MIXER_IN2					0x23

#define RT286_SET_PIN_SFT				6
#define RT286_SET_PIN_ENABLE				0x40
#define RT286_SET_PIN_DISABLE				0
#define RT286_SET_EAPD_HIGH				0x2
#define RT286_SET_EAPD_LOW				0

#define RT286_MUTE_SFT					7

/* Verb commands */
#define RT286_GET_PARAM(NID, PARAM) VERB_CMD(AC_VERB_PARAMETERS, NID, PARAM)
#define RT286_SET_POWER(NID) VERB_CMD(AC_VERB_SET_POWER_STATE, NID, 0)
#define RT286_SET_AUDIO_POWER RT286_SET_POWER(RT286_AUDIO_FUNCTION_GROUP)
#define RT286_SET_HPO_POWER RT286_SET_POWER(RT286_HP_OUT)
#define RT286_SET_SPK_POWER RT286_SET_POWER(RT286_SPK_OUT)
#define RT286_SET_DMIC1_POWER RT286_SET_POWER(RT286_DMIC1)
#define RT286_SPK_MUX\
	VERB_CMD(AC_VERB_SET_CONNECT_SEL, RT286_SPK_OUT, 0)
#define RT286_HPO_MUX\
	VERB_CMD(AC_VERB_SET_CONNECT_SEL, RT286_HP_OUT, 0)
#define RT286_ADC0_MUX\
	VERB_CMD(AC_VERB_SET_CONNECT_SEL, RT286_MIXER_IN1, 0)
#define RT286_ADC1_MUX\
	VERB_CMD(AC_VERB_SET_CONNECT_SEL, RT286_MIXER_IN2, 0)
#define RT286_SET_MIC1\
	VERB_CMD(AC_VERB_SET_PIN_WIDGET_CONTROL, RT286_MIC1, 0)
#define RT286_SET_PIN_HPO\
	VERB_CMD(AC_VERB_SET_PIN_WIDGET_CONTROL, RT286_HP_OUT, 0)
#define RT286_SET_PIN_SPK\
	VERB_CMD(AC_VERB_SET_PIN_WIDGET_CONTROL, RT286_SPK_OUT, 0)
#define RT286_SET_PIN_DMIC1\
	VERB_CMD(AC_VERB_SET_PIN_WIDGET_CONTROL, RT286_DMIC1, 0)
#define RT286_SPK_EAPD\
	VERB_CMD(AC_VERB_SET_EAPD_BTLENABLE, RT286_SPK_OUT, 0)
#define RT286_SET_AMP_GAIN_HPO\
	VERB_CMD(AC_VERB_SET_AMP_GAIN_MUTE, RT286_HP_OUT, 0)
#define RT286_SET_AMP_GAIN_ADC_IN1\
	VERB_CMD(AC_VERB_SET_AMP_GAIN_MUTE, RT286_ADC_IN1, 0)
#define RT286_SET_AMP_GAIN_ADC_IN2\
	VERB_CMD(AC_VERB_SET_AMP_GAIN_MUTE, RT286_ADC_IN2, 0)
#define RT286_GET_HP_SENSE\
	VERB_CMD(AC_VERB_GET_PIN_SENSE, RT286_HP_OUT, 0)
#define RT286_GET_MIC1_SENSE\
	VERB_CMD(AC_VERB_GET_PIN_SENSE, RT286_MIC1, 0)
#define RT286_SET_DMIC2_DEFAULT\
	VERB_CMD(AC_VERB_SET_CONFIG_DEFAULT_BYTES_3, RT286_DMIC2, 0)
#define RT286_DACL_GAIN\
	VERB_CMD(AC_VERB_SET_AMP_GAIN_MUTE, RT286_DAC_OUT1, 0xa000)
#define RT286_DACR_GAIN\
	VERB_CMD(AC_VERB_SET_AMP_GAIN_MUTE, RT286_DAC_OUT1, 0x9000)
#define RT286_ADCL_GAIN\
	VERB_CMD(AC_VERB_SET_AMP_GAIN_MUTE, RT286_ADC_IN1, 0x6000)
#define RT286_ADCR_GAIN\
	VERB_CMD(AC_VERB_SET_AMP_GAIN_MUTE, RT286_ADC_IN1, 0x5000)
#define RT286_MIC_GAIN\
	VERB_CMD(AC_VERB_SET_AMP_GAIN_MUTE, RT286_MIC1, 0x7000)
#define RT286_SPOL_GAIN\
	VERB_CMD(AC_VERB_SET_AMP_GAIN_MUTE, RT286_SPK_OUT, 0xa000)
#define RT286_SPOR_GAIN\
	VERB_CMD(AC_VERB_SET_AMP_GAIN_MUTE, RT286_SPK_OUT, 0x9000)
#define RT286_HPOL_GAIN\
	VERB_CMD(AC_VERB_SET_AMP_GAIN_MUTE, RT286_HP_OUT, 0xa000)
#define RT286_HPOR_GAIN\
	VERB_CMD(AC_VERB_SET_AMP_GAIN_MUTE, RT286_HP_OUT, 0x9000)
#define RT286_F_DAC_SWITCH\
	VERB_CMD(AC_VERB_SET_AMP_GAIN_MUTE, RT286_MIXER_OUT1, 0x7000)
#define RT286_F_RECMIX_SWITCH\
	VERB_CMD(AC_VERB_SET_AMP_GAIN_MUTE, RT286_MIXER_OUT1, 0x7100)
#define RT286_REC_MIC_SWITCH\
	VERB_CMD(AC_VERB_SET_AMP_GAIN_MUTE, RT286_MIXER_IN, 0x7000)
#define RT286_REC_I2S_SWITCH\
	VERB_CMD(AC_VERB_SET_AMP_GAIN_MUTE, RT286_MIXER_IN, 0x7100)
#define RT286_REC_LINE_SWITCH\
	VERB_CMD(AC_VERB_SET_AMP_GAIN_MUTE, RT286_MIXER_IN, 0x7200)
#define RT286_REC_BEEP_SWITCH\
	VERB_CMD(AC_VERB_SET_AMP_GAIN_MUTE, RT286_MIXER_IN, 0x7300)
#define RT286_DAC_FORMAT\
	VERB_CMD(AC_VERB_SET_STREAM_FORMAT, RT286_DAC_OUT1, 0)
#define RT286_ADC_FORMAT\
	VERB_CMD(AC_VERB_SET_STREAM_FORMAT, RT286_ADC_IN1, 0)
#define RT286_COEF_INDEX\
	VERB_CMD(AC_VERB_SET_COEF_INDEX, RT286_VENDOR_REGISTERS, 0)
#define RT286_PROC_COEF\
	VERB_CMD(AC_VERB_SET_PROC_COEF, RT286_VENDOR_REGISTERS, 0)
#define RT286_SET_GPIO_MASK\
	VERB_CMD(AC_VERB_SET_GPIO_MASK, RT286_AUDIO_FUNCTION_GROUP, 0)
#define RT286_SET_GPIO_DIRECTION\
	VERB_CMD(AC_VERB_SET_GPIO_DIRECTION, RT286_AUDIO_FUNCTION_GROUP, 0)
#define RT286_SET_GPIO_DATA\
	VERB_CMD(AC_VERB_SET_GPIO_DATA, RT286_AUDIO_FUNCTION_GROUP, 0)

/* Index registers */
#define RT286_A_BIAS_CTRL1	0x01
#define RT286_A_BIAS_CTRL2	0x02
#define RT286_POWER_CTRL1	0x03
#define RT286_A_BIAS_CTRL3	0x04
#define RT286_POWER_CTRL2	0x08
#define RT286_I2S_CTRL1		0x09
#define RT286_I2S_CTRL2		0x0a
#define RT286_CLK_DIV		0x0b
#define RT286_DC_GAIN		0x0d
#define RT286_POWER_CTRL3	0x0f
#define RT286_MIC1_DET_CTRL	0x19
#define RT286_MISC_CTRL1	0x20
#define RT286_GPIO_CTRL		0x29
#define RT286_IRQ_CTRL		0x33
#define RT286_PLL_CTRL1		0x49
#define RT286_CBJ_CTRL1		0x4f
#define RT286_CBJ_CTRL2		0x50
#define RT286_PLL_CTRL		0x63
#define RT286_DEPOP_CTRL1	0x66
#define RT286_DEPOP_CTRL2	0x67
#define RT286_DEPOP_CTRL3	0x68
#define RT286_DEPOP_CTRL4	0x69

/* SPDIF (0x06) */
#define RT286_SPDIF_SEL_SFT	0
#define RT286_SPDIF_SEL_PCM0	0
#define RT286_SPDIF_SEL_PCM1	1
#define RT286_SPDIF_SEL_SPOUT	2
#define RT286_SPDIF_SEL_PP	3

/* RECMIX (0x0b) */
#define RT286_M_REC_BEEP_SFT	0
#define RT286_M_REC_LINE1_SFT	1
#define RT286_M_REC_MIC1_SFT	2
#define RT286_M_REC_I2S_SFT	3

/* Front (0x0c) */
#define RT286_M_FRONT_DAC_SFT	0
#define RT286_M_FRONT_REC_SFT	1

/* SPK-OUT (0x14) */
#define RT286_M_SPK_MUX_SFT	14
#define RT286_SPK_SEL_MASK	0x1
#define RT286_SPK_SEL_SFT	0
#define RT286_SPK_SEL_F		0
#define RT286_SPK_SEL_S		1

/* HP-OUT (0x21) */
#define RT286_M_HP_MUX_SFT	14
#define RT286_HP_SEL_MASK	0x1
#define RT286_HP_SEL_SFT	0
#define RT286_HP_SEL_F		0
#define RT286_HP_SEL_S		1

/* ADC (0x22) (0x23) */
#define RT286_ADC_SEL_MASK	0x7
#define RT286_ADC_SEL_SFT	0
#define RT286_ADC_SEL_SURR	0
#define RT286_ADC_SEL_FRONT	1
#define RT286_ADC_SEL_DMIC	2
#define RT286_ADC_SEL_BEEP	4
#define RT286_ADC_SEL_LINE1	5
#define RT286_ADC_SEL_I2S	6
#define RT286_ADC_SEL_MIC1	7

#define RT286_SCLK_S_MCLK	0
#define RT286_SCLK_S_PLL	1

enum {
	RT286_AIF1,
	RT286_AIF2,
	RT286_AIFS,
};

int rt286_mic_detect(struct snd_soc_codec *codec, struct snd_soc_jack *jack);

#endif /* __RT286_H__ */

