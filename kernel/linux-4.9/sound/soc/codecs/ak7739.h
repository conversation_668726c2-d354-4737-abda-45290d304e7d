/*
 * ak7739.h  --  audio driver for ak7739
 *
 * Copyright (C) 2018 Asahi Kasei Microdevices Corporation
 *  Author                Date        Revision
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *                      18/06/04        1.0
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 *
 */

#ifndef _AK7739_H
#define _AK7739_H

//#define AK7739_I2C_IF
// #define AK7739_PCM_BCKP // BICK Pos edge setting for PCM format aaa

#define AK7739_SYNCDOMAIN_NUM   7
#define AK7739_SDATA_NUM        6

#define AK7739_AHDIV_BIT     1
#define AK7739_BUSCLOCK_NO   0   // 0:24.576MHz,  1:12.288MHz

#define AK7739_SDIO6_SYNC_DOMAIN  5
#define AK7739_ADC1_SYNC_DOMAIN   1
#define AK7739_CODEC_SYNC_DOMAIN  1
#define AK7739_DSP1_SYNC_DOMAIN   1
#define AK7739_DSP2_SYNC_DOMAIN   1

/* User Setting */


/* AUDIOHUB,PERI block */
#define  AK7739_C0_000_SYSCLOCK_SETTING1                0x0000
#define  AK7739_C0_001_SYSCLOCK_SETTING2                0x0001
#define  AK7739_C0_002_SYSCLOCK_SETTING3                0x0002
#define  AK7739_C0_003_SYSCLOCK_SETTING4                0x0003
#define  AK7739_C0_004_SYSCLOCK_SETTING5                0x0004
#define  AK7739_C0_008_AHCLK_SETTING                    0x0008
#define  AK7739_C0_009_BUSCLOCK_SETTING                 0x0009
#define  AK7739_C0_00A_BUSCLOCK_SETTING2                0x000A
#define  AK7739_C0_00B_CLKO_SETTING1                    0x000B
#define  AK7739_C0_00C_CLKO_SETTING2                    0x000C
#define  AK7739_C0_00D_MasterSPI_SCLK_SETTING1          0x000D
#define  AK7739_C0_00E_MasterSPI_SCLK_SETTING2          0x000E
#define  AK7739_C0_010_SYNCDOMAIN1_SETTING1             0x0010
#define  AK7739_C0_011_SYNCDOMAIN1_SETTING2             0x0011
#define  AK7739_C0_012_SYNCDOMAIN1_SETTING3             0x0012
#define  AK7739_C0_013_SYNCDOMAIN1_SETTING4             0x0013
#define  AK7739_C0_014_SYNCDOMAIN2_SETTING1             0x0014
#define  AK7739_C0_015_SYNCDOMAIN2_SETTING2             0x0015
#define  AK7739_C0_016_SYNCDOMAIN2_SETTING3             0x0016
#define  AK7739_C0_017_SYNCDOMAIN2_SETTING4             0x0017
#define  AK7739_C0_018_SYNCDOMAIN3_SETTING1             0x0018
#define  AK7739_C0_019_SYNCDOMAIN3_SETTING2             0x0019
#define  AK7739_C0_01A_SYNCDOMAIN3_SETTING3             0x001A
#define  AK7739_C0_01B_SYNCDOMAIN3_SETTING4             0x001B
#define  AK7739_C0_01C_SYNCDOMAIN4_SETTING1             0x001C
#define  AK7739_C0_01D_SYNCDOMAIN4_SETTING2             0x001D
#define  AK7739_C0_01E_SYNCDOMAIN4_SETTING3             0x001E
#define  AK7739_C0_01F_SYNCDOMAIN4_SETTING4             0x001F
#define  AK7739_C0_020_SYNCDOMAIN5_SETTING1             0x0020
#define  AK7739_C0_021_SYNCDOMAIN5_SETTING2             0x0021
#define  AK7739_C0_022_SYNCDOMAIN5_SETTING3             0x0022
#define  AK7739_C0_023_SYNCDOMAIN5_SETTING4             0x0023
#define  AK7739_C0_025_SYNCDOMAIN6_SETTING2             0x0025
#define  AK7739_C0_026_SYNCDOMAIN6_SETTING3             0x0026
#define  AK7739_C0_027_SYNCDOMAIN6_SETTING4             0x0027
#define  AK7739_C0_029_SYNCDOMAIN7_SETTING2             0x0029
#define  AK7739_C0_02A_SYNCDOMAIN7_SETTING3             0x002A
#define  AK7739_C0_02B_SYNCDOMAIN7_SETTING4             0x002B
#define  AK7739_C0_040_BICK_FORMAT_SETTING1             0x0040
#define  AK7739_C0_041_BICK_SYNCDOMAIN_SELECT1          0x0041
#define  AK7739_C0_042_BICK_FORMAT_SETTING2             0x0042
#define  AK7739_C0_043_BICK_SYNCDOMAIN_SELECT2          0x0043
#define  AK7739_C0_044_BICK_FORMAT_SETTING3             0x0044
#define  AK7739_C0_045_BICK_SYNCDOMAIN_SELECT3          0x0045
#define  AK7739_C0_046_BICK_FORMAT_SETTING4             0x0046
#define  AK7739_C0_047_BICK_SYNCDOMAIN_SELECT4          0x0047
#define  AK7739_C0_048_BICK_FORMAT_SETTING5             0x0048
#define  AK7739_C0_049_BICK_SYNCDOMAIN_SELECT5          0x0049
#define  AK7739_C0_050_SDIN1_INPUT_FORMAT               0x0050
#define  AK7739_C0_051_SDIN1_SYNCDOMAIN_SELECT          0x0051
#define  AK7739_C0_052_SDIN2_INPUT_FORMAT               0x0052
#define  AK7739_C0_053_SDIN2_SYNCDOMAIN_SELECT          0x0053
#define  AK7739_C0_054_SDIN3_INPUT_FORMAT               0x0054
#define  AK7739_C0_055_SDIN3_SYNCDOMAIN_SELECT          0x0055
#define  AK7739_C0_056_SDIN4_INPUT_FORMAT               0x0056
#define  AK7739_C0_057_SDIN4_SYNCDOMAIN_SELECT          0x0057
#define  AK7739_C0_058_SDIN5_INPUT_FORMAT               0x0058
#define  AK7739_C0_059_SDIN5_SYNCDOMAIN_SELECT          0x0059
#define  AK7739_C0_05A_SDIN6_INPUT_FORMAT               0x005A
#define  AK7739_C0_05B_SDIN6_SYNCDOMAIN_SELECT          0x005B
#define  AK7739_C0_060_SDOUT1_OUTPUT_FORMAT             0x0060
#define  AK7739_C0_061_SDOUT1_SYNCDOMAIN_SELECT         0x0061
#define  AK7739_C0_062_SDOUT2_OUTPUT_FORMAT             0x0062
#define  AK7739_C0_063_SDOUT2_SYNCDOMAIN_SELECT         0x0063
#define  AK7739_C0_064_SDOUT3_OUTPUT_FORMAT             0x0064
#define  AK7739_C0_065_SDOUT3_SYNCDOMAIN_SELECT         0x0065
#define  AK7739_C0_066_SDOUT4_OUTPUT_FORMAT             0x0066
#define  AK7739_C0_067_SDOUT4_SYNCDOMAIN_SELECT         0x0067
#define  AK7739_C0_068_SDOUT5_OUTPUT_FORMAT             0x0068
#define  AK7739_C0_069_SDOUT5_SYNCDOMAIN_SELECT         0x0069
#define  AK7739_C0_06A_SDOUT6_OUTPUT_FORMAT             0x006A
#define  AK7739_C0_06B_SDOUT6_SYNCDOMAIN_SELECT         0x006B
#define  AK7739_C0_087_OSMEM_SYNCDOMAIN_SELECT          0x0087
#define  AK7739_C0_088_ADC1_SYNCDOMAIN_SELECT           0x0088
#define  AK7739_C0_089_CODEC_SYNCDOMAIN_SELECT          0x0089
#define  AK7739_C0_098_MIXER1_SYNCDOMAIN_SELECT         0x0098
#define  AK7739_C0_099_MIXER2_SYNCDOMAIN_SELECT         0x0099
#define  AK7739_C0_0A0_SRC1_SYNCDOMAIN_SELECT           0x00A0
#define  AK7739_C0_0A1_SRC2_SYNCDOMAIN_SELECT           0x00A1
#define  AK7739_C0_0A2_SRC3_SYNCDOMAIN_SELECT           0x00A2
#define  AK7739_C0_0A3_SRC4_SYNCDOMAIN_SELECT           0x00A3
#define  AK7739_C0_0A4_SRC5_SYNCDOMAIN_SELECT           0x00A4
#define  AK7739_C0_0A5_SRC6_SYNCDOMAIN_SELECT           0x00A5
#define  AK7739_C0_0A6_SRC7_SYNCDOMAIN_SELECT           0x00A6
#define  AK7739_C0_0A7_SRC8_SYNCDOMAIN_SELECT           0x00A7
#define  AK7739_C0_0B0_DSP1_SYNCDOMAIN_SELECT           0x00B0
#define  AK7739_C0_0B1_DSP2_SYNCDOMAIN_SELECT           0x00B1
#define  AK7739_C0_0C0_DSP1_OUTPUT_SYNCDOMAIN_SELECT1   0x00C0
#define  AK7739_C0_0C1_DSP1_OUTPUT_SYNCDOMAIN_SELECT2   0x00C1
#define  AK7739_C0_0C2_DSP1_OUTPUT_SYNCDOMAIN_SELECT3   0x00C2
#define  AK7739_C0_0C3_DSP1_OUTPUT_SYNCDOMAIN_SELECT4   0x00C3
#define  AK7739_C0_0C4_DSP1_OUTPUT_SYNCDOMAIN_SELECT5   0x00C4
#define  AK7739_C0_0C5_DSP1_OUTPUT_SYNCDOMAIN_SELECT6   0x00C5
#define  AK7739_C0_0C6_DSP2_OUTPUT_SYNCDOMAIN_SELECT1   0x00C6
#define  AK7739_C0_0C7_DSP2_OUTPUT_SYNCDOMAIN_SELECT2   0x00C7
#define  AK7739_C0_0C8_DSP2_OUTPUT_SYNCDOMAIN_SELECT3   0x00C8
#define  AK7739_C0_0C9_DSP2_OUTPUT_SYNCDOMAIN_SELECT4   0x00C9
#define  AK7739_C0_0CA_DSP2_OUTPUT_SYNCDOMAIN_SELECT5   0x00CA
#define  AK7739_C0_0CB_DSP2_OUTPUT_SYNCDOMAIN_SELECT6   0x00CB
#define  AK7739_C0_106_DIT_INPUT_DATA_SELECT            0x0106
#define  AK7739_C0_108_DAC1_INPUT_DATA_SELECT           0x0108
#define  AK7739_C0_109_DAC2_INPUT_DATA_SELECT           0x0109
#define  AK7739_C0_118_MIXER1_CHA_INPUT_DATA_SELECT     0x0118
#define  AK7739_C0_119_MIXER1_CHB_INPUT_DATA_SELECT     0x0119
#define  AK7739_C0_11A_MIXER2_CHA_INPUT_DATA_SELECT     0x011A
#define  AK7739_C0_11B_MIXER2_CHB_INPUT_DATA_SELECT     0x011B
#define  AK7739_C0_120_SRC1_INPUT_DATA_SELECT           0x0120
#define  AK7739_C0_121_SRC2_INPUT_DATA_SELECT           0x0121
#define  AK7739_C0_122_SRC3_INPUT_DATA_SELECT           0x0122
#define  AK7739_C0_123_SRC4_INPUT_DATA_SELECT           0x0123
#define  AK7739_C0_124_SRC5_INPUT_DATA_SELECT           0x0124
#define  AK7739_C0_125_SRC6_INPUT_DATA_SELECT           0x0125
#define  AK7739_C0_126_SRC7_INPUT_DATA_SELECT           0x0126
#define  AK7739_C0_127_SRC8_INPUT_DATA_SELECT           0x0127
#define  AK7739_C0_140_SDOUT1A_OUTPUT_DATA_SELECT       0x0140
#define  AK7739_C0_141_SDOUT1B_OUTPUT_DATA_SELECT       0x0141
#define  AK7739_C0_142_SDOUT1C_OUTPUT_DATA_SELECT       0x0142
#define  AK7739_C0_143_SDOUT1D_OUTPUT_DATA_SELECT       0x0143
#define  AK7739_C0_144_SDOUT1E_OUTPUT_DATA_SELECT       0x0144
#define  AK7739_C0_145_SDOUT1F_OUTPUT_DATA_SELECT       0x0145
#define  AK7739_C0_146_SDOUT1G_OUTPUT_DATA_SELECT       0x0146
#define  AK7739_C0_147_SDOUT1H_OUTPUT_DATA_SELECT       0x0147
#define  AK7739_C0_148_SDOUT2A_OUTPUT_DATA_SELECT       0x0148
#define  AK7739_C0_149_SDOUT2B_OUTPUT_DATA_SELECT       0x0149
#define  AK7739_C0_14A_SDOUT2C_OUTPUT_DATA_SELECT       0x014A
#define  AK7739_C0_14B_SDOUT2D_OUTPUT_DATA_SELECT       0x014B
#define  AK7739_C0_14C_SDOUT2E_OUTPUT_DATA_SELECT       0x014C
#define  AK7739_C0_14D_SDOUT2F_OUTPUT_DATA_SELECT       0x014D
#define  AK7739_C0_14E_SDOUT2G_OUTPUT_DATA_SELECT       0x014E
#define  AK7739_C0_14F_SDOUT2H_OUTPUT_DATA_SELECT       0x014F
#define  AK7739_C0_150_SDOUT3A_OUTPUT_DATA_SELECT       0x0150
#define  AK7739_C0_151_SDOUT3B_OUTPUT_DATA_SELECT       0x0151
#define  AK7739_C0_152_SDOUT3C_OUTPUT_DATA_SELECT       0x0152
#define  AK7739_C0_153_SDOUT3D_OUTPUT_DATA_SELECT       0x0153
#define  AK7739_C0_154_SDOUT3E_OUTPUT_DATA_SELECT       0x0154
#define  AK7739_C0_155_SDOUT3F_OUTPUT_DATA_SELECT       0x0155
#define  AK7739_C0_156_SDOUT3G_OUTPUT_DATA_SELECT       0x0156
#define  AK7739_C0_157_SDOUT3H_OUTPUT_DATA_SELECT       0x0157
#define  AK7739_C0_158_SDOUT4A_OUTPUT_DATA_SELECT       0x0158
#define  AK7739_C0_159_SDOUT4B_OUTPUT_DATA_SELECT       0x0159
#define  AK7739_C0_15A_SDOUT4C_OUTPUT_DATA_SELECT       0x015A
#define  AK7739_C0_15B_SDOUT4D_OUTPUT_DATA_SELECT       0x015B
#define  AK7739_C0_15C_SDOUT4E_OUTPUT_DATA_SELECT       0x015C
#define  AK7739_C0_15D_SDOUT4F_OUTPUT_DATA_SELECT       0x015D
#define  AK7739_C0_15E_SDOUT4G_OUTPUT_DATA_SELECT       0x015E
#define  AK7739_C0_15F_SDOUT4H_OUTPUT_DATA_SELECT       0x015F
#define  AK7739_C0_160_SDOUT5A_OUTPUT_DATA_SELECT       0x0160
#define  AK7739_C0_161_SDOUT5B_OUTPUT_DATA_SELECT       0x0161
#define  AK7739_C0_162_SDOUT5C_OUTPUT_DATA_SELECT       0x0162
#define  AK7739_C0_163_SDOUT5D_OUTPUT_DATA_SELECT       0x0163
#define  AK7739_C0_164_SDOUT5E_OUTPUT_DATA_SELECT       0x0164
#define  AK7739_C0_165_SDOUT5F_OUTPUT_DATA_SELECT       0x0165
#define  AK7739_C0_166_SDOUT5G_OUTPUT_DATA_SELECT       0x0166
#define  AK7739_C0_167_SDOUT5H_OUTPUT_DATA_SELECT       0x0167
#define  AK7739_C0_168_SDOUT6A_OUTPUT_DATA_SELECT       0x0168
#define  AK7739_C0_169_SDOUT6B_OUTPUT_DATA_SELECT       0x0169
#define  AK7739_C0_16A_SDOUT6C_OUTPUT_DATA_SELECT       0x016A
#define  AK7739_C0_16B_SDOUT6D_OUTPUT_DATA_SELECT       0x016B
#define  AK7739_C0_16C_SDOUT6E_OUTPUT_DATA_SELECT       0x016C
#define  AK7739_C0_16D_SDOUT6F_OUTPUT_DATA_SELECT       0x016D
#define  AK7739_C0_16E_SDOUT6G_OUTPUT_DATA_SELECT       0x016E
#define  AK7739_C0_16F_SDOUT6H_OUTPUT_DATA_SELECT       0x016F
#define  AK7739_C0_180_DSP1_DIN1_INPUT_DATA_SELECT      0x0180
#define  AK7739_C0_181_DSP1_DIN2_INPUT_DATA_SELECT      0x0181
#define  AK7739_C0_182_DSP1_DIN3_INPUT_DATA_SELECT      0x0182
#define  AK7739_C0_183_DSP1_DIN4_INPUT_DATA_SELECT      0x0183
#define  AK7739_C0_184_DSP1_DIN5_INPUT_DATA_SELECT      0x0184
#define  AK7739_C0_185_DSP1_DIN6_INPUT_DATA_SELECT      0x0185
#define  AK7739_C0_186_DSP2_DIN1_INPUT_DATA_SELECT      0x0186
#define  AK7739_C0_187_DSP2_DIN2_INPUT_DATA_SELECT      0x0187
#define  AK7739_C0_188_DSP2_DIN3_INPUT_DATA_SELECT      0x0188
#define  AK7739_C0_189_DSP2_DIN4_INPUT_DATA_SELECT      0x0189
#define  AK7739_C0_18A_DSP2_DIN5_INPUT_DATA_SELECT      0x018A
#define  AK7739_C0_18B_DSP2_DIN6_INPUT_DATA_SELECT      0x018B
#define  AK7739_C0_200_SDOUT_OUTPUT_DATA_SELECT         0x0200
#define  AK7739_C0_201_SDOUT_ENABLE_SETTING             0x0201
#define  AK7739_C0_202_SDOUT_OUTPUT_MODE_SETTING        0x0202
#define  AK7739_C0_203_SDIN_INPUT_DATA_SELECT           0x0203
#define  AK7739_C0_204_MASTER_SPI_SELECT                0x0204
#define  AK7739_C0_205_STO_FLAG_SETTING                 0x0205
#define  AK7739_C0_206_LRCK4_5_OUTPUT_DATA_SELECT       0x0206
#define  AK7739_C0_210_MASTER_SPI_TX00                  0x0210
#define  AK7739_C0_211_MASTER_SPI_TX01                  0x0211
#define  AK7739_C0_212_MASTER_SPI_TX02                  0x0212
#define  AK7739_C0_213_MASTER_SPI_TX03                  0x0213
#define  AK7739_C0_214_MASTER_SPI_TX04                  0x0214
#define  AK7739_C0_215_MASTER_SPI_TX05                  0x0215
#define  AK7739_C0_216_MASTER_SPI_TX06                  0x0216
#define  AK7739_C0_217_MASTER_SPI_TX07                  0x0217
#define  AK7739_C0_218_MASTER_SPI_TX08                  0x0218
#define  AK7739_C0_219_MASTER_SPI_TX09                  0x0219
#define  AK7739_C0_21A_MASTER_SPI_TX10                  0x021A
#define  AK7739_C0_21B_MASTER_SPI_TX11                  0x021B
#define  AK7739_C0_21C_MASTER_SPI_TX12                  0x021C
#define  AK7739_C0_21D_MASTER_SPI_TX13                  0x021D
#define  AK7739_C0_21E_MASTER_SPI_TX14                  0x021E
#define  AK7739_C0_21F_MASTER_SPI_TX15                  0x021F
#define  AK7739_C0_220_MASTER_SPI_TX16                  0x0220
#define  AK7739_C0_221_MASTER_SPI_TX17                  0x0221
#define  AK7739_C0_222_MASTER_SPI_TX18                  0x0222
#define  AK7739_C0_223_MASTER_SPI_TX19                  0x0223
#define  AK7739_C0_224_MASTER_SPI_TX20                  0x0224
#define  AK7739_C0_225_MASTER_SPI_TX21                  0x0225
#define  AK7739_C0_226_MASTER_SPI_TX22                  0x0226
#define  AK7739_C0_227_MASTER_SPI_TX23                  0x0227
#define  AK7739_C0_228_MASTER_SPI_TX24                  0x0228
#define  AK7739_C0_229_MASTER_SPI_TX25                  0x0229
#define  AK7739_C0_22A_MASTER_SPI_TX26                  0x022A
#define  AK7739_C0_22B_MASTER_SPI_TX27                  0x022B
#define  AK7739_C0_22C_MASTER_SPI_TX28                  0x022C
#define  AK7739_C0_22D_MASTER_SPI_TX29                  0x022D
#define  AK7739_C0_22E_MASTER_SPI_TX30                  0x022E
#define  AK7739_C0_22F_MASTER_SPI_TX31                  0x022F
#define  AK7739_C0_300_DEVICE_ID                        0x0300
#define  AK7739_C0_301_REVISION_NUM                     0x0301
#define  AK7739_C0_302_CRC_ERROR_STATUS                 0x0302
#define  AK7739_C0_303_STO_READ_OUT                     0x0303
#define  AK7739_C0_30F_MASTER_SPI_STATUS                0x030F
#define  AK7739_C0_310_MASTER_SPI_RX00                  0x0310
#define  AK7739_C0_311_MASTER_SPI_RX01                  0x0311
#define  AK7739_C0_312_MASTER_SPI_RX02                  0x0312
#define  AK7739_C0_313_MASTER_SPI_RX03                  0x0313
#define  AK7739_C0_314_MASTER_SPI_RX04                  0x0314
#define  AK7739_C0_315_MASTER_SPI_RX05                  0x0315
#define  AK7739_C0_316_MASTER_SPI_RX06                  0x0316
#define  AK7739_C0_317_MASTER_SPI_RX07                  0x0317
#define  AK7739_C0_318_MASTER_SPI_RX08                  0x0318
#define  AK7739_C0_319_MASTER_SPI_RX09                  0x0319
#define  AK7739_C0_31A_MASTER_SPI_RX10                  0x031A
#define  AK7739_C0_31B_MASTER_SPI_RX11                  0x031B
#define  AK7739_C0_31C_MASTER_SPI_RX12                  0x031C
#define  AK7739_C0_31D_MASTER_SPI_RX13                  0x031D
#define  AK7739_C0_31E_MASTER_SPI_RX14                  0x031E
#define  AK7739_C0_31F_MASTER_SPI_RX15                  0x031F
#define  AK7739_C0_320_MASTER_SPI_RX16                  0x0320
#define  AK7739_C0_321_MASTER_SPI_RX17                  0x0321
#define  AK7739_C0_322_MASTER_SPI_RX18                  0x0322
#define  AK7739_C0_323_MASTER_SPI_RX19                  0x0323
#define  AK7739_C0_324_MASTER_SPI_RX20                  0x0324
#define  AK7739_C0_325_MASTER_SPI_RX21                  0x0325
#define  AK7739_C0_326_MASTER_SPI_RX22                  0x0326
#define  AK7739_C0_327_MASTER_SPI_RX23                  0x0327
#define  AK7739_C0_328_MASTER_SPI_RX24                  0x0328
#define  AK7739_C0_329_MASTER_SPI_RX25                  0x0329
#define  AK7739_C0_32A_MASTER_SPI_RX26                  0x032A
#define  AK7739_C0_32B_MASTER_SPI_RX27                  0x032B
#define  AK7739_C0_32C_MASTER_SPI_RX28                  0x032C
#define  AK7739_C0_32D_MASTER_SPI_RX29                  0x032D
#define  AK7739_C0_32E_MASTER_SPI_RX30                  0x032E
#define  AK7739_C0_32F_MASTER_SPI_RX31                  0x032F

/* DSP block */
#define  AK7739_C1_000_DSP_RESET_CONTROL                0x1000
#define  AK7739_C1_001_DSP_CLOCK_SETTING                0x1001
#define  AK7739_C1_002_RAM_CLEAR_SETTING                0x1002
#define  AK7739_C1_003_DSP_WATCHDOG_TIMER_FLAG_SETTING  0x1003
#define  AK7739_C1_004_DSP_GPO_SETTING                  0x1004
#define  AK7739_C1_005_DSP_GPO_STATUS                   0x1005
#define  AK7739_C1_008_DSP_WATCHDOG_TIMER_ERROR_STATUS  0x1008
#define  AK7739_C1_011_DSP1_DRAM_SETTING                0x1011
#define  AK7739_C1_012_DSP2_DRAM_SETTING                0x1012
#define  AK7739_C1_013_DSP3_DRAM_SETTING                0x1013
#define  AK7739_C1_020_DSP_DLYRAM_ASSIGNMENT            0x1020
#define  AK7739_C1_021_DSP1_DLYRAM_SETTING              0x1021
#define  AK7739_C1_022_DSP2_DLYRAM_SETTING              0x1022
#define  AK7739_C1_031_DSP1_CRAM_DLP0_SETTING           0x1031
#define  AK7739_C1_032_DSP2_CRAM_DLP0_SETTING           0x1032
#define  AK7739_C1_033_DSP3_CRAM_SETTING                0x1033
#define  AK7739_C1_040_DSP1_JX_SETTING                  0x1040
#define  AK7739_C1_041_DSP2_JX_SETTING                  0x1041
#define  AK7739_C1_042_DSP3_JX_SETTING                  0x1042

/* SRC block */
#define  AK7739_C2_000_SRC_POWER_MANAGEMENT             0x2000
#define  AK7739_C2_001_SRC_FILTER_SETTING1              0x2001
#define  AK7739_C2_003_SRC_PHASE_GROUP1                 0x2003
#define  AK7739_C2_005_SRC_MUTE_SETTING1                0x2005
#define  AK7739_C2_006_SRC_MUTE_SETTING2                0x2006
#define  AK7739_C2_007_SRC_STO_FLAG_SETTING             0x2007
#define  AK7739_C2_010_SRC_STATUS1                      0x2010
#define  AK7739_C2_101_MONO_SRC_POWER_MANAGEMENT        0x2101
#define  AK7739_C2_102_MONO_SRC_FILTER_SETTING          0x2102
#define  AK7739_C2_103_MONO_SRC_PHASE_GROUP             0x2103
#define  AK7739_C2_104_MONO_SRC_MUTE_SETTING            0x2104
#define  AK7739_C2_105_MONO_SRC_STO_FLAG_SETTING        0x2105
#define  AK7739_C2_106_MONO_SRC_PATH_SETTING            0x2106
#define  AK7739_C2_110_MONO_SRC_STATUS1                 0x2110
#define  AK7739_C2_200_DIT_POWER_MANAGEMENT             0x2200
#define  AK7739_C2_201_DIT_STATUS_BIT1                  0x2201
#define  AK7739_C2_202_DIT_STATUS_BIT2                  0x2202
#define  AK7739_C2_203_DIT_STATUS_BIT3                  0x2203
#define  AK7739_C2_204_DIT_STATUS_BIT4                  0x2204
#define  AK7739_C2_210_MIXER1_SETTING                   0x2210
#define  AK7739_C2_211_MIXER2_SETTING                   0x2211

/* CODEC block */
#define  AK7739_C3_000_POWER_MANAGEMENT                 0x3000
#define  AK7739_C3_001_MICBIAS_POWER_MANAGEMENT         0x3001
#define  AK7739_C3_002_RESET_CONTROL                    0x3002
#define  AK7739_C3_003_SYSTEM_CLOCK_SETTING             0x3003
#define  AK7739_C3_004_MIC_AMP_GAIN                     0x3004
#define  AK7739_C3_005_ADC1_LCH_DIGITAL_VOLUME          0x3005
#define  AK7739_C3_006_ADC1_RCH_DIGITAL_VOLUME          0x3006
#define  AK7739_C3_007_ADC2_LCH_DIGITAL_VOLUME          0x3007
#define  AK7739_C3_008_ADC2_RCH_DIGITAL_VOLUME          0x3008
#define  AK7739_C3_009_ADC_DIGITAL_FILTER_SETTING       0x3009
#define  AK7739_C3_00A_ADC_ANALOG_INPUT_SETTING         0x300A
#define  AK7739_C3_00B_ADC_MUTE_HPF_CONTROL             0x300B
#define  AK7739_C3_00C_DAC1_LCH_DIGITAL_VOLUME          0x300C
#define  AK7739_C3_00D_DAC1_RCH_DIGITAL_VOLUME          0x300D
#define  AK7739_C3_00E_DAC2_LCH_DIGITAL_VOLUME          0x300E
#define  AK7739_C3_00F_DAC2_RCH_DIGITAL_VOLUME          0x300F
#define  AK7739_C3_013_DAC_DEEMPHASIS_SETTING           0x3013
#define  AK7739_C3_014_DAC_MUTE_FILTER_SETTING          0x3014
#define  AK7739_C3_015_DIGITAL_MIC_CONTROL              0x3015

#define  AK7739_VIRT_C3_016_DSP1OUT1_MIX                0x3016
#define  AK7739_VIRT_C3_017_DSP1OUT2_MIX                0x3017
#define  AK7739_VIRT_C3_018_DSP1OUT3_MIX                0x3018
#define  AK7739_VIRT_C3_019_DSP1OUT4_MIX                0x3019
#define  AK7739_VIRT_C3_01A_DSP1OUT5_MIX                0x301A
#define  AK7739_VIRT_C3_01B_DSP1OUT6_MIX                0x301B
#define  AK7739_VIRT_C3_01C_DSP2OUT1_MIX                0x301C
#define  AK7739_VIRT_C3_01D_DSP2OUT2_MIX                0x301D
#define  AK7739_VIRT_C3_01E_DSP2OUT3_MIX                0x301E
#define  AK7739_VIRT_C3_01F_DSP2OUT4_MIX                0x301F
#define  AK7739_VIRT_C3_020_DSP2OUT5_MIX                0x3020
#define  AK7739_VIRT_C3_021_DSP2OUT6_MIX                0x3021

#define AK7739_MAX_REGISTER         AK7739_VIRT_C3_021_DSP2OUT6_MIX
#define AK7739_VIRT_REGISTER        AK7739_VIRT_C3_016_DSP1OUT1_MIX

/* Bitfield Definitions */


/* AK7739_C2_SERIAL_DATA_FORMAT (0xC2) Fields */
/* LRCK I/F Format */
#define AK7739_LRIF_I2S_MODE		0
#define AK7739_LRIF_MSB_MODE		5
#define AK7739_LRIF_LSB_MODE		5

#ifndef AK7739_PCM_BCKP
#define AK7739_LRIF_PCM_SHORT_MODE	0xE
#define AK7739_LRIF_PCM_LONG_MODE	0xF
#else
#define AK7739_LRIF_PCM_SHORT_MODE	6
#define AK7739_LRIF_PCM_LONG_MODE	7
#endif


/* Input Format is set as "MSB(24- bit)" by following register.
   CONT03(DIF2,DOF2), CONT06(DIFDA, DIF1), CONT07(DOF4,DOF3,DOF1) */

/* AK7739_CA_CLK_SDOUT_SETTING (0xCA) Fields */

#define MAX_LOOP_TIMES		3

#define COMMAND_WRITE_HUBREG		0xC0
#define COMMAND_READ_HUBREG			0x40
#define COMMAND_WRITE_DSPREG		0xC1
#define COMMAND_READ_DSPREG			0x41
#define COMMAND_WRITE_SRCREG		0xC2
#define COMMAND_READ_SRCREG			0x42
#define COMMAND_WRITE_CODECREG		0xC3
#define COMMAND_READ_CODECREG		0x43

#define COMMAND_CRC_READ			0x72
#define COMMAND_MIR_READ			0x76

#define COMMAND_WRITE_CRAM_RUN		0x80
#define COMMAND_WRITE_CRAM_EXEC     0xA4

#define COMMAND_WRITE_CRAM          0xB4
#define COMMAND_WRITE_PRAM          0xB8

#define COMMAND_WRITE_OFREG         0xB2

#define	AK7739_CRAM1_MAX_ADDRESS	6143
#define	AK7739_CRAM2_MAX_ADDRESS	6143
#define	AK7739_CRAM3_MAX_ADDRESS	4095

#define	TOTAL_NUM_OF_PRAM1_MAX		51203
#define	TOTAL_NUM_OF_PRAM2_MAX		40963
#define	TOTAL_NUM_OF_PRAM3_MAX		10243
#define	TOTAL_NUM_OF_CRAM1_MAX		24579
#define	TOTAL_NUM_OF_CRAM2_MAX		24579
#define	TOTAL_NUM_OF_CRAM3_MAX		16387
#define	TOTAL_NUM_OF_OFREG1_MAX     259
#define	TOTAL_NUM_OF_OFREG2_MAX     259


static const char *ak7739_firmware_pram[] =
{
    "Off",
    "basic1",
    "basic2",
    "dsp1_data1",
    "dsp1_data2",
    "dsp1_data3",
    "dsp1_data4",
    "dsp1_data5",
    "dsp2_data1",
    "dsp2_data2",
    "dsp2_data3",
    "dsp2_data4",
    "dsp2_data5",
    "dsp3_data1",
    "dsp3_data2",
    "dsp3_data3",
    "dsp3_data4",
    "dsp3_data5",

};

static const char *ak7739_firmware_cram[] =
{
    "Off",
    "basic1",
    "basic2",
    "dsp1_data1",
    "dsp1_data2",
    "dsp1_data3",
    "dsp1_data4",
    "dsp1_data5",
    "dsp2_data1",
    "dsp2_data2",
    "dsp2_data3",
    "dsp2_data4",
    "dsp2_data5",
    "dsp3_data1",
    "dsp3_data2",
    "dsp3_data3",
    "dsp3_data4",
    "dsp3_data5",
};

static const char *ak7739_firmware_ofreg[] =
{
    "Off",
    "basic1",
    "basic2",
    "dsp1_data1",
    "dsp1_data2",
    "dsp1_data3",
    "dsp1_data4",
    "dsp1_data5",
    "dsp2_data1",
    "dsp2_data2",
    "dsp2_data3",
    "dsp2_data4",
    "dsp2_data5",
};


enum {
	AIF_PORT1 = 0,
	AIF_PORT2,
	AIF_PORT3,
	AIF_PORT4,
	AIF_PORT5,
	NUM_AIF_DAIS,
};

#define RAMTYPE_MAX  6
#define MAX_DSP_NAME_SIZE  128
#define SOC_DAPM_VALUE_ENUM SOC_DAPM_ENUM

enum snd_soc_control_type {
	SND_SOC_SPI,
	SND_SOC_I2C
};

enum ak7739_ram_type {
	RAMTYPE_PRAM = 0,
	RAMTYPE_CRAM,
	RAMTYPE_OFREG,
};

enum ak7739_status {
	POWERDOWN = 0,
	SUSPEND,
	STANDBY,
	DOWNLOAD,
	DOWNLOAD_FINISH,
	RUN,
};

enum ak7739_EQ {
    NONE = 0,
    Rocks,
    Pops,
    Jazz,
    Vocal,
    Power,
};

#endif

